// This file is created by egg-ts-helper@1.35.2
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
import ExportAdmin = require('../../../app/controller/admin');
import ExportAkshare = require('../../../app/controller/akshare');
import ExportAlert = require('../../../app/controller/alert');
import ExportAlertHistory = require('../../../app/controller/alert_history');
import ExportAlltick = require('../../../app/controller/alltick');
import ExportAlphavantage = require('../../../app/controller/alphavantage');
import ExportApiDocs = require('../../../app/controller/api_docs');
import ExportAuth = require('../../../app/controller/auth');
import ExportBacktest = require('../../../app/controller/backtest');
import ExportCache = require('../../../app/controller/cache');
import ExportCacheManagement = require('../../../app/controller/cacheManagement');
import ExportCacheStats = require('../../../app/controller/cacheStats');
import ExportCoinRecharge = require('../../../app/controller/coinRecharge');
import ExportCoins = require('../../../app/controller/coins');
import ExportData = require('../../../app/controller/data');
import ExportDatabaseHealth = require('../../../app/controller/database_health');
import ExportDataQualityManagement = require('../../../app/controller/dataQualityManagement');
import ExportDataSource = require('../../../app/controller/dataSource');
import ExportDataSourceManagement = require('../../../app/controller/dataSourceManagement');
import ExportDojiPattern = require('../../../app/controller/dojiPattern');
import ExportDojiAlertHistory = require('../../../app/controller/doji_alert_history');
import ExportEastmoney = require('../../../app/controller/eastmoney');
import ExportEnv = require('../../../app/controller/env');
import ExportFactor = require('../../../app/controller/factor');
import ExportFundamental = require('../../../app/controller/fundamental');
import ExportGoogleFinance = require('../../../app/controller/googleFinance');
import ExportGraphql = require('../../../app/controller/graphql');
import ExportHealth = require('../../../app/controller/health');
import ExportHome = require('../../../app/controller/home');
import ExportJuhe = require('../../../app/controller/juhe');
import ExportLimitlist = require('../../../app/controller/limitlist');
import ExportLogs = require('../../../app/controller/logs');
import ExportMembership = require('../../../app/controller/membership');
import ExportMoneyflow = require('../../../app/controller/moneyflow');
import ExportMonitoring = require('../../../app/controller/monitoring');
import ExportNetease = require('../../../app/controller/netease');
import ExportNotification = require('../../../app/controller/notification');
import ExportPage = require('../../../app/controller/page');
import ExportPageGroup = require('../../../app/controller/page_group');
import ExportPageStats = require('../../../app/controller/page_stats');
import ExportPermissionTemplate = require('../../../app/controller/permission_template');
import ExportPortfolio = require('../../../app/controller/portfolio');
import ExportRiskAlert = require('../../../app/controller/riskAlert');
import ExportRiskMonitoring = require('../../../app/controller/riskMonitoring');
import ExportSecurity = require('../../../app/controller/security');
import ExportSimulation = require('../../../app/controller/simulation');
import ExportSina = require('../../../app/controller/sina');
import ExportSmartRecommendation = require('../../../app/controller/smartRecommendation');
import ExportStock = require('../../../app/controller/stock');
import ExportStopLossManager = require('../../../app/controller/stopLossManager');
import ExportStrategy = require('../../../app/controller/strategy');
import ExportStressTesting = require('../../../app/controller/stressTesting');
import ExportTechnicalIndicators = require('../../../app/controller/technicalIndicators');
import ExportTencent = require('../../../app/controller/tencent');
import ExportTushare = require('../../../app/controller/tushare');
import ExportUser = require('../../../app/controller/user');
import ExportWatchlist = require('../../../app/controller/watchlist');
import ExportYahooFinance = require('../../../app/controller/yahooFinance');
import ExportZhitu = require('../../../app/controller/zhitu');

declare module 'egg' {
  interface IController {
    admin: ExportAdmin;
    akshare: ExportAkshare;
    alert: ExportAlert;
    alertHistory: ExportAlertHistory;
    alltick: ExportAlltick;
    alphavantage: ExportAlphavantage;
    apiDocs: ExportApiDocs;
    auth: ExportAuth;
    backtest: ExportBacktest;
    cache: ExportCache;
    cacheManagement: ExportCacheManagement;
    cacheStats: ExportCacheStats;
    coinRecharge: ExportCoinRecharge;
    coins: ExportCoins;
    data: ExportData;
    databaseHealth: ExportDatabaseHealth;
    dataQualityManagement: ExportDataQualityManagement;
    dataSource: ExportDataSource;
    dataSourceManagement: ExportDataSourceManagement;
    dojiPattern: ExportDojiPattern;
    dojiAlertHistory: ExportDojiAlertHistory;
    eastmoney: ExportEastmoney;
    env: ExportEnv;
    factor: ExportFactor;
    fundamental: ExportFundamental;
    googleFinance: ExportGoogleFinance;
    graphql: ExportGraphql;
    health: ExportHealth;
    home: ExportHome;
    juhe: ExportJuhe;
    limitlist: ExportLimitlist;
    logs: ExportLogs;
    membership: ExportMembership;
    moneyflow: ExportMoneyflow;
    monitoring: ExportMonitoring;
    netease: ExportNetease;
    notification: ExportNotification;
    page: ExportPage;
    pageGroup: ExportPageGroup;
    pageStats: ExportPageStats;
    permissionTemplate: ExportPermissionTemplate;
    portfolio: ExportPortfolio;
    riskAlert: ExportRiskAlert;
    riskMonitoring: ExportRiskMonitoring;
    security: ExportSecurity;
    simulation: ExportSimulation;
    sina: ExportSina;
    smartRecommendation: ExportSmartRecommendation;
    stock: ExportStock;
    stopLossManager: ExportStopLossManager;
    strategy: ExportStrategy;
    stressTesting: ExportStressTesting;
    technicalIndicators: ExportTechnicalIndicators;
    tencent: ExportTencent;
    tushare: ExportTushare;
    user: ExportUser;
    watchlist: ExportWatchlist;
    yahooFinance: ExportYahooFinance;
    zhitu: ExportZhitu;
  }
}
