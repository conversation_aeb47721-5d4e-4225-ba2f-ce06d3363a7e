# 数据源API配置示例
# 复制此文件为 .env 并填入真实的API密钥

# ================================
# 智兔数服API配置
# ================================
# 获取地址: https://www.zhitudata.com/
ZHITU_API_KEY=D564FC55-057B-4F6F-932C-C115E78BFAE4

# ================================
# Alpha Vantage API配置
# ================================
# 获取地址: https://www.alphavantage.co/support/#api-key
ALPHA_VANTAGE_API_KEY=f6235795d0b5310a44d87a6a41cd9dfc-c-app

# ================================
# 聚合数据API配置
# ================================
# 获取地址: https://www.juhe.cn/
JUHE_API_KEY=4191aa94e0f3ba88c66b827fbbe56624

# ================================
# 数据库配置
# ================================
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=root
MYSQL_DATABASE=stock_analysis

# ================================
# Redis配置
# ================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ================================
# 服务器配置
# ================================
PORT=7001
NODE_ENV=development

# ================================
# JWT配置
# ================================
JWT_SECRET=your_jwt_secret_here

# ================================
# 其他配置
# ================================
# API请求超时时间(毫秒)
API_REQUEST_TIMEOUT=15000

# API重试次数
API_RETRY_COUNT=3

# 调试模式
DEBUG_DATA_SOURCES=false
DEBUG_API_CALLS=false
