name: Security Scan

on:
  schedule:
    - cron: '0 0 * * 0' # Run every Sunday at midnight
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch: # Allow manual triggering

jobs:
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run npm audit
      run: npm audit --production --audit-level=high
      continue-on-error: true
    
    - name: Run Snyk to check for vulnerabilities
      uses: snyk/actions/node@master
      continue-on-error: true
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high
    
    - name: Run CodeQL Analysis
      uses: github/codeql-action/analyze@v2
      with:
        languages: javascript, typescript
    
    - name: Generate security report
      run: |
        echo "# Security Scan Report" > security-report.md
        echo "## npm audit" >> security-report.md
        npm audit --json | jq -r '.advisories | to_entries | .[] | .value | "- " + .title + " (severity: " + .severity + ")"' >> security-report.md || echo "No vulnerabilities found" >> security-report.md
        
        echo "## Snyk" >> security-report.md
        if [ -f "snyk-result.json" ]; then
          jq -r '.vulnerabilities[] | "- " + .title + " (severity: " + .severity + ")"' snyk-result.json >> security-report.md || echo "No vulnerabilities found" >> security-report.md
        else
          echo "No Snyk results found" >> security-report.md
        fi
    
    - name: Upload security report
      uses: actions/upload-artifact@v3
      with:
        name: security-report
        path: security-report.md
    
    - name: Send security report
      if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
      uses: rtCamp/action-slack-notify@v2
      env:
        SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        SLACK_CHANNEL: security
        SLACK_COLOR: warning
        SLACK_TITLE: Security Scan Report
        SLACK_MESSAGE: 'Weekly security scan completed. Check the attached report for details.'
        SLACK_FOOTER: 'Stock Analysis Web'