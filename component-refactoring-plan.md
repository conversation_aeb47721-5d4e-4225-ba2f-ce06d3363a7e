# 组件结构重构计划

## 问题分析

通过对当前代码库的分析，我们发现以下问题：

1. **组件结构不一致**：一些组件直接放在 `components` 目录下，而其他组件则按功能分类在子目录中
2. **组件职责不清晰**：部分组件承担了过多的责任，违反了单一职责原则
3. **组件复用性低**：许多组件包含特定业务逻辑，难以在不同场景中复用
4. **组件接口不统一**：不同组件使用不同的接口约定，增加了使用难度
5. **冗余组件**：存在功能重复或相似的组件
6. **测试组件混杂**：测试或演示组件与生产组件混在一起

## 重构目标

1. **建立清晰的组件层次结构**：按照功能和复用性对组件进行分类
2. **实现组件职责单一**：每个组件只负责一个明确的功能
3. **提高组件复用性**：将通用逻辑抽离为可复用组件
4. **统一组件接口**：为相似组件定义一致的接口约定
5. **移除冗余组件**：合并功能相似的组件，移除不必要的组件
6. **分离测试组件**：将测试或演示组件移至专门的目录

## 组件分类方案

我们将组件分为以下几类：

### 1. 基础组件 (Base Components)

纯展示型组件，不包含业务逻辑，高度可复用。

- 按钮、输入框、选择器、模态框等
- 位置：`src/components/base/`

### 2. 通用组件 (Common Components)

包含一定逻辑但与具体业务无关的组件。

- 表格、表单、分页器、上传组件等
- 位置：`src/components/common/`

### 3. 功能组件 (Feature Components)

特定功能的组件，可能包含业务逻辑但可在多个视图中复用。

- 股票搜索、图表、提醒设置等
- 位置：`src/components/features/`

### 4. 布局组件 (Layout Components)

用于页面布局的组件。

- 页头、页脚、侧边栏、主布局等
- 位置：`src/components/layout/`

### 5. 视图组件 (View Components)

特定页面的组件，通常由其他组件组合而成。

- 仪表盘卡片、分析面板等
- 位置：`src/components/views/`

## 组件接口规范

### Props 命名规范

- 使用 camelCase 命名
- 布尔类型 props 使用 `is` 或 `has` 前缀
- 事件处理 props 使用 `on` 前缀

### 事件命名规范

- 使用 kebab-case 命名
- 表示状态变化的事件使用 `update:propName` 格式
- 表示用户操作的事件使用动词，如 `click`、`submit`

### 插槽命名规范

- 使用 kebab-case 命名
- 默认插槽不命名
- 具名插槽使用描述性名称，如 `header`、`footer`

## 重构步骤

### 1. 基础组件重构

1. 创建基础组件目录结构
2. 实现核心基础组件
3. 将现有组件中的基础功能抽离

### 2. 通用组件重构

1. 识别并重构通用组件
2. 统一通用组件接口
3. 添加适当的文档和类型定义

### 3. 功能组件重构

1. 将业务逻辑与展示逻辑分离
2. 重构现有功能组件
3. 合并功能相似的组件

### 4. 布局组件重构

1. 识别并重构布局组件
2. 确保布局组件的响应式设计

### 5. 视图组件重构

1. 将页面特定组件移至视图组件目录
2. 确保视图组件只组合其他组件而不包含复杂逻辑

### 6. 移除冗余组件

1. 识别并标记冗余组件
2. 将功能合并到其他组件
3. 移除不必要的组件

## 具体实施计划

### 第一阶段：组件结构调整

1. 创建新的目录结构
2. 移动现有组件到适当的目录
3. 更新导入路径

### 第二阶段：组件接口统一

1. 审查并统一组件 props
2. 审查并统一组件事件
3. 审查并统一组件插槽

### 第三阶段：组件优化

1. 拆分复杂组件
2. 合并相似组件
3. 提取可复用逻辑

### 第四阶段：文档和测试

1. 为组件添加文档注释
2. 更新组件使用示例
3. 确保组件测试覆盖

## 需要重点关注的组件

1. **StockSearch 和 StockSearchInput**：合并为单一组件
2. **图表组件**：提取通用图表逻辑，简化实现
3. **提醒相关组件**：统一接口，提高复用性
4. **仪表盘组件**：优化性能，提高可配置性
5. **十字星形态组件**：简化或考虑移除（根据功能评估矩阵）

## 预期成果

1. 更清晰、更一致的组件结构
2. 更高的组件复用性
3. 更好的代码可维护性
4. 更统一的组件接口
5. 更小的代码体积
6. 更好的性能
