{"config": {"session": {"maxAge": 86400000, "key": "EGG_SESS", "httpOnly": true, "encrypt": true, "logValue": true}, "security": {"domainWhiteList": ["http://localhost:5173"], "protocolWhiteList": [], "defaultMiddleware": "csrf,hsts,methodnoallow,noopen,nosniff,csp,xssProtection,xframe,dta", "csrf": {"enable": false, "type": "ctoken", "ignoreJSON": false, "useSession": false, "cookieName": "csrfToken", "sessionName": "csrfToken", "headerName": "x-csrf-token", "bodyName": "_csrf", "queryName": "_csrf", "rotateWhenInvalid": false, "supportedRequests": [{"path": {}, "methods": ["POST", "PATCH", "DELETE", "PUT", "CONNECT"]}], "refererWhiteList": [], "cookieOptions": {"signed": false, "httpOnly": true, "sameSite": "strict", "secure": false}}, "xframe": {"enable": true, "value": "SAMEORIGIN"}, "hsts": {"enable": false, "maxAge": 31536000, "includeSubdomains": false}, "dta": {"enable": true}, "methodnoallow": {"enable": true}, "noopen": {"enable": true}, "nosniff": {"enable": true}, "referrerPolicy": {"enable": false, "value": "no-referrer-when-downgrade"}, "xssProtection": {"enable": true, "value": "1; mode=block"}, "csp": {"enable": false, "policy": {}}, "ssrf": {"ipBlackList": null, "ipExceptionList": null, "hostnameExceptionList": null, "checkAddress": null}, "enable": true, "xss": {"enable": true}, "rateLimit": {"enable": true, "windowMs": 60000, "max": 100, "message": "请求频率过高，请稍后再试", "statusCode": 429, "headers": true, "paths": [{"path": "/api/v1/auth/login", "windowMs": 60000, "max": 5}, {"path": "/api/v1/auth/register", "windowMs": 60000, "max": 3}, {"path": "/api/v1/auth/reset-password", "windowMs": 60000, "max": 3}, {"path": "/api/v1/stocks/search", "windowMs": 60000, "max": 20}]}, "bruteForce": {"enable": true, "paths": ["/api/v1/auth/login", "/api/v1/auth/register", "/api/v1/auth/reset-password"], "maxAttempts": 5, "blockDuration": 900000}, "contentSecurityPolicy": {"enable": true, "policy": {"default-src": ["'self'"], "script-src": ["'self'", "'unsafe-inline'", "'unsafe-eval'"], "style-src": ["'self'", "'unsafe-inline'"], "img-src": ["'self'", "data:"], "font-src": ["'self'", "data:"], "connect-src": ["'self'"]}}, "headers": {"enable": true, "xssProtection": {"enable": true, "value": "1; mode=block"}, "noSniff": {"enable": true, "value": "nosniff"}, "frameOptions": {"enable": true, "value": "SAMEORIGIN"}, "hsts": {"enable": false, "maxAge": 31536000, "includeSubdomains": true}, "referrerPolicy": {"enable": true, "value": "strict-origin-when-cross-origin"}}, "_protocolWhiteListSet": "<Set>"}, "helper": {"shtml": {}}, "jsonp": {"limit": 50, "callback": ["_callback", "callback"], "csrf": false}, "onerror": {"errorPageUrl": "", "appErrorFilter": null, "templatePath": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-onerror\\lib\\onerror_page.mustache"}, "i18n": {"defaultLocale": "en_US", "dirs": [], "queryField": "locale", "cookieField": "locale", "cookieDomain": "", "cookieMaxAge": "1y"}, "watcher": {"type": "development", "eventSources": {"default": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-watcher\\lib\\event-sources\\default", "development": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-watcher\\lib\\event-sources\\development"}}, "customLogger": {"scheduleLogger": {"consoleLevel": "NONE", "file": "egg-schedule.log"}}, "schedule": {"directory": []}, "multipart": {"mode": "stream", "autoFields": false, "defaultCharset": "utf8", "defaultParamCharset": "utf8", "fieldNameSize": 100, "fieldSize": "100kb", "fields": 10, "fileSize": "10mb", "files": 10, "fileExtensions": [], "whitelist": null, "allowArrayField": false, "tmpdir": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\egg-multipart-tmp\\stock-analysis-server", "cleanSchedule": {"cron": "0 30 4 * * *", "disable": false}}, "development": {"watchDirs": [], "ignoreDirs": [], "fastReady": false, "reloadOnDebug": true, "overrideDefault": false, "overrideIgnore": false}, "logrotator": {"filesRotateByHour": null, "hourDelimiter": "-", "filesRotateBySize": null, "maxFileSize": 52428800, "maxFiles": 10, "rotateDuration": 60000, "maxDays": 31}, "static": {"prefix": "/public/", "dir": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\app\\public", "dynamic": true, "preload": false, "buffer": false, "maxFiles": 1000}, "view": {"root": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\app\\view", "cache": false, "defaultExtension": ".html", "defaultViewEngine": "", "mapping": {}}, "sequelize": {"dialect": "mysql", "database": "stock_analysis", "host": "127.0.0.1", "port": 3306, "username": "root", "password": "<String len: 4>", "timezone": "+08:00", "define": {"underscored": true, "freezeTableName": true}}, "jwt": {"secret": "<String len: 15>", "enable": false, "expiresIn": "7d"}, "cors": {"origin": "*", "allowMethods": "GET,HEAD,PUT,POST,DELETE,PATCH,OPTIONS", "credentials": true}, "validate": {}, "mysql": {"default": {"database": null, "connectionLimit": 5}, "app": true, "agent": false}, "env": "local", "name": "Stock Analysis Web", "keys": "<String len: 32>", "cookies": {}, "proxy": false, "maxIpsCount": 0, "maxProxyCount": 0, "protocolHeaders": "x-forwarded-proto", "ipHeaders": "x-forwarded-for", "hostHeaders": "", "pkg": {"name": "stock-analysis-server", "version": "1.0.0", "description": "Backend server for stock analysis web application", "private": true, "egg": {"declarations": true}, "dependencies": {"apollo-server-express": "^3.13.0", "egg": "^3.30.1", "egg-cors": "^2.2.4", "egg-jwt": "^3.1.7", "egg-mysql": "^3.4.0", "egg-rate-limiter": "^2.0.0", "egg-redis": "^2.4.0", "egg-scripts": "^2.17.0", "egg-security": "^3.0.0", "egg-sequelize": "^6.0.0", "express-rate-limit": "^7.2.0", "graphql": "^16.11.0", "graphql-tools": "^9.0.19", "helmet": "^7.1.0", "iconv-lite": "^0.6.3", "mysql2": "^3.14.0", "rate-limit-redis": "^4.2.0", "redis": "^4.6.13", "sequelize": "^6.37.7", "xss": "^1.0.14"}, "devDependencies": {"egg-bin": "^5.19.0", "egg-ci": "^2.2.0", "egg-mock": "^5.15.1", "eslint": "^8.57.1", "eslint-config-egg": "^12.3.1", "sequelize-cli": "^6.6.3"}, "engines": {"node": ">=14.0.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-stock-analysis-server", "stop": "egg-scripts stop --title=egg-server-stock-analysis-server", "dev": "egg-bin dev", "dev:win": "set EGG_SKIP_MIGRATION=true && egg-bin dev", "dev-no-migrate": "egg-bin dev --ignore-migration", "debug": "egg-bin debug", "test": "npm run lint -- --fix && npm run test-local", "test-local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov", "migrate": "sequelize db:migrate", "migrate:undo": "sequelize db:migrate:undo", "migrate:status": "sequelize db:migrate:status", "test:db-optimization": "node scripts/test-database-optimization.js", "db:optimize": "sequelize db:migrate --name 20250721000000-add-indexes.js"}, "ci": {"version": "16, 18", "type": "github"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT"}, "baseDir": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server", "HOME": "C:\\Users\\<USER>", "rundir": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\run", "dump": {"ignore": "<Set>", "timing": {"slowBootActionMinDuration": 5000}}, "confusedConfigurations": {"bodyparser": "<PERSON><PERSON><PERSON><PERSON>", "notFound": "notfound", "sitefile": "siteFile", "middlewares": "middleware", "httpClient": "httpclient"}, "notfound": {"pageUrl": ""}, "siteFile": {"/favicon.ico": "<Buffer len: 6463>", "cacheControl": "public, max-age=2592000"}, "bodyParser": {"enable": true, "encoding": "utf8", "formLimit": "1mb", "jsonLimit": "1mb", "textLimit": "1mb", "strict": true, "queryString": {"arrayLimit": 100, "depth": 5, "parameterLimit": 1000}, "onerror": "<Function onerror>"}, "logger": {"dir": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server/logs", "encoding": "utf8", "env": "local", "level": "DEBUG", "consoleLevel": "DEBUG", "disableConsoleAfterReady": false, "outputJSON": false, "buffer": true, "appLogName": "stock-analysis-server-web.log", "coreLogName": "egg-web.log", "agentLogName": "egg-agent.log", "errorLogName": "common-error.log", "coreLogger": {"consoleLevel": "WARN"}, "allowDebugAtProd": false, "enablePerformanceTimer": false, "enableFastContextLogger": false, "type": "agent", "localStorage": "<AsyncLocalStorage>"}, "httpclient": {"enableDNSCache": false, "dnsCacheLookupInterval": 10000, "dnsCacheMaxLength": 1000, "request": {"timeout": 5000}, "httpAgent": {"keepAlive": true, "freeSocketTimeout": 4000, "maxSockets": 9007199254740991, "maxFreeSockets": 256}, "httpsAgent": {"keepAlive": true, "freeSocketTimeout": 4000, "maxSockets": 9007199254740991, "maxFreeSockets": 256}, "useHttpClientNext": false}, "meta": {"enable": true, "logging": false}, "coreMiddleware": ["meta", "siteFile", "notfound", "<PERSON><PERSON><PERSON><PERSON>", "override<PERSON><PERSON><PERSON>"], "workerStartTimeout": 600000, "serverTimeout": null, "cluster": {"listen": {"path": "", "port": 7001, "hostname": ""}}, "clusterClient": {"maxWaitTime": 60000, "responseTimeout": 60000}, "onClientError": null, "middleware": ["<PERSON><PERSON><PERSON><PERSON>", "security", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "redis": {"client": {"port": 6379, "host": "127.0.0.1", "password": "<String len: 0>", "db": 0}}, "cache": {"enabled": true, "prefix": "app:cache:", "defaultTTL": 300, "layers": {"client": {"enabled": true, "maxAge": {"static": 86400, "api": 60, "user": 300}}, "server": {"enabled": true, "ttl": {"stock": 300, "user": 600, "market": 60, "search": 1800, "static": 86400}}, "database": {"enabled": true, "refreshInterval": {"stock": 3600, "index": 1800, "industry": 7200}}}}, "tushare": {"token": "983b25aa025eee598034c4741dc776dd73356ddc53ddcffbb180cf61", "baseUrl": "http://api.tushare.pro"}, "dataSource": {"sources": {"tushare": {"priority": 100, "reliability": 0.95, "performance": 0.9, "costPerRequest": 1, "enabled": true}, "akshare": {"priority": 90, "reliability": 0.9, "performance": 0.85, "costPerRequest": 0.5, "enabled": true}, "sina": {"priority": 80, "reliability": 0.8, "performance": 0.95, "costPerRequest": 0, "enabled": true}, "eastmoney": {"priority": 70, "reliability": 0.85, "performance": 0.8, "costPerRequest": 0, "enabled": true}, "netease": {"priority": 60, "reliability": 0.75, "performance": 0.75, "costPerRequest": 0, "enabled": true}, "tencent": {"priority": 50, "reliability": 0.7, "performance": 0.7, "costPerRequest": 0, "enabled": true}, "yahoo_finance": {"priority": 40, "reliability": 0.6, "performance": 0.6, "costPerRequest": 0, "enabled": true}, "alltick": {"priority": 30, "reliability": 0.5, "performance": 0.5, "costPerRequest": 2, "enabled": true}, "juhe": {"priority": 20, "reliability": 0.4, "performance": 0.4, "costPerRequest": 0.1, "enabled": true}, "zhitu": {"priority": 10, "reliability": 0.3, "performance": 0.3, "costPerRequest": 0.2, "enabled": true}}, "failover": {"enabled": true, "maxRetries": 3, "retryDelay": 1000, "healthCheckInterval": 300000, "recoveryThreshold": 3, "failureThreshold": 3, "timeoutThreshold": 10000}, "requestOptimizer": {"batchingEnabled": true, "throttlingEnabled": true, "parallelEnabled": true, "adaptiveEnabled": true, "rateLimits": {"default": {"maxRequests": 20, "windowMs": 1000, "maxBatchSize": 50, "minBatchWait": 50, "maxBatchWait": 200}, "tushare": {"maxRequests": 10, "windowMs": 1000, "maxBatchSize": 100, "minBatchWait": 100, "maxBatchWait": 300}, "akshare": {"maxRequests": 15, "windowMs": 1000, "maxBatchSize": 80, "minBatchWait": 50, "maxBatchWait": 200}, "sina": {"maxRequests": 30, "windowMs": 1000, "maxBatchSize": 50, "minBatchWait": 20, "maxBatchWait": 100}, "eastmoney": {"maxRequests": 20, "windowMs": 1000, "maxBatchSize": 60, "minBatchWait": 30, "maxBatchWait": 150}, "alltick": {"maxRequests": 5, "windowMs": 1000, "maxBatchSize": 20, "minBatchWait": 200, "maxBatchWait": 500}}, "parallel": {"maxConcurrent": 5, "priorityLevels": 3, "timeout": 10000, "retryCount": 2, "retryDelay": 1000, "adaptiveTimeout": true}}}, "auth": {"enable": false, "defaultUser": {"id": 1, "username": "dev_admin", "role": "admin"}}, "coreMiddlewares": "~config~coreMiddleware", "appMiddlewares": "~config~middleware", "appMiddleware": "~config~middleware"}, "plugins": {"onerror": {"enable": true, "package": "egg-onerror", "name": "onerror", "dependencies": [], "optionalDependencies": ["jsonp"], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-onerror", "version": "2.4.0"}, "session": {"enable": true, "package": "egg-session", "name": "session", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-session", "version": "3.3.0"}, "i18n": {"enable": true, "package": "egg-i18n", "name": "i18n", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-i18n", "version": "2.1.1"}, "watcher": {"enable": true, "package": "egg-watcher", "name": "watcher", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-watcher", "version": "3.1.1", "dependents": ["development"]}, "multipart": {"enable": true, "package": "egg-multipart", "name": "multipart", "dependencies": [], "optionalDependencies": ["schedule"], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-multipart", "version": "3.5.0"}, "security": {"enable": true, "package": "egg-security", "name": "security", "dependencies": [], "optionalDependencies": ["session"], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-security", "version": "3.7.0"}, "development": {"enable": true, "package": "egg-development", "name": "development", "dependencies": ["watcher"], "optionalDependencies": [], "env": ["local"], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-development", "version": "3.0.2"}, "logrotator": {"enable": true, "package": "egg-logrotator", "name": "logrotator", "dependencies": ["schedule"], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-logrotator", "version": "3.2.0"}, "schedule": {"enable": true, "package": "egg-schedule", "name": "schedule", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-schedule", "version": "4.0.1", "dependents": ["logrotator"]}, "static": {"enable": true, "package": "egg-static", "name": "static", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-static", "version": "2.3.1"}, "jsonp": {"enable": true, "package": "egg-jsonp", "name": "jsonp", "dependencies": [], "optionalDependencies": ["security"], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-jsonp", "version": "2.0.0"}, "view": {"enable": true, "package": "egg-view", "name": "view", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-view", "version": "2.1.4"}, "sequelize": {"enable": true, "package": "egg-sequelize", "name": "sequelize", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-sequelize", "version": "6.0.0"}, "jwt": {"enable": true, "package": "egg-jwt", "name": "jwt", "dependencies": [], "optionalDependencies": ["onerror"], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-jwt", "version": "3.1.7"}, "cors": {"enable": true, "package": "egg-cors", "name": "cors", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-cors", "version": "2.2.4"}, "validate": {"enable": true, "package": "egg-validate", "name": "validate", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\node_modules\\egg-validate", "version": "2.0.2"}, "mysql": {"enable": true, "package": "egg-mysql", "name": "mysql", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\node_modules\\egg-mysql", "version": "3.4.0"}, "redis": {"enable": false, "package": "egg-redis", "name": "redis", "dependencies": [], "optionalDependencies": [], "env": [], "from": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server\\config\\plugin.js", "path": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\node_modules\\egg-redis", "version": "2.6.1"}}, "appInfo": {"name": "stock-analysis-server", "baseDir": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server", "env": "local", "scope": "", "HOME": "C:\\Users\\<USER>", "pkg": {"name": "stock-analysis-server", "version": "1.0.0", "description": "Backend server for stock analysis web application", "private": true, "egg": {"declarations": true}, "dependencies": {"apollo-server-express": "^3.13.0", "egg": "^3.30.1", "egg-cors": "^2.2.4", "egg-jwt": "^3.1.7", "egg-mysql": "^3.4.0", "egg-rate-limiter": "^2.0.0", "egg-redis": "^2.4.0", "egg-scripts": "^2.17.0", "egg-security": "^3.0.0", "egg-sequelize": "^6.0.0", "express-rate-limit": "^7.2.0", "graphql": "^16.11.0", "graphql-tools": "^9.0.19", "helmet": "^7.1.0", "iconv-lite": "^0.6.3", "mysql2": "^3.14.0", "rate-limit-redis": "^4.2.0", "redis": "^4.6.13", "sequelize": "^6.37.7", "xss": "^1.0.14"}, "devDependencies": {"egg-bin": "^5.19.0", "egg-ci": "^2.2.0", "egg-mock": "^5.15.1", "eslint": "^8.57.1", "eslint-config-egg": "^12.3.1", "sequelize-cli": "^6.6.3"}, "engines": {"node": ">=14.0.0"}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-stock-analysis-server", "stop": "egg-scripts stop --title=egg-server-stock-analysis-server", "dev": "egg-bin dev", "dev:win": "set EGG_SKIP_MIGRATION=true && egg-bin dev", "dev-no-migrate": "egg-bin dev --ignore-migration", "debug": "egg-bin debug", "test": "npm run lint -- --fix && npm run test-local", "test-local": "egg-bin test", "cov": "egg-bin cov", "lint": "eslint .", "ci": "npm run lint && npm run cov", "migrate": "sequelize db:migrate", "migrate:undo": "sequelize db:migrate:undo", "migrate:status": "sequelize db:migrate:status", "test:db-optimization": "node scripts/test-database-optimization.js", "db:optimize": "sequelize db:migrate --name 20250721000000-add-indexes.js"}, "ci": {"version": "16, 18", "type": "github"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT"}, "root": "E:\\桌面\\HappyStockMarket\\stock-analysis-web\\server"}}