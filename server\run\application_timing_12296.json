[{"name": "Process Start", "start": 1753510401710, "end": 1753510403565, "duration": 1855, "pid": 12296, "index": 0}, {"name": "Application Start", "start": 1753510403567, "end": 1753510405531, "duration": 1964, "pid": 12296, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753510403586, "end": 1753510403627, "duration": 41, "pid": 12296, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753510403627, "end": 1753510403679, "duration": 52, "pid": 12296, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753510403628, "end": 1753510403629, "duration": 1, "pid": 12296, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753510403634, "end": 1753510403634, "duration": 0, "pid": 12296, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753510403635, "end": 1753510403636, "duration": 1, "pid": 12296, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753510403637, "end": 1753510403638, "duration": 1, "pid": 12296, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753510403639, "end": 1753510403640, "duration": 1, "pid": 12296, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753510403641, "end": 1753510403641, "duration": 0, "pid": 12296, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753510403642, "end": 1753510403643, "duration": 1, "pid": 12296, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753510403644, "end": 1753510403644, "duration": 0, "pid": 12296, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753510403645, "end": 1753510403646, "duration": 1, "pid": 12296, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753510403648, "end": 1753510403648, "duration": 0, "pid": 12296, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753510403649, "end": 1753510403650, "duration": 1, "pid": 12296, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753510403651, "end": 1753510403651, "duration": 0, "pid": 12296, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753510403652, "end": 1753510403653, "duration": 1, "pid": 12296, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753510403654, "end": 1753510403655, "duration": 1, "pid": 12296, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753510403656, "end": 1753510403656, "duration": 0, "pid": 12296, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753510403657, "end": 1753510403658, "duration": 1, "pid": 12296, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753510403659, "end": 1753510403660, "duration": 1, "pid": 12296, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753510403660, "end": 1753510403661, "duration": 1, "pid": 12296, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753510403662, "end": 1753510403662, "duration": 0, "pid": 12296, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753510403663, "end": 1753510403664, "duration": 1, "pid": 12296, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753510403665, "end": 1753510403665, "duration": 0, "pid": 12296, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753510403667, "end": 1753510403668, "duration": 1, "pid": 12296, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753510403670, "end": 1753510403671, "duration": 1, "pid": 12296, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753510403674, "end": 1753510403674, "duration": 0, "pid": 12296, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753510403678, "end": 1753510403678, "duration": 0, "pid": 12296, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753510403679, "end": 1753510403679, "duration": 0, "pid": 12296, "index": 29}, {"name": "Load extend/application.js", "start": 1753510403680, "end": 1753510403781, "duration": 101, "pid": 12296, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1753510403681, "end": 1753510403681, "duration": 0, "pid": 12296, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1753510403682, "end": 1753510403684, "duration": 2, "pid": 12296, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1753510403685, "end": 1753510403691, "duration": 6, "pid": 12296, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1753510403693, "end": 1753510403701, "duration": 8, "pid": 12296, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1753510403703, "end": 1753510403705, "duration": 2, "pid": 12296, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1753510403707, "end": 1753510403709, "duration": 2, "pid": 12296, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1753510403710, "end": 1753510403766, "duration": 56, "pid": 12296, "index": 37}, {"name": "Require(33) app/extend/application.js", "start": 1753510403769, "end": 1753510403772, "duration": 3, "pid": 12296, "index": 38}, {"name": "Load extend/request.js", "start": 1753510403781, "end": 1753510403799, "duration": 18, "pid": 12296, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1753510403789, "end": 1753510403791, "duration": 2, "pid": 12296, "index": 40}, {"name": "Load extend/response.js", "start": 1753510403800, "end": 1753510403820, "duration": 20, "pid": 12296, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1753510403808, "end": 1753510403812, "duration": 4, "pid": 12296, "index": 42}, {"name": "Load extend/context.js", "start": 1753510403820, "end": 1753510403894, "duration": 74, "pid": 12296, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1753510403822, "end": 1753510403837, "duration": 15, "pid": 12296, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1753510403838, "end": 1753510403840, "duration": 2, "pid": 12296, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1753510403841, "end": 1753510403842, "duration": 1, "pid": 12296, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1753510403844, "end": 1753510403873, "duration": 29, "pid": 12296, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1753510403875, "end": 1753510403877, "duration": 2, "pid": 12296, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753510403879, "end": 1753510403879, "duration": 0, "pid": 12296, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1753510403880, "end": 1753510403884, "duration": 4, "pid": 12296, "index": 50}, {"name": "Require(43) app/extend/context.js", "start": 1753510403884, "end": 1753510403885, "duration": 1, "pid": 12296, "index": 51}, {"name": "Load extend/helper.js", "start": 1753510403894, "end": 1753510403938, "duration": 44, "pid": 12296, "index": 52}, {"name": "Require(44) node_modules/egg-security/app/extend/helper.js", "start": 1753510403895, "end": 1753510403920, "duration": 25, "pid": 12296, "index": 53}, {"name": "Require(45) node_modules/egg/app/extend/helper.js", "start": 1753510403927, "end": 1753510403928, "duration": 1, "pid": 12296, "index": 54}, {"name": "Require(46) app/extend/helper.js", "start": 1753510403928, "end": 1753510403929, "duration": 1, "pid": 12296, "index": 55}, {"name": "Load app.js", "start": 1753510403938, "end": 1753510404018, "duration": 80, "pid": 12296, "index": 56}, {"name": "Require(47) node_modules/egg-session/app.js", "start": 1753510403938, "end": 1753510403939, "duration": 1, "pid": 12296, "index": 57}, {"name": "Require(48) node_modules/egg-security/app.js", "start": 1753510403939, "end": 1753510403942, "duration": 3, "pid": 12296, "index": 58}, {"name": "Require(49) node_modules/egg-onerror/app.js", "start": 1753510403943, "end": 1753510403955, "duration": 12, "pid": 12296, "index": 59}, {"name": "Require(50) node_modules/egg-i18n/app.js", "start": 1753510403956, "end": 1753510403969, "duration": 13, "pid": 12296, "index": 60}, {"name": "Require(51) node_modules/egg-watcher/app.js", "start": 1753510403970, "end": 1753510403985, "duration": 15, "pid": 12296, "index": 61}, {"name": "Require(52) node_modules/egg-schedule/app.js", "start": 1753510403985, "end": 1753510403986, "duration": 1, "pid": 12296, "index": 62}, {"name": "Require(53) node_modules/egg-multipart/app.js", "start": 1753510403987, "end": 1753510403989, "duration": 2, "pid": 12296, "index": 63}, {"name": "Require(54) node_modules/egg-development/app.js", "start": 1753510403990, "end": 1753510403990, "duration": 0, "pid": 12296, "index": 64}, {"name": "Require(55) node_modules/egg-logrotator/app.js", "start": 1753510403991, "end": 1753510403991, "duration": 0, "pid": 12296, "index": 65}, {"name": "Require(56) node_modules/egg-static/app.js", "start": 1753510403992, "end": 1753510403992, "duration": 0, "pid": 12296, "index": 66}, {"name": "Require(57) node_modules/egg-sequelize/app.js", "start": 1753510403994, "end": 1753510403994, "duration": 0, "pid": 12296, "index": 67}, {"name": "Require(58) node_modules/egg-jwt/app.js", "start": 1753510403995, "end": 1753510403995, "duration": 0, "pid": 12296, "index": 68}, {"name": "Require(59) node_modules/egg-cors/app.js", "start": 1753510403996, "end": 1753510403996, "duration": 0, "pid": 12296, "index": 69}, {"name": "Require(60) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1753510403997, "end": 1753510403999, "duration": 2, "pid": 12296, "index": 70}, {"name": "Require(61) node_modules/egg-mysql/app.js", "start": 1753510403999, "end": 1753510404015, "duration": 16, "pid": 12296, "index": 71}, {"name": "Require(62) app.js", "start": 1753510404017, "end": 1753510404017, "duration": 0, "pid": 12296, "index": 72}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753510404028, "end": 1753510405522, "duration": 1494, "pid": 12296, "index": 73}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753510404671, "end": 1753510404780, "duration": 109, "pid": 12296, "index": 74}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753510404701, "end": 1753510405480, "duration": 779, "pid": 12296, "index": 75}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753510404829, "end": 1753510405530, "duration": 701, "pid": 12296, "index": 76}, {"name": "Did Load in app.js:didLoad", "start": 1753510404830, "end": 1753510405302, "duration": 472, "pid": 12296, "index": 77}, {"name": "Load Service", "start": 1753510404830, "end": 1753510404988, "duration": 158, "pid": 12296, "index": 78}, {"name": "Load \"service\" to Context", "start": 1753510404830, "end": 1753510404988, "duration": 158, "pid": 12296, "index": 79}, {"name": "Load Middleware", "start": 1753510404988, "end": 1753510405157, "duration": 169, "pid": 12296, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1753510404989, "end": 1753510405142, "duration": 153, "pid": 12296, "index": 81}, {"name": "Load Controller", "start": 1753510405157, "end": 1753510405264, "duration": 107, "pid": 12296, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1753510405157, "end": 1753510405264, "duration": 107, "pid": 12296, "index": 83}, {"name": "Load Router", "start": 1753510405264, "end": 1753510405283, "duration": 19, "pid": 12296, "index": 84}, {"name": "Require(63) app/router.js", "start": 1753510405265, "end": 1753510405266, "duration": 1, "pid": 12296, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1753510405267, "end": 1753510405301, "duration": 34, "pid": 12296, "index": 86}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1753510405530, "end": 1753510405530, "duration": 0, "pid": 12296, "index": 87}]