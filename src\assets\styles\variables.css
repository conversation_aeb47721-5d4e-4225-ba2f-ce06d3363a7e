:root {
  /* ===== 核心品牌色彩 ===== */
  --primary-color: #1a365d;
  /* 深蓝色 - 主色调，体现专业和信任 */
  --primary-light: #2d5a87;
  /* 浅蓝色 - 主色调变体 */
  --primary-dark: #0f2a44;
  /* 深蓝色 - 主色调深色变体 */

  --accent-color: #3182ce;
  /* 蓝色 - 强调色，用于按钮和链接 */
  --accent-light: #63b3ed;
  /* 浅蓝色 - 强调色变体 */
  --accent-dark: #2c5282;
  /* 深蓝色 - 强调色深色变体 */

  /* ===== 功能性色彩 ===== */
  --success-color: #38a169;
  /* 绿色 - 成功/上涨 */
  --success-light: #68d391;
  /* 浅绿色 */
  --success-dark: #2f855a;
  /* 深绿色 */

  --danger-color: #e53e3e;
  /* 红色 - 错误/下跌 */
  --danger-light: #fc8181;
  /* 浅红色 */
  --danger-dark: #c53030;
  /* 深红色 */

  --warning-color: #d69e2e;
  /* 橙色 - 警告 */
  --warning-light: #f6e05e;
  /* 浅橙色 */
  --warning-dark: #b7791f;
  /* 深橙色 */

  --info-color: #3182ce;
  /* 蓝色 - 信息 */
  --info-light: #63b3ed;
  /* 浅蓝色 */
  --info-dark: #2c5282;
  /* 深蓝色 */

  /* ===== 中性色彩系统 ===== */
  --gray-50: #f7fafc;
  /* 最浅灰 */
  --gray-100: #edf2f7;
  /* 浅灰 */
  --gray-200: #e2e8f0;
  /* 浅灰 */
  --gray-300: #cbd5e0;
  /* 中浅灰 */
  --gray-400: #a0aec0;
  /* 中灰 */
  --gray-500: #718096;
  /* 中深灰 */
  --gray-600: #4a5568;
  /* 深灰 */
  --gray-700: #2d3748;
  /* 深灰 */
  --gray-800: #1a202c;
  /* 很深灰 */
  --gray-900: #171923;
  /* 最深灰 */

  /* ===== 背景色彩 ===== */
  --bg-primary: #ffffff;
  /* 主背景 - 纯白 */
  --bg-secondary: #f7fafc;
  /* 次要背景 - 浅灰 */
  --bg-tertiary: #edf2f7;
  /* 第三背景 - 中浅灰 */
  --bg-quaternary: #e2e8f0;
  /* 第四背景 - 中灰 */
  --bg-overlay: rgba(26, 32, 44, 0.6);
  /* 遮罩背景 */

  /* ===== 文本色彩 ===== */
  --text-primary: #1a202c;
  /* 主要文本 - 深色 */
  --text-secondary: #4a5568;
  /* 次要文本 - 中深灰 */
  --text-tertiary: #718096;
  /* 第三文本 - 中灰 */
  --text-quaternary: #a0aec0;
  /* 第四文本 - 浅灰 */
  --text-disabled: #cbd5e0;
  /* 禁用文本 - 很浅灰 */
  --text-inverse: #ffffff;
  /* 反色文本 - 白色 */

  /* ===== 边框色彩 ===== */
  --border-light: #e2e8f0;
  /* 浅边框 */
  --border-regular: #cbd5e0;
  /* 常规边框 */
  --border-medium: #a0aec0;
  /* 中等边框 */
  --border-dark: #718096;
  /* 深边框 */
  --border-focus: #3182ce;
  /* 焦点边框 */

  /* ===== 阴影系统 ===== */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* ===== 间距系统 (8px 基础网格) ===== */
  --spacing-0: 0;
  /* 0px */
  --spacing-1: 0.25rem;
  /* 4px */
  --spacing-2: 0.5rem;
  /* 8px */
  --spacing-3: 0.75rem;
  /* 12px */
  --spacing-4: 1rem;
  /* 16px */
  --spacing-5: 1.25rem;
  /* 20px */
  --spacing-6: 1.5rem;
  /* 24px */
  --spacing-8: 2rem;
  /* 32px */
  --spacing-10: 2.5rem;
  /* 40px */
  --spacing-12: 3rem;
  /* 48px */
  --spacing-16: 4rem;
  /* 64px */
  --spacing-20: 5rem;
  /* 80px */
  --spacing-24: 6rem;
  /* 96px */
  --spacing-32: 8rem;
  /* 128px */

  /* ===== 兼容性别名 ===== */
  --spacing-xs: var(--spacing-1);
  /* 4px */
  --spacing-sm: var(--spacing-2);
  /* 8px */
  --spacing-md: var(--spacing-4);
  /* 16px */
  --spacing-lg: var(--spacing-6);
  /* 24px */
  --spacing-xl: var(--spacing-8);
  /* 32px */
  --spacing-2xl: var(--spacing-12);
  /* 48px */
  --spacing-3xl: var(--spacing-16);
  /* 64px */

  /* ===== 字体系统 ===== */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Helvetica, Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  --font-family-number: 'SF Pro Display', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  /* 字体大小 */
  --font-size-xs: 0.75rem;
  /* 12px */
  --font-size-sm: 0.875rem;
  /* 14px */
  --font-size-base: 1rem;
  /* 16px */
  --font-size-lg: 1.125rem;
  /* 18px */
  --font-size-xl: 1.25rem;
  /* 20px */
  --font-size-2xl: 1.5rem;
  /* 24px */
  --font-size-3xl: 1.875rem;
  /* 30px */
  --font-size-4xl: 2.25rem;
  /* 36px */
  --font-size-5xl: 3rem;
  /* 48px */
  --font-size-6xl: 3.75rem;
  /* 60px */

  /* 兼容性别名 */
  --font-size-md: var(--font-size-base);
  --font-size-xxl: var(--font-size-2xl);

  /* 字体粗细 */
  --font-weight-thin: 100;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  /* 行高 */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* 字间距 */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  /* ===== 边框半径 ===== */
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;
  /* 2px */
  --border-radius-base: 0.25rem;
  /* 4px */
  --border-radius-md: 0.375rem;
  /* 6px */
  --border-radius-lg: 0.5rem;
  /* 8px */
  --border-radius-xl: 0.75rem;
  /* 12px */
  --border-radius-2xl: 1rem;
  /* 16px */
  --border-radius-3xl: 1.5rem;
  /* 24px */
  --border-radius-full: 9999px;
  /* 圆形 */

  /* ===== 过渡动画 ===== */
  --transition-none: none;
  --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 350ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slower: all 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* 缓动函数 */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* ===== Z-index 层级 ===== */
  --z-index-hide: -1;
  --z-index-auto: auto;
  --z-index-base: 0;
  --z-index-docked: 10;
  --z-index-dropdown: 1000;
  --z-index-sticky: 1100;
  --z-index-banner: 1200;
  --z-index-overlay: 1300;
  --z-index-modal: 1400;
  --z-index-popover: 1500;
  --z-index-skiplink: 1600;
  --z-index-toast: 1700;
  --z-index-tooltip: 1800;

  /* ===== 断点系统 ===== */
  --breakpoint-sm: 640px;
  /* 小屏幕 */
  --breakpoint-md: 768px;
  /* 中等屏幕 */
  --breakpoint-lg: 1024px;
  /* 大屏幕 */
  --breakpoint-xl: 1280px;
  /* 超大屏幕 */
  --breakpoint-2xl: 1536px;
  /* 超超大屏幕 */

  /* ===== 容器最大宽度 ===== */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* ===== 专业金融色彩 ===== */
  --color-bull: var(--success-color);
  /* 牛市/上涨 */
  --color-bear: var(--danger-color);
  /* 熊市/下跌 */
  --color-neutral: var(--gray-500);
  /* 中性/平盘 */
  --color-volume: #8884d8;
  /* 成交量 */
  --color-ma5: #ff7300;
  /* 5日均线 */
  --color-ma10: #387908;
  /* 10日均线 */
  --color-ma20: #9c27b0;
  /* 20日均线 */
  --color-ma60: #2196f3;
  /* 60日均线 */

  /* ===== 渐变色彩 ===== */
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  --gradient-success: linear-gradient(135deg, var(--success-color), var(--success-light));
  --gradient-danger: linear-gradient(135deg, var(--danger-color), var(--danger-light));
  --gradient-warning: linear-gradient(135deg, var(--warning-color), var(--warning-light));
  --gradient-info: linear-gradient(135deg, var(--info-color), var(--info-light));
  --gradient-premium: linear-gradient(135deg, #667eea, #764ba2);
  --gradient-hero: linear-gradient(135deg, var(--primary-color), var(--accent-color), #4ecdc4);
}

/* ===== 暗色主题变量 ===== */
.dark-theme {
  /* 核心品牌色彩 - 暗色主题 */
  --primary-color: #4299e1;
  --primary-light: #63b3ed;
  --primary-dark: #3182ce;

  --accent-color: #4299e1;
  --accent-light: #63b3ed;
  --accent-dark: #3182ce;

  /* 功能性色彩 - 暗色主题 */
  --success-color: #48bb78;
  --success-light: #68d391;
  --success-dark: #38a169;

  --danger-color: #f56565;
  --danger-light: #fc8181;
  --danger-dark: #e53e3e;

  --warning-color: #ed8936;
  --warning-light: #f6ad55;
  --warning-dark: #d69e2e;

  --info-color: #4299e1;
  --info-light: #63b3ed;
  --info-dark: #3182ce;

  /* 中性色彩系统 - 暗色主题 */
  --gray-50: #171923;
  --gray-100: #1a202c;
  --gray-200: #2d3748;
  --gray-300: #4a5568;
  --gray-400: #718096;
  --gray-500: #a0aec0;
  --gray-600: #cbd5e0;
  --gray-700: #e2e8f0;
  --gray-800: #edf2f7;
  --gray-900: #f7fafc;

  /* 背景色彩 - 暗色主题 */
  --bg-primary: #171923;
  --bg-secondary: #1a202c;
  --bg-tertiary: #2d3748;
  --bg-quaternary: #4a5568;
  --bg-overlay: rgba(0, 0, 0, 0.8);

  /* 文本色彩 - 暗色主题 */
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e0;
  --text-quaternary: #a0aec0;
  --text-disabled: #718096;
  --text-inverse: #171923;

  /* 边框色彩 - 暗色主题 */
  --border-light: #2d3748;
  --border-regular: #4a5568;
  --border-medium: #718096;
  --border-dark: #a0aec0;
  --border-focus: #4299e1;

  /* 阴影系统 - 暗色主题 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);

  /* 专业金融色彩 - 暗色主题 */
  --color-bull: var(--success-color);
  --color-bear: var(--danger-color);
  --color-neutral: var(--gray-500);
  --color-volume: #9f7aea;
  --color-ma5: #ff9500;
  --color-ma10: #4fd1c7;
  --color-ma20: #d53f8c;
  --color-ma60: #4299e1;

  /* 渐变色彩 - 暗色主题 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  --gradient-success: linear-gradient(135deg, var(--success-color), var(--success-light));
  --gradient-danger: linear-gradient(135deg, var(--danger-color), var(--danger-light));
  --gradient-warning: linear-gradient(135deg, var(--warning-color), var(--warning-light));
  --gradient-info: linear-gradient(135deg, var(--info-color), var(--info-light));
  --gradient-premium: linear-gradient(135deg, #805ad5, #d53f8c);
  --gradient-hero: linear-gradient(135deg, var(--primary-color), var(--accent-color), #4fd1c7);
}