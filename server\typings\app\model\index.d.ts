// This file is created by egg-ts-helper@1.35.2
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
import ExportAlertHistory = require('../../../app/model/alert_history');
import ExportApiRequestLog = require('../../../app/model/apiRequestLog');
import ExportCoinRechargeRequest = require('../../../app/model/coinRechargeRequest');
import ExportCoinTransaction = require('../../../app/model/coinTransaction');
import ExportDashboardWidget = require('../../../app/model/dashboard_widget');
import ExportDojiPattern = require('../../../app/model/doji_pattern');
import ExportDojiPatternAlert = require('../../../app/model/doji_pattern_alert');
import ExportDojiPatternAlertHistory = require('../../../app/model/doji_pattern_alert_history');
import ExportDojiPatternPerformanceStats = require('../../../app/model/doji_pattern_performance_stats');
import ExportDojiPatternQueryCache = require('../../../app/model/doji_pattern_query_cache');
import ExportDojiPatternStatistics = require('../../../app/model/doji_pattern_statistics');
import ExportDojiPatternUserSettings = require('../../../app/model/doji_pattern_user_settings');
import ExportIndex = require('../../../app/model/index');
import ExportLoginAttempt = require('../../../app/model/loginAttempt');
import ExportMarketRiskFactor = require('../../../app/model/market_risk_factor');
import ExportNotification = require('../../../app/model/notification');
import ExportPageAccessLog = require('../../../app/model/page_access_log');
import ExportPageAccessStat = require('../../../app/model/page_access_stat');
import ExportPageGroup = require('../../../app/model/page_group');
import ExportPageGroupMapping = require('../../../app/model/page_group_mapping');
import ExportPagePermission = require('../../../app/model/page_permission');
import ExportPermissionTemplate = require('../../../app/model/permission_template');
import ExportPortfolioHolding = require('../../../app/model/portfolio_holding');
import ExportPortfolioReturn = require('../../../app/model/portfolio_return');
import ExportRiskAlertLog = require('../../../app/model/risk_alert_log');
import ExportRiskAlertRule = require('../../../app/model/risk_alert_rule');
import ExportRiskMonitoringConfig = require('../../../app/model/risk_monitoring_config');
import ExportRiskMonitoringStatus = require('../../../app/model/risk_monitoring_status');
import ExportSecurityAuditLog = require('../../../app/model/securityAuditLog');
import ExportSimulationAccount = require('../../../app/model/simulation_account');
import ExportSimulationPosition = require('../../../app/model/simulation_position');
import ExportSimulationTransaction = require('../../../app/model/simulation_transaction');
import ExportStock = require('../../../app/model/stock');
import ExportStockDailyData = require('../../../app/model/stock_daily_data');
import ExportStopLossConfig = require('../../../app/model/stop_loss_config');
import ExportStopLossExecution = require('../../../app/model/stop_loss_execution');
import ExportStopLossOrder = require('../../../app/model/stop_loss_order');
import ExportStrategyExecution = require('../../../app/model/strategy_execution');
import ExportStressTestResult = require('../../../app/model/stress_test_result');
import ExportStressTestScenario = require('../../../app/model/stress_test_scenario');
import ExportSystemLog = require('../../../app/model/system_log');
import ExportSystemPage = require('../../../app/model/system_page');
import ExportTradeRecord = require('../../../app/model/trade_record');
import ExportUser = require('../../../app/model/user');
import ExportUserAlert = require('../../../app/model/user_alert');
import ExportUserBrowsingHistory = require('../../../app/model/user_browsing_history');
import ExportUserDashboard = require('../../../app/model/user_dashboard');
import ExportUserMembership = require('../../../app/model/user_membership');
import ExportUserPortfolio = require('../../../app/model/user_portfolio');
import ExportUserPreference = require('../../../app/model/user_preference');
import ExportUserStrategy = require('../../../app/model/user_strategy');
import ExportUserWatchlist = require('../../../app/model/user_watchlist');
import ExportVarCalculation = require('../../../app/model/var_calculation');
import ExportWatchlistAlert = require('../../../app/model/watchlist_alert');
import ExportWatchlistAlertHistory = require('../../../app/model/watchlist_alert_history');
import ExportWatchlistItem = require('../../../app/model/watchlist_item');

declare module 'egg' {
  interface IModel {
    AlertHistory: ReturnType<typeof ExportAlertHistory>;
    ApiRequestLog: ReturnType<typeof ExportApiRequestLog>;
    CoinRechargeRequest: ReturnType<typeof ExportCoinRechargeRequest>;
    CoinTransaction: ReturnType<typeof ExportCoinTransaction>;
    DashboardWidget: ReturnType<typeof ExportDashboardWidget>;
    DojiPattern: ReturnType<typeof ExportDojiPattern>;
    DojiPatternAlert: ReturnType<typeof ExportDojiPatternAlert>;
    DojiPatternAlertHistory: ReturnType<typeof ExportDojiPatternAlertHistory>;
    DojiPatternPerformanceStats: ReturnType<typeof ExportDojiPatternPerformanceStats>;
    DojiPatternQueryCache: ReturnType<typeof ExportDojiPatternQueryCache>;
    DojiPatternStatistics: ReturnType<typeof ExportDojiPatternStatistics>;
    DojiPatternUserSettings: ReturnType<typeof ExportDojiPatternUserSettings>;
    Index: ReturnType<typeof ExportIndex>;
    LoginAttempt: ReturnType<typeof ExportLoginAttempt>;
    MarketRiskFactor: ReturnType<typeof ExportMarketRiskFactor>;
    Notification: ReturnType<typeof ExportNotification>;
    PageAccessLog: ReturnType<typeof ExportPageAccessLog>;
    PageAccessStat: ReturnType<typeof ExportPageAccessStat>;
    PageGroup: ReturnType<typeof ExportPageGroup>;
    PageGroupMapping: ReturnType<typeof ExportPageGroupMapping>;
    PagePermission: ReturnType<typeof ExportPagePermission>;
    PermissionTemplate: ReturnType<typeof ExportPermissionTemplate>;
    PortfolioHolding: ReturnType<typeof ExportPortfolioHolding>;
    PortfolioReturn: ReturnType<typeof ExportPortfolioReturn>;
    RiskAlertLog: ReturnType<typeof ExportRiskAlertLog>;
    RiskAlertRule: ReturnType<typeof ExportRiskAlertRule>;
    RiskMonitoringConfig: ReturnType<typeof ExportRiskMonitoringConfig>;
    RiskMonitoringStatus: ReturnType<typeof ExportRiskMonitoringStatus>;
    SecurityAuditLog: ReturnType<typeof ExportSecurityAuditLog>;
    SimulationAccount: ReturnType<typeof ExportSimulationAccount>;
    SimulationPosition: ReturnType<typeof ExportSimulationPosition>;
    SimulationTransaction: ReturnType<typeof ExportSimulationTransaction>;
    Stock: ReturnType<typeof ExportStock>;
    StockDailyData: ReturnType<typeof ExportStockDailyData>;
    StopLossConfig: ReturnType<typeof ExportStopLossConfig>;
    StopLossExecution: ReturnType<typeof ExportStopLossExecution>;
    StopLossOrder: ReturnType<typeof ExportStopLossOrder>;
    StrategyExecution: ReturnType<typeof ExportStrategyExecution>;
    StressTestResult: ReturnType<typeof ExportStressTestResult>;
    StressTestScenario: ReturnType<typeof ExportStressTestScenario>;
    SystemLog: ReturnType<typeof ExportSystemLog>;
    SystemPage: ReturnType<typeof ExportSystemPage>;
    TradeRecord: ReturnType<typeof ExportTradeRecord>;
    User: ReturnType<typeof ExportUser>;
    UserAlert: ReturnType<typeof ExportUserAlert>;
    UserBrowsingHistory: ReturnType<typeof ExportUserBrowsingHistory>;
    UserDashboard: ReturnType<typeof ExportUserDashboard>;
    UserMembership: ReturnType<typeof ExportUserMembership>;
    UserPortfolio: ReturnType<typeof ExportUserPortfolio>;
    UserPreference: ReturnType<typeof ExportUserPreference>;
    UserStrategy: ReturnType<typeof ExportUserStrategy>;
    UserWatchlist: ReturnType<typeof ExportUserWatchlist>;
    VarCalculation: ReturnType<typeof ExportVarCalculation>;
    WatchlistAlert: ReturnType<typeof ExportWatchlistAlert>;
    WatchlistAlertHistory: ReturnType<typeof ExportWatchlistAlertHistory>;
    WatchlistItem: ReturnType<typeof ExportWatchlistItem>;
  }
}
