{"name": "stock-analysis-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"serve": "vite --host", "dev": "cd server && npm run dev", "build": "vite build", "preview": "vite preview", "build-only": "vite build", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "build:analyze": "cross-env ANALYZE=true vite build", "build:optimize": "npm run build && node scripts/optimize-build.js", "type-check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "format": "prettier --write src/", "proxy": "node proxy-server.cjs", "start": "run-p serve proxy", "start:prod": "pm2 start server/pm2.config.js --env production", "start:staging": "pm2 start server/pm2.config.js --env staging", "deploy:staging": "powershell -File scripts/deploy-windows.ps1 -Environment staging", "deploy:production": "powershell -File scripts/deploy-windows.ps1 -Environment production", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "ci:lint": "eslint . --ext .js,.jsx,.ts,.tsx", "ci:type-check": "vue-tsc --noEmit", "ci:audit": "npm audit --production", "ci:sonar": "sonar-scanner", "ci:deploy-history": "node scripts/deployment-history.js", "ci:rollback": "scripts/rollback.sh", "test": "vitest run src/tests", "test:watch": "vitest src/tests", "test:coverage": "vitest run src/tests --coverage", "test:ui": "vitest --ui src/tests", "test:unit": "vitest run src/tests/core src/tests/utils src/tests/services", "test:integration": "vitest run src/tests/integration", "test:performance": "vitest run src/tests/performance", "test:e2e": "npx playwright test tests/e2e", "test:server": "cd server && npm test", "test:a11y": "npx playwright test tests/accessibility", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:tushare": "vitest run src/tests/services/tushareDataSource.spec.ts", "test:datasources": "vitest run src/tests/services/tushareDataSource.spec.ts src/tests/services/dataSourceManager.spec.ts", "test:ci": "npm run test:unit -- --coverage && npm run test:integration", "test:ci:e2e": "npx playwright test --project=chromium", "test:ci:a11y": "npx playwright test --project=accessibility", "check-datasources": "node scripts/check-data-sources.cjs", "fix-datasources": "node scripts/fix-datasources.cjs", "check-api-keys": "node scripts/check-api-keys.cjs", "check-config": "node scripts/manual-check.cjs", "test-api": "node scripts/test-api-directly.cjs", "test-juhe": "node scripts/test-juhe-api.cjs", "test-zhitu": "node scripts/test-zhitu-api.cjs", "test-akshare": "node scripts/test-akshare.cjs", "test-akshare-simple": "node scripts/test-akshare-simple.cjs", "test-akshare-win": "node scripts/test-akshare-windows.cjs", "test-tushare": "node scripts/test-tushare-api.cjs", "diagnose-login": "node scripts/diagnose-login.cjs", "fix-login": "node scripts/fix-login.cjs", "check-backend": "node scripts/check-backend.cjs", "check-python": "node scripts/check-python-env.cjs", "fix-system": "node scripts/fix-system-errors.cjs", "fix-akshare": "node scripts/fix-akshare-timeout.cjs", "fix-all": "node scripts/fix-all-issues.cjs", "generate-pwa-icons": "node scripts/generate-pwa-icons.cjs"}, "dependencies": {"axios": "^1.9.0", "chalk": "^4.1.2", "crypto-js": "^4.2.0", "dompurify": "^3.0.9", "dotenv": "^16.6.0", "echarts": "^5.4.3", "egg-mysql": "^5.0.0", "egg-redis": "^2.6.1", "egg-security": "^3.0.0", "egg-validate": "^2.0.2", "element-plus": "^2.9.8", "esbuild": "^0.25.5", "file-saver": "^2.0.5", "futu-api": "^9.3.5308", "helmet": "^7.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "mitt": "^3.0.1", "node-fetch": "^3.3.2", "pinia": "^2.1.6", "uuid": "^11.1.0", "vue": "^3.3.4", "vue-router": "^4.2.4", "workbox-window": "^7.0.0", "ws": "^8.18.3", "xlsx": "^0.18.5", "xss": "^1.0.14", "yarn": "^1.22.22"}, "devDependencies": {"@axe-core/playwright": "^4.8.5", "@tsconfig/node16": "^1.0.3", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/node": "^16.18.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/ui": "^0.34.6", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "express": "^5.1.0", "jest": "^29.7.0", "jsdom": "^22.1.0", "jsonwebtoken": "^9.0.2", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "rollup-plugin-visualizer": "^6.0.3", "sequelize-cli": "^6.6.2", "sharp": "^0.32.6", "terser": "^5.24.0", "ts-jest": "^29.3.4", "typescript": "~5.0.4", "vite": "^4.3.9", "vite-plugin-compression2": "^0.10.5", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-pwa": "^0.16.5", "vite-plugin-vue-devtools": "^1.0.0-rc.5", "vitest": "^0.34.6", "vue-tsc": "^1.8.5", "workbox-build": "^7.0.0", "workbox-core": "^7.0.0", "workbox-precaching": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0"}}