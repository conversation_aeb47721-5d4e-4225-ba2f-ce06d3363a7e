/* 辅助功能样式 */

/* 屏幕阅读器专用类 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* 焦点样式 */
.focus-visible :focus {
  outline: 3px solid var(--primary-color);
  outline-offset: 2px;
}

/* 跳过导航链接 - 允许键盘用户跳过导航 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 0;
}

/* 高对比度模式 */
.high-contrast {
  --primary-color: #0074cc;
  --accent-color: #0074cc;
  --success-color: #00882b;
  --warning-color: #c75000;
  --danger-color: #cc0000;
  --info-color: #0074cc;
  
  --bg-primary: #ffffff;
  --bg-secondary: #f0f0f0;
  --bg-tertiary: #e0e0e0;
  
  --text-primary: #000000;
  --text-secondary: #333333;
  --text-tertiary: #555555;
  --text-disabled: #777777;
  
  --border-light: #666666;
  --border-regular: #444444;
  --border-dark: #222222;
}

/* 大文本模式 */
.large-text {
  --font-size-xs: 14px;
  --font-size-sm: 16px;
  --font-size-md: 18px;
  --font-size-lg: 20px;
  --font-size-xl: 24px;
  
  line-height: 1.5;
  letter-spacing: 0.12em;
  word-spacing: 0.16em;
}

/* 减少动画模式 */
.reduced-motion * {
  animation-duration: 0.001ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.001ms !important;
  scroll-behavior: auto !important;
}

/* 屏幕阅读器模式 - 增强语义和导航 */
.screen-reader-mode table {
  display: block;
}

.screen-reader-mode table caption {
  display: block;
  font-weight: bold;
  margin-bottom: 8px;
}

.screen-reader-mode abbr[title],
.screen-reader-mode acronym[title] {
  text-decoration: none;
  border-bottom: 1px dotted;
}

/* 触摸目标尺寸 */
.touch-target {
  min-width: 44px;
  min-height: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 触摸反馈 */
@media (hover: none) {
  .touch-feedback:active {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(0.98);
  }
}

/* 响应式文本大小 */
@media (max-width: 768px) {
  :root {
    --font-size-xs: 11px;
    --font-size-sm: 13px;
    --font-size-md: 15px;
    --font-size-lg: 17px;
    --font-size-xl: 19px;
  }
  
  .large-text {
    --font-size-xs: 13px;
    --font-size-sm: 15px;
    --font-size-md: 17px;
    --font-size-lg: 19px;
    --font-size-xl: 22px;
  }
}

/* 修复iOS Safari中的vh单位问题 */
.vh-fix {
  height: 100vh; /* 回退 */
  height: calc(var(--vh, 1vh) * 100);
}

/* 无障碍表单样式 */
.a11y-form label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.a11y-form input,
.a11y-form select,
.a11y-form textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid var(--border-regular);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  transition: border-color var(--transition-fast);
}

.a11y-form input:focus,
.a11y-form select:focus,
.a11y-form textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

.a11y-form .error-message {
  color: var(--danger-color);
  margin-top: 4px;
  font-size: var(--font-size-sm);
}

/* 无障碍表格样式 */
.a11y-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.a11y-table caption {
  font-weight: bold;
  text-align: left;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.a11y-table th {
  background-color: var(--bg-tertiary);
  text-align: left;
  padding: 12px;
  font-weight: 600;
  border-bottom: 2px solid var(--border-regular);
}

.a11y-table td {
  padding: 12px;
  border-bottom: 1px solid var(--border-light);
}

.a11y-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

/* 无障碍导航菜单 */
.a11y-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.a11y-nav li {
  margin: 0;
}

.a11y-nav a {
  display: block;
  padding: 12px 16px;
  color: var(--text-primary);
  text-decoration: none;
  transition: background-color var(--transition-fast);
}

.a11y-nav a:hover,
.a11y-nav a:focus {
  background-color: var(--bg-tertiary);
  outline: none;
}

.a11y-nav a.active {
  background-color: var(--primary-color);
  color: white;
}

/* 无障碍对话框 */
.a11y-dialog {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 90%;
  width: 500px;
  padding: 24px;
}

.a11y-dialog-title {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
}

.a11y-dialog-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: transparent;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  min-height: 44px;
}

/* 无障碍卡片 */
.a11y-card {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid var(--border-light);
}

.a11y-card-title {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: var(--font-size-md);
  color: var(--text-primary);
}

.a11y-card-content {
  color: var(--text-secondary);
}

/* 无障碍标签 */
.a11y-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
}

.a11y-badge-primary {
  background-color: var(--primary-color);
  color: white;
}

.a11y-badge-success {
  background-color: var(--success-color);
  color: white;
}

.a11y-badge-warning {
  background-color: var(--warning-color);
  color: white;
}

.a11y-badge-danger {
  background-color: var(--danger-color);
  color: white;
}

/* 无障碍进度条 */
.a11y-progress {
  width: 100%;
  height: 8px;
  background-color: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
}

.a11y-progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 4px;
  transition: width var(--transition-normal);
}

/* 无障碍工具提示 */
.a11y-tooltip {
  position: relative;
  display: inline-block;
}

.a11y-tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  text-align: center;
  border-radius: var(--border-radius-sm);
  padding: 8px;
  position: absolute;
  z-index: var(--z-index-tooltip);
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity var(--transition-fast);
  box-shadow: var(--shadow-sm);
  font-size: var(--font-size-sm);
}

.a11y-tooltip:hover .a11y-tooltip-text,
.a11y-tooltip:focus .a11y-tooltip-text,
.a11y-tooltip:focus-within .a11y-tooltip-text {
  visibility: visible;
  opacity: 1;
}