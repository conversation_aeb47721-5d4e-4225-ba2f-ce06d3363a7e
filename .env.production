# Production Environment Configuration

# Server Configuration
NODE_ENV=production
PORT=7001
API_PREFIX=/api/v1

# Database Configuration
DB_HOST=production_db_host
DB_PORT=3306
DB_USER=stock_user_prod
DB_PASSWORD=production_password
DB_NAME=stock_analysis_prod
DB_POOL_MIN=10
DB_POOL_MAX=50

# Redis Configuration
REDIS_HOST=production_redis_host
REDIS_PORT=6379
REDIS_PASSWORD=production_redis_password
REDIS_DB=0
REDIS_PREFIX=stock:

# JWT Configuration
JWT_SECRET=production_jwt_secret_key
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# External APIs Configuration
TUSHARE_TOKEN=your_production_tushare_token
JUHE_API_KEY=your_production_juhe_api_key
ZHITU_API_KEY=your_production_zhitu_api_key
AKSHARE_TIMEOUT=30000

# Logging Configuration
LOG_LEVEL=warn
LOG_TO_FILE=true
LOG_FILE_PATH=./logs/app-production.log

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SAMPLE_RATE=0.05

# Feature Flags
ENABLE_TURTLE_TRADING=true
ENABLE_SMART_RECOMMENDATIONS=true
ENABLE_PORTFOLIO_ANALYSIS=true
ENABLE_REAL_TIME_DATA=true