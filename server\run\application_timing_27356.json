[{"name": "Process Start", "start": 1753511599106, "end": 1753511601070, "duration": 1964, "pid": 27356, "index": 0}, {"name": "Application Start", "start": 1753511601071, "end": 1753511604385, "duration": 3314, "pid": 27356, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753511601088, "end": 1753511601128, "duration": 40, "pid": 27356, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753511601128, "end": 1753511601194, "duration": 66, "pid": 27356, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753511601129, "end": 1753511601131, "duration": 2, "pid": 27356, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753511601140, "end": 1753511601142, "duration": 2, "pid": 27356, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753511601144, "end": 1753511601145, "duration": 1, "pid": 27356, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753511601147, "end": 1753511601147, "duration": 0, "pid": 27356, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753511601149, "end": 1753511601150, "duration": 1, "pid": 27356, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753511601152, "end": 1753511601153, "duration": 1, "pid": 27356, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753511601154, "end": 1753511601155, "duration": 1, "pid": 27356, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753511601157, "end": 1753511601157, "duration": 0, "pid": 27356, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753511601158, "end": 1753511601159, "duration": 1, "pid": 27356, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753511601160, "end": 1753511601161, "duration": 1, "pid": 27356, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753511601163, "end": 1753511601163, "duration": 0, "pid": 27356, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753511601165, "end": 1753511601165, "duration": 0, "pid": 27356, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753511601167, "end": 1753511601167, "duration": 0, "pid": 27356, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753511601168, "end": 1753511601169, "duration": 1, "pid": 27356, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753511601170, "end": 1753511601170, "duration": 0, "pid": 27356, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753511601171, "end": 1753511601172, "duration": 1, "pid": 27356, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753511601173, "end": 1753511601174, "duration": 1, "pid": 27356, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753511601175, "end": 1753511601175, "duration": 0, "pid": 27356, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753511601176, "end": 1753511601177, "duration": 1, "pid": 27356, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753511601178, "end": 1753511601178, "duration": 0, "pid": 27356, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753511601180, "end": 1753511601180, "duration": 0, "pid": 27356, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753511601182, "end": 1753511601183, "duration": 1, "pid": 27356, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753511601185, "end": 1753511601185, "duration": 0, "pid": 27356, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753511601189, "end": 1753511601189, "duration": 0, "pid": 27356, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753511601193, "end": 1753511601193, "duration": 0, "pid": 27356, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753511601194, "end": 1753511601194, "duration": 0, "pid": 27356, "index": 29}, {"name": "Load extend/application.js", "start": 1753511601195, "end": 1753511601309, "duration": 114, "pid": 27356, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1753511601196, "end": 1753511601196, "duration": 0, "pid": 27356, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1753511601197, "end": 1753511601199, "duration": 2, "pid": 27356, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1753511601200, "end": 1753511601206, "duration": 6, "pid": 27356, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1753511601208, "end": 1753511601215, "duration": 7, "pid": 27356, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1753511601217, "end": 1753511601219, "duration": 2, "pid": 27356, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1753511601221, "end": 1753511601223, "duration": 2, "pid": 27356, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1753511601224, "end": 1753511601288, "duration": 64, "pid": 27356, "index": 37}, {"name": "Require(33) app/extend/application.js", "start": 1753511601291, "end": 1753511601297, "duration": 6, "pid": 27356, "index": 38}, {"name": "Load extend/request.js", "start": 1753511601309, "end": 1753511601331, "duration": 22, "pid": 27356, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1753511601319, "end": 1753511601321, "duration": 2, "pid": 27356, "index": 40}, {"name": "Load extend/response.js", "start": 1753511601331, "end": 1753511601358, "duration": 27, "pid": 27356, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1753511601343, "end": 1753511601347, "duration": 4, "pid": 27356, "index": 42}, {"name": "Load extend/context.js", "start": 1753511601358, "end": 1753511601440, "duration": 82, "pid": 27356, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1753511601359, "end": 1753511601378, "duration": 19, "pid": 27356, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1753511601379, "end": 1753511601383, "duration": 4, "pid": 27356, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1753511601385, "end": 1753511601386, "duration": 1, "pid": 27356, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1753511601388, "end": 1753511601417, "duration": 29, "pid": 27356, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1753511601419, "end": 1753511601420, "duration": 1, "pid": 27356, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753511601422, "end": 1753511601423, "duration": 1, "pid": 27356, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1753511601425, "end": 1753511601429, "duration": 4, "pid": 27356, "index": 50}, {"name": "Require(43) app/extend/context.js", "start": 1753511601429, "end": 1753511601430, "duration": 1, "pid": 27356, "index": 51}, {"name": "Load extend/helper.js", "start": 1753511601440, "end": 1753511601488, "duration": 48, "pid": 27356, "index": 52}, {"name": "Require(44) node_modules/egg-security/app/extend/helper.js", "start": 1753511601442, "end": 1753511601468, "duration": 26, "pid": 27356, "index": 53}, {"name": "Require(45) node_modules/egg/app/extend/helper.js", "start": 1753511601476, "end": 1753511601477, "duration": 1, "pid": 27356, "index": 54}, {"name": "Require(46) app/extend/helper.js", "start": 1753511601478, "end": 1753511601478, "duration": 0, "pid": 27356, "index": 55}, {"name": "Load app.js", "start": 1753511601488, "end": 1753511601571, "duration": 83, "pid": 27356, "index": 56}, {"name": "Require(47) node_modules/egg-session/app.js", "start": 1753511601489, "end": 1753511601490, "duration": 1, "pid": 27356, "index": 57}, {"name": "Require(48) node_modules/egg-security/app.js", "start": 1753511601491, "end": 1753511601493, "duration": 2, "pid": 27356, "index": 58}, {"name": "Require(49) node_modules/egg-onerror/app.js", "start": 1753511601495, "end": 1753511601507, "duration": 12, "pid": 27356, "index": 59}, {"name": "Require(50) node_modules/egg-i18n/app.js", "start": 1753511601508, "end": 1753511601521, "duration": 13, "pid": 27356, "index": 60}, {"name": "Require(51) node_modules/egg-watcher/app.js", "start": 1753511601522, "end": 1753511601535, "duration": 13, "pid": 27356, "index": 61}, {"name": "Require(52) node_modules/egg-schedule/app.js", "start": 1753511601536, "end": 1753511601537, "duration": 1, "pid": 27356, "index": 62}, {"name": "Require(53) node_modules/egg-multipart/app.js", "start": 1753511601538, "end": 1753511601540, "duration": 2, "pid": 27356, "index": 63}, {"name": "Require(54) node_modules/egg-development/app.js", "start": 1753511601541, "end": 1753511601542, "duration": 1, "pid": 27356, "index": 64}, {"name": "Require(55) node_modules/egg-logrotator/app.js", "start": 1753511601543, "end": 1753511601543, "duration": 0, "pid": 27356, "index": 65}, {"name": "Require(56) node_modules/egg-static/app.js", "start": 1753511601544, "end": 1753511601544, "duration": 0, "pid": 27356, "index": 66}, {"name": "Require(57) node_modules/egg-sequelize/app.js", "start": 1753511601546, "end": 1753511601546, "duration": 0, "pid": 27356, "index": 67}, {"name": "Require(58) node_modules/egg-jwt/app.js", "start": 1753511601547, "end": 1753511601547, "duration": 0, "pid": 27356, "index": 68}, {"name": "Require(59) node_modules/egg-cors/app.js", "start": 1753511601548, "end": 1753511601549, "duration": 1, "pid": 27356, "index": 69}, {"name": "Require(60) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1753511601549, "end": 1753511601552, "duration": 3, "pid": 27356, "index": 70}, {"name": "Require(61) node_modules/egg-mysql/app.js", "start": 1753511601552, "end": 1753511601569, "duration": 17, "pid": 27356, "index": 71}, {"name": "Require(62) app.js", "start": 1753511601570, "end": 1753511601571, "duration": 1, "pid": 27356, "index": 72}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753511601582, "end": 1753511604383, "duration": 2801, "pid": 27356, "index": 73}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753511602288, "end": 1753511602631, "duration": 343, "pid": 27356, "index": 74}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753511602400, "end": 1753511604157, "duration": 1757, "pid": 27356, "index": 75}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753511602789, "end": 1753511604235, "duration": 1446, "pid": 27356, "index": 76}, {"name": "Did Load in app.js:didLoad", "start": 1753511602791, "end": 1753511603742, "duration": 951, "pid": 27356, "index": 77}, {"name": "Load Service", "start": 1753511602791, "end": 1753511603145, "duration": 354, "pid": 27356, "index": 78}, {"name": "Load \"service\" to Context", "start": 1753511602791, "end": 1753511603145, "duration": 354, "pid": 27356, "index": 79}, {"name": "Load Middleware", "start": 1753511603145, "end": 1753511603392, "duration": 247, "pid": 27356, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1753511603145, "end": 1753511603372, "duration": 227, "pid": 27356, "index": 81}, {"name": "Load Controller", "start": 1753511603392, "end": 1753511603630, "duration": 238, "pid": 27356, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1753511603392, "end": 1753511603630, "duration": 238, "pid": 27356, "index": 83}, {"name": "Load Router", "start": 1753511603630, "end": 1753511603716, "duration": 86, "pid": 27356, "index": 84}, {"name": "Require(63) app/router.js", "start": 1753511603631, "end": 1753511603633, "duration": 2, "pid": 27356, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1753511603640, "end": 1753511603741, "duration": 101, "pid": 27356, "index": 86}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1753511604384, "end": 1753511604384, "duration": 0, "pid": 27356, "index": 87}]