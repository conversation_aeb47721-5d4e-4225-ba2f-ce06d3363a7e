[{"name": "Process Start", "start": 1753536106473, "end": 1753536110583, "duration": 4110, "pid": 29160, "index": 0}, {"name": "Application Start", "start": 1753536110584, "end": 1753536112317, "duration": 1733, "pid": 29160, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753536110602, "end": 1753536110642, "duration": 40, "pid": 29160, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753536110642, "end": 1753536110719, "duration": 77, "pid": 29160, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753536110643, "end": 1753536110645, "duration": 2, "pid": 29160, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753536110650, "end": 1753536110650, "duration": 0, "pid": 29160, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753536110652, "end": 1753536110653, "duration": 1, "pid": 29160, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753536110653, "end": 1753536110654, "duration": 1, "pid": 29160, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753536110655, "end": 1753536110656, "duration": 1, "pid": 29160, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753536110657, "end": 1753536110657, "duration": 0, "pid": 29160, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753536110659, "end": 1753536110659, "duration": 0, "pid": 29160, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753536110661, "end": 1753536110661, "duration": 0, "pid": 29160, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753536110662, "end": 1753536110663, "duration": 1, "pid": 29160, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753536110664, "end": 1753536110665, "duration": 1, "pid": 29160, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753536110666, "end": 1753536110667, "duration": 1, "pid": 29160, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753536110669, "end": 1753536110669, "duration": 0, "pid": 29160, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753536110670, "end": 1753536110671, "duration": 1, "pid": 29160, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753536110671, "end": 1753536110672, "duration": 1, "pid": 29160, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753536110673, "end": 1753536110673, "duration": 0, "pid": 29160, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753536110674, "end": 1753536110675, "duration": 1, "pid": 29160, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753536110676, "end": 1753536110677, "duration": 1, "pid": 29160, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753536110678, "end": 1753536110679, "duration": 1, "pid": 29160, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753536110679, "end": 1753536110680, "duration": 1, "pid": 29160, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753536110681, "end": 1753536110682, "duration": 1, "pid": 29160, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753536110686, "end": 1753536110686, "duration": 0, "pid": 29160, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753536110689, "end": 1753536110694, "duration": 5, "pid": 29160, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753536110703, "end": 1753536110705, "duration": 2, "pid": 29160, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753536110712, "end": 1753536110712, "duration": 0, "pid": 29160, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753536110718, "end": 1753536110719, "duration": 1, "pid": 29160, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753536110719, "end": 1753536110719, "duration": 0, "pid": 29160, "index": 29}, {"name": "Load extend/agent.js", "start": 1753536110720, "end": 1753536110841, "duration": 121, "pid": 29160, "index": 30}, {"name": "Require(26) node_modules/egg-security/app/extend/agent.js", "start": 1753536110721, "end": 1753536110723, "duration": 2, "pid": 29160, "index": 31}, {"name": "Require(27) node_modules/egg-schedule/app/extend/agent.js", "start": 1753536110727, "end": 1753536110815, "duration": 88, "pid": 29160, "index": 32}, {"name": "Require(28) node_modules/egg-logrotator/app/extend/agent.js", "start": 1753536110818, "end": 1753536110821, "duration": 3, "pid": 29160, "index": 33}, {"name": "Load extend/context.js", "start": 1753536110842, "end": 1753536110944, "duration": 102, "pid": 29160, "index": 34}, {"name": "Require(29) node_modules/egg-security/app/extend/context.js", "start": 1753536110844, "end": 1753536110872, "duration": 28, "pid": 29160, "index": 35}, {"name": "Require(30) node_modules/egg-jsonp/app/extend/context.js", "start": 1753536110874, "end": 1753536110880, "duration": 6, "pid": 29160, "index": 36}, {"name": "Require(31) node_modules/egg-i18n/app/extend/context.js", "start": 1753536110881, "end": 1753536110882, "duration": 1, "pid": 29160, "index": 37}, {"name": "Require(32) node_modules/egg-multipart/app/extend/context.js", "start": 1753536110884, "end": 1753536110912, "duration": 28, "pid": 29160, "index": 38}, {"name": "Require(33) node_modules/egg-view/app/extend/context.js", "start": 1753536110915, "end": 1753536110917, "duration": 2, "pid": 29160, "index": 39}, {"name": "Require(34) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753536110920, "end": 1753536110921, "duration": 1, "pid": 29160, "index": 40}, {"name": "Require(35) node_modules/egg/app/extend/context.js", "start": 1753536110922, "end": 1753536110928, "duration": 6, "pid": 29160, "index": 41}, {"name": "Require(36) app/extend/context.js", "start": 1753536110929, "end": 1753536110930, "duration": 1, "pid": 29160, "index": 42}, {"name": "Load agent.js", "start": 1753536110944, "end": 1753536111057, "duration": 113, "pid": 29160, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1753536110946, "end": 1753536110947, "duration": 1, "pid": 29160, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1753536110949, "end": 1753536110950, "duration": 1, "pid": 29160, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1753536110952, "end": 1753536110972, "duration": 20, "pid": 29160, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1753536110973, "end": 1753536110977, "duration": 4, "pid": 29160, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1753536110978, "end": 1753536111003, "duration": 25, "pid": 29160, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1753536111005, "end": 1753536111005, "duration": 0, "pid": 29160, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1753536111008, "end": 1753536111010, "duration": 2, "pid": 29160, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1753536111015, "end": 1753536111054, "duration": 39, "pid": 29160, "index": 51}, {"name": "Require(45) node_modules/egg/agent.js", "start": 1753536111055, "end": 1753536111055, "duration": 0, "pid": 29160, "index": 52}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753536111073, "end": 1753536112035, "duration": 962, "pid": 29160, "index": 53}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1753536111074, "end": 1753536112007, "duration": 933, "pid": 29160, "index": 54}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1753536111075, "end": 1753536112307, "duration": 1232, "pid": 29160, "index": 55}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753536111774, "end": 1753536111896, "duration": 122, "pid": 29160, "index": 56}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753536111810, "end": 1753536112031, "duration": 221, "pid": 29160, "index": 57}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753536111954, "end": 1753536112314, "duration": 360, "pid": 29160, "index": 58}]