name: Dependency Updates

on:
  schedule:
    - cron: '0 0 * * 1' # Run every Monday at midnight
  workflow_dispatch: # Allow manual triggering

jobs:
  update-dependencies:
    name: Update Dependencies
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16.x'
        cache: 'npm'
    
    - name: Check for updates
      id: ncu
      run: |
        npm install -g npm-check-updates
        echo "::set-output name=updates::$(ncu --packageFile package.json --format json)"
    
    - name: Create Pull Request if updates available
      if: ${{ fromJson(steps.ncu.outputs.updates).length > 0 }}
      uses: peter-evans/create-pull-request@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore(deps): update dependencies'
        title: 'chore(deps): update dependencies'
        body: |
          This PR updates dependencies to their latest versions.
          
          <details>
          <summary>Updates</summary>
          
          ```
          ${{ steps.ncu.outputs.updates }}
          ```
          </details>
          
          Auto-generated by the Dependency Updates workflow.
        branch: dependency-updates
        base: develop
        labels: dependencies
        draft: false