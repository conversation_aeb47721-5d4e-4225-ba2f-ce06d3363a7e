{"env": {"node": true, "es2021": true, "mocha": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"indent": ["error", 2], "linebreak-style": ["error", "unix"], "quotes": ["error", "single"], "semi": ["error", "always"], "no-console": "warn", "no-unused-vars": "warn", "no-undef": "error", "no-multiple-empty-lines": ["error", {"max": 1}], "eol-last": ["error", "always"], "no-case-declarations": "off"}, "overrides": [{"files": ["test/**/*.js"], "rules": {"no-console": "off"}}]}