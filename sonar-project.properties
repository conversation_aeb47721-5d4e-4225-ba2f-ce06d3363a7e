# SonarCloud configuration for stock-analysis-web

# Project identification
sonar.projectKey=happystockmarket_stock-analysis-web
sonar.organization=happystockmarket

# Project metadata
sonar.projectName=Stock Analysis Web
sonar.projectVersion=1.0

# Path to source directories
sonar.sources=src,server
sonar.tests=src/tests,server/test

# Encoding of the source files
sonar.sourceEncoding=UTF-8

# Exclude patterns
sonar.exclusions=node_modules/**,dist/**,coverage/**,**/*.test.js,**/*.spec.js,**/*.test.ts,**/*.spec.ts

# Test coverage configuration
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.typescript.lcov.reportPaths=coverage/lcov.info

# TypeScript configuration
sonar.typescript.tsconfigPath=tsconfig.json

# JavaScript configuration
sonar.javascript.jsVersion=ES2020

# Code duplication detection
sonar.cpd.exclusions=src/tests/**,server/test/**