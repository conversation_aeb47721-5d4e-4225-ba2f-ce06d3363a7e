# Staging Environment Configuration

# Server Configuration
NODE_ENV=staging
PORT=7001
API_PREFIX=/api/v1

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=stock_user_staging
DB_PASSWORD=staging_password
DB_NAME=stock_analysis_staging
DB_POOL_MIN=5
DB_POOL_MAX=20

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1
REDIS_PREFIX=stock_staging:

# JWT Configuration
JWT_SECRET=staging_jwt_secret_key
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# External APIs Configuration
TUSHARE_TOKEN=your_tushare_token
JUHE_API_KEY=your_juhe_api_key
ZHITU_API_KEY=your_zhitu_api_key
AKSHARE_TIMEOUT=30000

# Logging Configuration
LOG_LEVEL=info
LOG_TO_FILE=true
LOG_FILE_PATH=./logs/app-staging.log

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SAMPLE_RATE=0.1

# Feature Flags
ENABLE_TURTLE_TRADING=true
ENABLE_SMART_RECOMMENDATIONS=true
ENABLE_PORTFOLIO_ANALYSIS=true
ENABLE_REAL_TIME_DATA=false