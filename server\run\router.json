[{"name": null, "methods": ["HEAD", "ACL", "BIND", "CHECKOUT", "CONNECT", "COPY", "DELETE", "GET", "HEAD", "LINK", "LOCK", "M-SEARCH", "MERGE", "MKACTIVITY", "MKCALENDAR", "MKCOL", "MOVE", "NOTIFY", "OPTIONS", "PATCH", "POST", "PROPFIND", "PROPPATCH", "PURGE", "PUT", "QUERY", "REBIND", "REPORT", "SEARCH", "SOURCE", "SUBSCRIBE", "TRACE", "UNBIND", "UNLINK", "UNLOCK", "UNSUBSCRIBE"], "paramNames": [{"name": 0, "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": true, "pattern": ".*"}], "path": "/api/*", "regexp": "/^\\/api\\/((?:.*))(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "ACL", "BIND", "CHECKOUT", "CONNECT", "COPY", "DELETE", "GET", "HEAD", "LINK", "LOCK", "M-SEARCH", "MERGE", "MKACTIVITY", "MKCALENDAR", "MKCOL", "MOVE", "NOTIFY", "OPTIONS", "PATCH", "POST", "PROPFIND", "PROPPATCH", "PURGE", "PUT", "QUERY", "REBIND", "REPORT", "SEARCH", "SOURCE", "SUBSCRIBE", "TRACE", "UNBIND", "UNLINK", "UNLOCK", "UNSUBSCRIBE"], "paramNames": [], "path": "/api/v1/graphql", "regexp": "/^\\/api\\/v1\\/graphql(?:\\/(?=$))?$/", "stack": ["graphqlContext", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/graphql/playground", "regexp": "/^\\/api\\/v1\\/graphql\\/playground(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/graphql/schema", "regexp": "/^\\/api\\/v1\\/graphql\\/schema(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/auth/register", "regexp": "/^\\/api\\/v1\\/auth\\/register(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/auth/login", "regexp": "/^\\/api\\/v1\\/auth\\/login(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/auth/logout", "regexp": "/^\\/api\\/v1\\/auth\\/logout(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/auth/refresh", "regexp": "/^\\/api\\/v1\\/auth\\/refresh(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/auth/password-reset", "regexp": "/^\\/api\\/v1\\/auth\\/password-reset(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/auth/validate", "regexp": "/^\\/api\\/v1\\/auth\\/validate(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/users/profile", "regexp": "/^\\/api\\/v1\\/users\\/profile(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/v1/users/profile", "regexp": "/^\\/api\\/v1\\/users\\/profile(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/v1/users/preferences", "regexp": "/^\\/api\\/v1\\/users\\/preferences(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/v1/users/password", "regexp": "/^\\/api\\/v1\\/users\\/password(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/stocks", "regexp": "/^\\/api\\/v1\\/stocks(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/stocks/search", "regexp": "/^\\/api\\/v1\\/stocks\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "symbol", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/stocks/:symbol", "regexp": "/^\\/api\\/v1\\/stocks\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "symbol", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/stocks/:symbol/quote", "regexp": "/^\\/api\\/v1\\/stocks\\/((?:[^\\/]+?))\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "symbol", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/stocks/:symbol/history", "regexp": "/^\\/api\\/v1\\/stocks\\/((?:[^\\/]+?))\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "symbol", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/stocks/:symbol/indicators", "regexp": "/^\\/api\\/v1\\/stocks\\/((?:[^\\/]+?))\\/indicators(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/stocks/quotes/batch", "regexp": "/^\\/api\\/v1\\/stocks\\/quotes\\/batch(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/industries", "regexp": "/^\\/api\\/v1\\/industries(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "code", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/industries/:code", "regexp": "/^\\/api\\/v1\\/industries\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/markets/hot-stocks", "regexp": "/^\\/api\\/v1\\/markets\\/hot-stocks(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/markets/limit-up", "regexp": "/^\\/api\\/v1\\/markets\\/limit-up(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/markets/limit-down", "regexp": "/^\\/api\\/v1\\/markets\\/limit-down(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/watchlists", "regexp": "/^\\/api\\/v1\\/watchlists(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/watchlists", "regexp": "/^\\/api\\/v1\\/watchlists(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/watchlists/:id", "regexp": "/^\\/api\\/v1\\/watchlists\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/watchlists/:id", "regexp": "/^\\/api\\/v1\\/watchlists\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/watchlists/:id", "regexp": "/^\\/api\\/v1\\/watchlists\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/watchlists/:id/items", "regexp": "/^\\/api\\/v1\\/watchlists\\/((?:[^\\/]+?))\\/items(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/watchlists/:id/items", "regexp": "/^\\/api\\/v1\\/watchlists\\/((?:[^\\/]+?))\\/items(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "watchlistId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "itemId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/watchlists/:watchlistId/items/:itemId", "regexp": "/^\\/api\\/v1\\/watchlists\\/((?:[^\\/]+?))\\/items\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "watchlistId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "itemId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/watchlists/:watchlistId/items/:itemId", "regexp": "/^\\/api\\/v1\\/watchlists\\/((?:[^\\/]+?))\\/items\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/portfolios", "regexp": "/^\\/api\\/v1\\/portfolios(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/portfolios", "regexp": "/^\\/api\\/v1\\/portfolios(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/portfolios/:id", "regexp": "/^\\/api\\/v1\\/portfolios\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/portfolios/:id", "regexp": "/^\\/api\\/v1\\/portfolios\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/portfolios/:id", "regexp": "/^\\/api\\/v1\\/portfolios\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/portfolios/:id/holdings", "regexp": "/^\\/api\\/v1\\/portfolios\\/((?:[^\\/]+?))\\/holdings(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/portfolios/:id/holdings", "regexp": "/^\\/api\\/v1\\/portfolios\\/((?:[^\\/]+?))\\/holdings(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "portfolioId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "holdingId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/portfolios/:portfolioId/holdings/:holdingId", "regexp": "/^\\/api\\/v1\\/portfolios\\/((?:[^\\/]+?))\\/holdings\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "portfolioId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "holdingId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/portfolios/:portfolioId/holdings/:holdingId", "regexp": "/^\\/api\\/v1\\/portfolios\\/((?:[^\\/]+?))\\/holdings\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/portfolios/:id/trades", "regexp": "/^\\/api\\/v1\\/portfolios\\/((?:[^\\/]+?))\\/trades(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/portfolios/:id/trades", "regexp": "/^\\/api\\/v1\\/portfolios\\/((?:[^\\/]+?))\\/trades(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "portfolioId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "tradeId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/portfolios/:portfolioId/trades/:tradeId", "regexp": "/^\\/api\\/v1\\/portfolios\\/((?:[^\\/]+?))\\/trades\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "portfolioId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "tradeId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/portfolios/:portfolioId/trades/:tradeId", "regexp": "/^\\/api\\/v1\\/portfolios\\/((?:[^\\/]+?))\\/trades\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/alerts", "regexp": "/^\\/api\\/v1\\/alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/alerts", "regexp": "/^\\/api\\/v1\\/alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/alerts/:id", "regexp": "/^\\/api\\/v1\\/alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/alerts/:id", "regexp": "/^\\/api\\/v1\\/alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/alerts/:id", "regexp": "/^\\/api\\/v1\\/alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/alerts/:id/history", "regexp": "/^\\/api\\/v1\\/alerts\\/((?:[^\\/]+?))\\/history(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "symbol", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/analysis/indicators/:symbol", "regexp": "/^\\/api\\/v1\\/analysis\\/indicators\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "symbol", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/analysis/signals/:symbol", "regexp": "/^\\/api\\/v1\\/analysis\\/signals\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/analysis/scan", "regexp": "/^\\/api\\/v1\\/analysis\\/scan(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/analysis/patterns/doji", "regexp": "/^\\/api\\/v1\\/analysis\\/patterns\\/doji(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/analysis/patterns/doji/screen", "regexp": "/^\\/api\\/v1\\/analysis\\/patterns\\/doji\\/screen(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/strategies", "regexp": "/^\\/api\\/v1\\/strategies(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/strategies", "regexp": "/^\\/api\\/v1\\/strategies(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/strategies/:id", "regexp": "/^\\/api\\/v1\\/strategies\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/strategies/:id", "regexp": "/^\\/api\\/v1\\/strategies\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/strategies/:id", "regexp": "/^\\/api\\/v1\\/strategies\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/strategies/:id/execute", "regexp": "/^\\/api\\/v1\\/strategies\\/((?:[^\\/]+?))\\/execute(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/backtests", "regexp": "/^\\/api\\/v1\\/backtests(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/backtests", "regexp": "/^\\/api\\/v1\\/backtests(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/backtests/:id", "regexp": "/^\\/api\\/v1\\/backtests\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/backtests/:id", "regexp": "/^\\/api\\/v1\\/backtests\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/risk/configs", "regexp": "/^\\/api\\/v1\\/risk\\/configs(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/risk/configs", "regexp": "/^\\/api\\/v1\\/risk\\/configs(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/risk/configs/:id", "regexp": "/^\\/api\\/v1\\/risk\\/configs\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/risk/configs/:id", "regexp": "/^\\/api\\/v1\\/risk\\/configs\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/risk/alerts", "regexp": "/^\\/api\\/v1\\/risk\\/alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/risk/alerts", "regexp": "/^\\/api\\/v1\\/risk\\/alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/risk/alerts/:id", "regexp": "/^\\/api\\/v1\\/risk\\/alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/risk/alerts/:id", "regexp": "/^\\/api\\/v1\\/risk\\/alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/risk/var", "regexp": "/^\\/api\\/v1\\/risk\\/var(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/risk/var/calculate", "regexp": "/^\\/api\\/v1\\/risk\\/var\\/calculate(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/simulation/accounts", "regexp": "/^\\/api\\/v1\\/simulation\\/accounts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/simulation/accounts", "regexp": "/^\\/api\\/v1\\/simulation\\/accounts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/simulation/accounts/:id", "regexp": "/^\\/api\\/v1\\/simulation\\/accounts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/simulation/accounts/:id/positions", "regexp": "/^\\/api\\/v1\\/simulation\\/accounts\\/((?:[^\\/]+?))\\/positions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/simulation/accounts/:id/transactions", "regexp": "/^\\/api\\/v1\\/simulation\\/accounts\\/((?:[^\\/]+?))\\/transactions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/simulation/accounts/:id/trades", "regexp": "/^\\/api\\/v1\\/simulation\\/accounts\\/((?:[^\\/]+?))\\/trades(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/membership", "regexp": "/^\\/api\\/v1\\/membership(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/membership/levels", "regexp": "/^\\/api\\/v1\\/membership\\/levels(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/membership/check-access", "regexp": "/^\\/api\\/v1\\/membership\\/check-access(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/notifications", "regexp": "/^\\/api\\/v1\\/notifications(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/notifications/unread-count", "regexp": "/^\\/api\\/v1\\/notifications\\/unread-count(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/notifications/:id/read", "regexp": "/^\\/api\\/v1\\/notifications\\/((?:[^\\/]+?))\\/read(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/v1/notifications/read-all", "regexp": "/^\\/api\\/v1\\/notifications\\/read-all(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/notifications/:id", "regexp": "/^\\/api\\/v1\\/notifications\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/system/health", "regexp": "/^\\/api\\/v1\\/system\\/health(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/system/cache/stats", "regexp": "/^\\/api\\/v1\\/system\\/cache\\/stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/system/cache/refresh", "regexp": "/^\\/api\\/v1\\/system\\/cache\\/refresh(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "key", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/system/cache/:key", "regexp": "/^\\/api\\/v1\\/system\\/cache\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/docs", "regexp": "/^\\/api\\/v1\\/docs(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/docs/graphql", "regexp": "/^\\/api\\/v1\\/docs\\/graphql(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/docs/migration", "regexp": "/^\\/api\\/v1\\/docs\\/migration(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/version", "regexp": "/^\\/api\\/v1\\/version(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/graphql", "regexp": "/^\\/api\\/v1\\/graphql(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/graphql/playground", "regexp": "/^\\/api\\/v1\\/graphql\\/playground(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/graphql/schema", "regexp": "/^\\/api\\/v1\\/graphql\\/schema(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/graphql/health", "regexp": "/^\\/api\\/v1\\/graphql\\/health(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/database/health", "regexp": "/^\\/api\\/v1\\/database\\/health(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/database/connection-pool", "regexp": "/^\\/api\\/v1\\/database\\/connection-pool(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/database/slow-queries", "regexp": "/^\\/api\\/v1\\/database\\/slow-queries(?:\\/(?=$))?$/", "stack": ["adminAuth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/database/tables", "regexp": "/^\\/api\\/v1\\/database\\/tables(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/database/table-stats", "regexp": "/^\\/api\\/v1\\/database\\/table-stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/database/maintain-table", "regexp": "/^\\/api\\/v1\\/database\\/maintain-table(?:\\/(?=$))?$/", "stack": ["adminAuth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/database/maintain-database", "regexp": "/^\\/api\\/v1\\/database\\/maintain-database(?:\\/(?=$))?$/", "stack": ["adminAuth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/database/clear-model-cache", "regexp": "/^\\/api\\/v1\\/database\\/clear-model-cache(?:\\/(?=$))?$/", "stack": ["adminAuth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-quality/validate", "regexp": "/^\\/api\\/v1\\/data-quality\\/validate(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-quality/validate-batch", "regexp": "/^\\/api\\/v1\\/data-quality\\/validate-batch(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-quality/transform", "regexp": "/^\\/api\\/v1\\/data-quality\\/transform(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-quality/transform-batch", "regexp": "/^\\/api\\/v1\\/data-quality\\/transform-batch(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-quality/process", "regexp": "/^\\/api\\/v1\\/data-quality\\/process(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-quality/process-batch", "regexp": "/^\\/api\\/v1\\/data-quality\\/process-batch(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/data-quality/stats", "regexp": "/^\\/api\\/v1\\/data-quality\\/stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-quality/reset-stats", "regexp": "/^\\/api\\/v1\\/data-quality\\/reset-stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/data-quality/schemas", "regexp": "/^\\/api\\/v1\\/data-quality\\/schemas(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/security/csrf-token", "regexp": "/^\\/api\\/v1\\/security\\/csrf-token(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/security/validate-csrf", "regexp": "/^\\/api\\/v1\\/security\\/validate-csrf(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/security/config", "regexp": "/^\\/api\\/v1\\/security\\/config(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/", "regexp": "/^(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/health/database", "regexp": "/^\\/api\\/health\\/database(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/health/user-table", "regexp": "/^\\/api\\/health\\/user-table(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/health/jwt", "regexp": "/^\\/api\\/health\\/jwt(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/health/system", "regexp": "/^\\/api\\/health\\/system(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/health/create-test-user", "regexp": "/^\\/api\\/health\\/create-test-user(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/health/reset-test-passwords", "regexp": "/^\\/api\\/health\\/reset-test-passwords(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/auth/register", "regexp": "/^\\/api\\/auth\\/register(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/auth/login", "regexp": "/^\\/api\\/auth\\/login(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/auth/password-reset-request", "regexp": "/^\\/api\\/auth\\/password-reset-request(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/auth/validate-token", "regexp": "/^\\/api\\/auth\\/validate-token(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/users/profile", "regexp": "/^\\/api\\/users\\/profile(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/users/profile", "regexp": "/^\\/api\\/users\\/profile(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/users/preferences", "regexp": "/^\\/api\\/users\\/preferences(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/users/password", "regexp": "/^\\/api\\/users\\/password(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/watchlists", "regexp": "/^\\/api\\/watchlists(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/watchlists", "regexp": "/^\\/api\\/watchlists(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:id", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:id", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:id/stocks", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))\\/stocks(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:id/stocks", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))\\/stocks(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "watchlistId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "itemId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:watchlistId/stocks/:itemId", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))\\/stocks\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "watchlistId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "itemId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlists/:watchlistId/stocks/:itemId/notes", "regexp": "/^\\/api\\/watchlists\\/((?:[^\\/]+?))\\/stocks\\/((?:[^\\/]+?))\\/notes(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/portfolios", "regexp": "/^\\/api\\/portfolios(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/portfolios", "regexp": "/^\\/api\\/portfolios(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id/holdings", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/holdings(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id/holdings", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/holdings(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "portfolioId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "holdingId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:portfolioId/holdings/:holdingId", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/holdings\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "portfolioId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "holdingId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:portfolioId/holdings/:holdingId", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/holdings\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id/trades", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/trades(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:id/trades", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/trades(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "portfolioId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "tradeId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/portfolios/:portfolioId/trades/:tradeId", "regexp": "/^\\/api\\/portfolios\\/((?:[^\\/]+?))\\/trades\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/simulation/accounts", "regexp": "/^\\/api\\/simulation\\/accounts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/simulation/accounts", "regexp": "/^\\/api\\/simulation\\/accounts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/simulation/accounts/:id", "regexp": "/^\\/api\\/simulation\\/accounts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/simulation/accounts/:id/positions", "regexp": "/^\\/api\\/simulation\\/accounts\\/((?:[^\\/]+?))\\/positions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/simulation/accounts/:id/transactions", "regexp": "/^\\/api\\/simulation\\/accounts\\/((?:[^\\/]+?))\\/transactions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/simulation/accounts/:id/trade", "regexp": "/^\\/api\\/simulation\\/accounts\\/((?:[^\\/]+?))\\/trade(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stocks", "regexp": "/^\\/api\\/stocks(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stocks/search", "regexp": "/^\\/api\\/stocks\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "code", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stocks/:code/quote", "regexp": "/^\\/api\\/stocks\\/((?:[^\\/]+?))\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/stocks/quotes/batch", "regexp": "/^\\/api\\/stocks\\/quotes\\/batch(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "code", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stocks/:code/history", "regexp": "/^\\/api\\/stocks\\/((?:[^\\/]+?))\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/stocks/sync", "regexp": "/^\\/api\\/stocks\\/sync(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stocks/stats", "regexp": "/^\\/api\\/stocks\\/stats(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stocks/industry-list", "regexp": "/^\\/api\\/stocks\\/industry-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "code", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stocks/industry/:code", "regexp": "/^\\/api\\/stocks\\/industry\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stocks/hot-stocks", "regexp": "/^\\/api\\/stocks\\/hot-stocks(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stocks/limit-up", "regexp": "/^\\/api\\/stocks\\/limit-up(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stocks/limit-down", "regexp": "/^\\/api\\/stocks\\/limit-down(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/technical-indicators/scan", "regexp": "/^\\/api\\/technical-indicators\\/scan(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "stockCode", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/technical-indicators/:stockCode", "regexp": "/^\\/api\\/technical-indicators\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "stockCode", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/technical-indicators/:stockCode/realtime", "regexp": "/^\\/api\\/technical-indicators\\/((?:[^\\/]+?))\\/realtime(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "stockCode", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/technical-indicators/:stockCode/history", "regexp": "/^\\/api\\/technical-indicators\\/((?:[^\\/]+?))\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/test/redis", "regexp": "/^\\/api\\/test\\/redis(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/test/store-stock", "regexp": "/^\\/api\\/test\\/store-stock(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/test/store-all-stocks", "regexp": "/^\\/api\\/test\\/store-all-stocks(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/env/info", "regexp": "/^\\/api\\/env\\/info(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/test", "regexp": "/^\\/api\\/sina\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/quote", "regexp": "/^\\/api\\/sina\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/stock-list", "regexp": "/^\\/api\\/sina\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/search", "regexp": "/^\\/api\\/sina\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/history", "regexp": "/^\\/api\\/sina\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/sina/news", "regexp": "/^\\/api\\/sina\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/test", "regexp": "/^\\/api\\/eastmoney\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/quote", "regexp": "/^\\/api\\/eastmoney\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/stock-list", "regexp": "/^\\/api\\/eastmoney\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/search", "regexp": "/^\\/api\\/eastmoney\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/history", "regexp": "/^\\/api\\/eastmoney\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/eastmoney/news", "regexp": "/^\\/api\\/eastmoney\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/test", "regexp": "/^\\/api\\/tencent\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/quote", "regexp": "/^\\/api\\/tencent\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/stock-list", "regexp": "/^\\/api\\/tencent\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/search", "regexp": "/^\\/api\\/tencent\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/history", "regexp": "/^\\/api\\/tencent\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tencent/news", "regexp": "/^\\/api\\/tencent\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/test", "regexp": "/^\\/api\\/netease\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/quote", "regexp": "/^\\/api\\/netease\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/stock-list", "regexp": "/^\\/api\\/netease\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/search", "regexp": "/^\\/api\\/netease\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/history", "regexp": "/^\\/api\\/netease\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/netease/news", "regexp": "/^\\/api\\/netease\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/test", "regexp": "/^\\/api\\/akshare\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/quote", "regexp": "/^\\/api\\/akshare\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/stock-list", "regexp": "/^\\/api\\/akshare\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/search", "regexp": "/^\\/api\\/akshare\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/history", "regexp": "/^\\/api\\/akshare\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/akshare/news", "regexp": "/^\\/api\\/akshare\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tushare/test", "regexp": "/^\\/api\\/tushare\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/tushare/stock-basic", "regexp": "/^\\/api\\/tushare\\/stock-basic(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/tushare/update-stock-basic", "regexp": "/^\\/api\\/tushare\\/update-stock-basic(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/tushare", "regexp": "/^\\/api\\/tushare(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/data-source/test", "regexp": "/^\\/api\\/data-source\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/data-source/stocks", "regexp": "/^\\/api\\/data-source\\/stocks(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/data-source-management/sources", "regexp": "/^\\/api\\/v1\\/data-source-management\\/sources(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/data-source-management/health", "regexp": "/^\\/api\\/v1\\/data-source-management\\/health(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-source-management/check-health", "regexp": "/^\\/api\\/v1\\/data-source-management\\/check-health(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/data-source-management/priority", "regexp": "/^\\/api\\/v1\\/data-source-management\\/priority(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/data-source-management/best-source", "regexp": "/^\\/api\\/v1\\/data-source-management\\/best-source(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/data-source-management/score-history", "regexp": "/^\\/api\\/v1\\/data-source-management\\/score-history(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/v1/data-source-management/source-config", "regexp": "/^\\/api\\/v1\\/data-source-management\\/source-config(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-source-management/toggle-source", "regexp": "/^\\/api\\/v1\\/data-source-management\\/toggle-source(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-source-management/reset-health", "regexp": "/^\\/api\\/v1\\/data-source-management\\/reset-health(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/data-source-management/failover-config", "regexp": "/^\\/api\\/v1\\/data-source-management\\/failover-config(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/v1/data-source-management/failover-config", "regexp": "/^\\/api\\/v1\\/data-source-management\\/failover-config(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-source-management/test-failover", "regexp": "/^\\/api\\/v1\\/data-source-management\\/test-failover(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-source-management/test-batch", "regexp": "/^\\/api\\/v1\\/data-source-management\\/test-batch(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-source-management/test-parallel", "regexp": "/^\\/api\\/v1\\/data-source-management\\/test-parallel(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/data-source-management/request-optimizer-stats", "regexp": "/^\\/api\\/v1\\/data-source-management\\/request-optimizer-stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/data-source-management/reset-request-optimizer-stats", "regexp": "/^\\/api\\/v1\\/data-source-management\\/reset-request-optimizer-stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/yahoo_finance/test", "regexp": "/^\\/api\\/yahoo_finance\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/yahoo_finance/quote", "regexp": "/^\\/api\\/yahoo_finance\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/yahoo_finance/stock-list", "regexp": "/^\\/api\\/yahoo_finance\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/yahoo_finance/search", "regexp": "/^\\/api\\/yahoo_finance\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/yahoo_finance/history", "regexp": "/^\\/api\\/yahoo_finance\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/yahoo_finance/news", "regexp": "/^\\/api\\/yahoo_finance\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/google_finance/test", "regexp": "/^\\/api\\/google_finance\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/google_finance/quote", "regexp": "/^\\/api\\/google_finance\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/google_finance/stock-list", "regexp": "/^\\/api\\/google_finance\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/google_finance/search", "regexp": "/^\\/api\\/google_finance\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/google_finance/history", "regexp": "/^\\/api\\/google_finance\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/google_finance/news", "regexp": "/^\\/api\\/google_finance\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/juhe/test", "regexp": "/^\\/api\\/juhe\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/alltick/test", "regexp": "/^\\/api\\/alltick\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/alltick/quote", "regexp": "/^\\/api\\/alltick\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/alltick/stocks", "regexp": "/^\\/api\\/alltick\\/stocks(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/alltick/search", "regexp": "/^\\/api\\/alltick\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/alltick/history", "regexp": "/^\\/api\\/alltick\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/juhe/quote", "regexp": "/^\\/api\\/juhe\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/juhe/stock-list", "regexp": "/^\\/api\\/juhe\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/juhe/search", "regexp": "/^\\/api\\/juhe\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/juhe/history", "regexp": "/^\\/api\\/juhe\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/juhe/news", "regexp": "/^\\/api\\/juhe\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/zhitu/test", "regexp": "/^\\/api\\/zhitu\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/zhitu/quote", "regexp": "/^\\/api\\/zhitu\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/zhitu/stock-list", "regexp": "/^\\/api\\/zhitu\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/zhitu/search", "regexp": "/^\\/api\\/zhitu\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/zhitu/history", "regexp": "/^\\/api\\/zhitu\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/zhitu/news", "regexp": "/^\\/api\\/zhitu\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/alphavantage/test", "regexp": "/^\\/api\\/alphavantage\\/test(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/alphavantage/quote", "regexp": "/^\\/api\\/alphavantage\\/quote(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/alphavantage/stock-list", "regexp": "/^\\/api\\/alphavantage\\/stock-list(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/alphavantage/search", "regexp": "/^\\/api\\/alphavantage\\/search(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/alphavantage/history", "regexp": "/^\\/api\\/alphavantage\\/history(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/alphavantage/news", "regexp": "/^\\/api\\/alphavantage\\/news(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/alerts", "regexp": "/^\\/api\\/alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/alerts", "regexp": "/^\\/api\\/alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PATCH"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/alerts/:id", "regexp": "/^\\/api\\/alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/alerts/:id", "regexp": "/^\\/api\\/alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/alerts/:id/history", "regexp": "/^\\/api\\/alerts\\/((?:[^\\/]+?))\\/history(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PATCH"], "paramNames": [{"name": "historyId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/alerts/history/:historyId", "regexp": "/^\\/api\\/alerts\\/history\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/watchlist-alerts", "regexp": "/^\\/api\\/watchlist-alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/watchlist-alerts", "regexp": "/^\\/api\\/watchlist-alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "watchlistId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}, {"name": "alertId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/watchlist-alerts/:watchlistId/:alertId", "regexp": "/^\\/api\\/watchlist-alerts\\/((?:[^\\/]+?))\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/cache/status", "regexp": "/^\\/api\\/v1\\/cache\\/status(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache/clear", "regexp": "/^\\/api\\/v1\\/cache\\/clear(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache/prewarm", "regexp": "/^\\/api\\/v1\\/cache\\/prewarm(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache/smart-prewarm", "regexp": "/^\\/api\\/v1\\/cache\\/smart-prewarm(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache/invalidate", "regexp": "/^\\/api\\/v1\\/cache\\/invalidate(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache/invalidate-by-time", "regexp": "/^\\/api\\/v1\\/cache\\/invalidate-by-time(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache/compress", "regexp": "/^\\/api\\/v1\\/cache\\/compress(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/cache/hot-keys", "regexp": "/^\\/api\\/v1\\/cache\\/hot-keys(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/cache-management/stats", "regexp": "/^\\/api\\/v1\\/cache-management\\/stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/cache-management/health", "regexp": "/^\\/api\\/v1\\/cache-management\\/health(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache-management/prewarm", "regexp": "/^\\/api\\/v1\\/cache-management\\/prewarm(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache-management/optimize", "regexp": "/^\\/api\\/v1\\/cache-management\\/optimize(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache-management/smart-invalidate", "regexp": "/^\\/api\\/v1\\/cache-management\\/smart-invalidate(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache-management/clear", "regexp": "/^\\/api\\/v1\\/cache-management\\/clear(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/cache-management/hot-keys", "regexp": "/^\\/api\\/v1\\/cache-management\\/hot-keys(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/cache-management/item", "regexp": "/^\\/api\\/v1\\/cache-management\\/item(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache-management/item", "regexp": "/^\\/api\\/v1\\/cache-management\\/item(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [], "path": "/api/v1/cache-management/item", "regexp": "/^\\/api\\/v1\\/cache-management\\/item(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache-management/batch", "regexp": "/^\\/api\\/v1\\/cache-management\\/batch(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/cache-management/config", "regexp": "/^\\/api\\/v1\\/cache-management\\/config(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [], "path": "/api/v1/cache-management/config", "regexp": "/^\\/api\\/v1\\/cache-management\\/config(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/cache-management/reset-stats", "regexp": "/^\\/api\\/v1\\/cache-management\\/reset-stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/cache/status", "regexp": "/^\\/api\\/cache\\/status(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache/refresh", "regexp": "/^\\/api\\/cache\\/refresh(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "dataSource", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/cache/:dataSource", "regexp": "/^\\/api\\/cache\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "dataSource", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/cache/source/:dataSource", "regexp": "/^\\/api\\/cache\\/source\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/cache/refresh-limit", "regexp": "/^\\/api\\/cache\\/refresh-limit(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/cache-stats", "regexp": "/^\\/api\\/cache-stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache-stats/reset", "regexp": "/^\\/api\\/cache-stats\\/reset(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/public/cache-stats", "regexp": "/^\\/api\\/public\\/cache-stats(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/refresh-data", "regexp": "/^\\/api\\/refresh-data(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/smart-recommendation", "regexp": "/^\\/api\\/smart-recommendation(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/smart-recommendation/stats", "regexp": "/^\\/api\\/smart-recommendation\\/stats(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/smart-recommendation/config", "regexp": "/^\\/api\\/smart-recommendation\\/config(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "symbol", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/smart-recommendation/analyze/:symbol", "regexp": "/^\\/api\\/smart-recommendation\\/analyze\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/smart-recommendation/refresh", "regexp": "/^\\/api\\/smart-recommendation\\/refresh(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/refresh-status", "regexp": "/^\\/api\\/refresh-status(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/membership", "regexp": "/^\\/api\\/membership(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/membership/levels", "regexp": "/^\\/api\\/membership\\/levels(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/membership/check-access", "regexp": "/^\\/api\\/membership\\/check-access(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/membership/update", "regexp": "/^\\/api\\/membership\\/update(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/coins", "regexp": "/^\\/api\\/coins(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/coins/transactions", "regexp": "/^\\/api\\/coins\\/transactions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/coins/exchange", "regexp": "/^\\/api\\/coins\\/exchange(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/coins/add", "regexp": "/^\\/api\\/coins\\/add(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/coins/deduct", "regexp": "/^\\/api\\/coins\\/deduct(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/coins/recharge", "regexp": "/^\\/api\\/coins\\/recharge(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/coins/recharge", "regexp": "/^\\/api\\/coins\\/recharge(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/coins/recharge/all", "regexp": "/^\\/api\\/coins\\/recharge\\/all(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "requestId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/coins/recharge/:requestId", "regexp": "/^\\/api\\/coins\\/recharge\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "requestId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/coins/recharge/:requestId/process", "regexp": "/^\\/api\\/coins\\/recharge\\/((?:[^\\/]+?))\\/process(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "requestId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/coins/recharge/:requestId/cancel", "regexp": "/^\\/api\\/coins\\/recharge\\/((?:[^\\/]+?))\\/cancel(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/notifications", "regexp": "/^\\/api\\/notifications(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/notifications/unread-count", "regexp": "/^\\/api\\/notifications\\/unread-count(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "notificationId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/notifications/:notificationId/read", "regexp": "/^\\/api\\/notifications\\/((?:[^\\/]+?))\\/read(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/notifications/read-all", "regexp": "/^\\/api\\/notifications\\/read-all(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "notificationId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/notifications/:notificationId", "regexp": "/^\\/api\\/notifications\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/admin/users", "regexp": "/^\\/api\\/admin\\/users(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "userId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/admin/users/:userId", "regexp": "/^\\/api\\/admin\\/users\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "userId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/admin/users/:userId", "regexp": "/^\\/api\\/admin\\/users\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PATCH"], "paramNames": [{"name": "userId", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/admin/users/:userId/status", "regexp": "/^\\/api\\/admin\\/users\\/((?:[^\\/]+?))\\/status(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/admin/stats", "regexp": "/^\\/api\\/admin\\/stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/pages", "regexp": "/^\\/api\\/pages(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/pages/:id", "regexp": "/^\\/api\\/pages\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/pages", "regexp": "/^\\/api\\/pages(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/pages/:id", "regexp": "/^\\/api\\/pages\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/pages/:id", "regexp": "/^\\/api\\/pages\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/pages/:id/permissions", "regexp": "/^\\/api\\/pages\\/((?:[^\\/]+?))\\/permissions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/pages/batch-status", "regexp": "/^\\/api\\/pages\\/batch-status(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/pages/init", "regexp": "/^\\/api\\/pages\\/init(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/user-menu", "regexp": "/^\\/api\\/user-menu(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/check-page-access", "regexp": "/^\\/api\\/check-page-access(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/page-stats/summary", "regexp": "/^\\/api\\/page-stats\\/summary(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/page-stats/stats", "regexp": "/^\\/api\\/page-stats\\/stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/page-stats/logs", "regexp": "/^\\/api\\/page-stats\\/logs(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/page-stats/log", "regexp": "/^\\/api\\/page-stats\\/log(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/page-stats/duration", "regexp": "/^\\/api\\/page-stats\\/duration(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/page-groups", "regexp": "/^\\/api\\/page-groups(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/page-groups/:id", "regexp": "/^\\/api\\/page-groups\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/page-groups", "regexp": "/^\\/api\\/page-groups(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/page-groups/:id", "regexp": "/^\\/api\\/page-groups\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/page-groups/:id", "regexp": "/^\\/api\\/page-groups\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/page-groups/:id/permissions", "regexp": "/^\\/api\\/page-groups\\/((?:[^\\/]+?))\\/permissions(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/permission-templates", "regexp": "/^\\/api\\/permission-templates(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/permission-templates/:id", "regexp": "/^\\/api\\/permission-templates\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/permission-templates", "regexp": "/^\\/api\\/permission-templates(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/permission-templates/:id", "regexp": "/^\\/api\\/permission-templates\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/permission-templates/:id", "regexp": "/^\\/api\\/permission-templates\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/permission-templates/apply-to-page", "regexp": "/^\\/api\\/permission-templates\\/apply-to-page(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/permission-templates/apply-to-group", "regexp": "/^\\/api\\/permission-templates\\/apply-to-group(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/logs/data-source", "regexp": "/^\\/api\\/logs\\/data-source(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/logs/data-source/recent", "regexp": "/^\\/api\\/logs\\/data-source\\/recent(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/logs/data-source", "regexp": "/^\\/api\\/logs\\/data-source(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [], "path": "/api/logs/data-source", "regexp": "/^\\/api\\/logs\\/data-source(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "source", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/cache/stats/:source", "regexp": "/^\\/api\\/cache\\/stats\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "source", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/cache/details/:source", "regexp": "/^\\/api\\/cache\\/details\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "dataSource", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/cache/source/:dataSource", "regexp": "/^\\/api\\/cache\\/source\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [], "path": "/api/cache/key", "regexp": "/^\\/api\\/cache\\/key(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache/expire", "regexp": "/^\\/api\\/cache\\/expire(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache/clean/time", "regexp": "/^\\/api\\/cache\\/clean\\/time(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache/clean/capacity", "regexp": "/^\\/api\\/cache\\/clean\\/capacity(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/cache/clean/auto", "regexp": "/^\\/api\\/cache\\/clean\\/auto(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/factor/calculate", "regexp": "/^\\/api\\/factor\\/calculate(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/factor/batch-calculate", "regexp": "/^\\/api\\/factor\\/batch-calculate(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/factor/configs", "regexp": "/^\\/api\\/factor\\/configs(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/factor/correlation", "regexp": "/^\\/api\\/factor\\/correlation(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/factor/importance", "regexp": "/^\\/api\\/factor\\/importance(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/factor/statistics", "regexp": "/^\\/api\\/factor\\/statistics(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/factor/cache/clear", "regexp": "/^\\/api\\/factor\\/cache\\/clear(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/factor/cache/stats", "regexp": "/^\\/api\\/factor\\/cache\\/stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/strategy", "regexp": "/^\\/api\\/strategy(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/strategy", "regexp": "/^\\/api\\/strategy(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/strategy/templates", "regexp": "/^\\/api\\/strategy\\/templates(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/strategy/:id", "regexp": "/^\\/api\\/strategy\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/strategy/:id", "regexp": "/^\\/api\\/strategy\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/strategy/:id", "regexp": "/^\\/api\\/strategy\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/strategy/:id/execute", "regexp": "/^\\/api\\/strategy\\/((?:[^\\/]+?))\\/execute(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/strategy/:id/optimize", "regexp": "/^\\/api\\/strategy\\/((?:[^\\/]+?))\\/optimize(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/strategy/:id/toggle", "regexp": "/^\\/api\\/strategy\\/((?:[^\\/]+?))\\/toggle(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/strategy/:id/clone", "regexp": "/^\\/api\\/strategy\\/((?:[^\\/]+?))\\/clone(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/strategy/:id/history", "regexp": "/^\\/api\\/strategy\\/((?:[^\\/]+?))\\/history(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/strategy/:id/performance", "regexp": "/^\\/api\\/strategy\\/((?:[^\\/]+?))\\/performance(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/strategy/:id/risk", "regexp": "/^\\/api\\/strategy\\/((?:[^\\/]+?))\\/risk(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/strategy/batch-execute", "regexp": "/^\\/api\\/strategy\\/batch-execute(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/backtest/run", "regexp": "/^\\/api\\/backtest\\/run(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/backtest/batch", "regexp": "/^\\/api\\/backtest\\/batch(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/backtest/history", "regexp": "/^\\/api\\/backtest\\/history(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/backtest/:id", "regexp": "/^\\/api\\/backtest\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/backtest/:id", "regexp": "/^\\/api\\/backtest\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/backtest/compare", "regexp": "/^\\/api\\/backtest\\/compare(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/backtest/templates", "regexp": "/^\\/api\\/backtest\\/templates(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/risk/config", "regexp": "/^\\/api\\/risk\\/config(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/risk/config", "regexp": "/^\\/api\\/risk\\/config(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/risk/config/:id", "regexp": "/^\\/api\\/risk\\/config\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/risk/config/:id", "regexp": "/^\\/api\\/risk\\/config\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/risk/var/calculate", "regexp": "/^\\/api\\/risk\\/var\\/calculate(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/risk/var/batch-calculate", "regexp": "/^\\/api\\/risk\\/var\\/batch-calculate(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/risk/var/history", "regexp": "/^\\/api\\/risk\\/var\\/history(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/risk/var/:id", "regexp": "/^\\/api\\/risk\\/var\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/risk/dashboard", "regexp": "/^\\/api\\/risk\\/dashboard(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/stress-test/scenario", "regexp": "/^\\/api\\/stress-test\\/scenario(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stress-test/scenario", "regexp": "/^\\/api\\/stress-test\\/scenario(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stress-test/scenario/:id", "regexp": "/^\\/api\\/stress-test\\/scenario\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stress-test/scenario/:id", "regexp": "/^\\/api\\/stress-test\\/scenario\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/stress-test/run", "regexp": "/^\\/api\\/stress-test\\/run(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/stress-test/batch-run", "regexp": "/^\\/api\\/stress-test\\/batch-run(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stress-test/history", "regexp": "/^\\/api\\/stress-test\\/history(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stress-test/:id", "regexp": "/^\\/api\\/stress-test\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stress-test/templates", "regexp": "/^\\/api\\/stress-test\\/templates(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stress-test/dashboard", "regexp": "/^\\/api\\/stress-test\\/dashboard(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/risk-alert/rule", "regexp": "/^\\/api\\/risk-alert\\/rule(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/risk-alert/rule", "regexp": "/^\\/api\\/risk-alert\\/rule(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/risk-alert/rule/:id", "regexp": "/^\\/api\\/risk-alert\\/rule\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/risk-alert/rule/:id", "regexp": "/^\\/api\\/risk-alert\\/rule\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/risk-alert/monitor", "regexp": "/^\\/api\\/risk-alert\\/monitor(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/risk-alert/history", "regexp": "/^\\/api\\/risk-alert\\/history(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/risk-alert/:id/resolve", "regexp": "/^\\/api\\/risk-alert\\/((?:[^\\/]+?))\\/resolve(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/risk-alert/batch-resolve", "regexp": "/^\\/api\\/risk-alert\\/batch-resolve(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/risk-alert/status", "regexp": "/^\\/api\\/risk-alert\\/status(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/risk-alert/dashboard", "regexp": "/^\\/api\\/risk-alert\\/dashboard(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/risk-alert/templates", "regexp": "/^\\/api\\/risk-alert\\/templates(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/stop-loss/config", "regexp": "/^\\/api\\/stop-loss\\/config(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stop-loss/config", "regexp": "/^\\/api\\/stop-loss\\/config(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stop-loss/config/:id", "regexp": "/^\\/api\\/stop-loss\\/config\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stop-loss/config/:id", "regexp": "/^\\/api\\/stop-loss\\/config\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/stop-loss/order", "regexp": "/^\\/api\\/stop-loss\\/order(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/stop-loss/order", "regexp": "/^\\/api\\/stop-loss\\/order(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stop-loss/order/:id/cancel", "regexp": "/^\\/api\\/stop-loss\\/order\\/((?:[^\\/]+?))\\/cancel(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/stop-loss/check-triggers", "regexp": "/^\\/api\\/stop-loss\\/check-triggers(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/stop-loss/order/:id/execute", "regexp": "/^\\/api\\/stop-loss\\/order\\/((?:[^\\/]+?))\\/execute(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/moneyflow/hsgt", "regexp": "/^\\/api\\/moneyflow\\/hsgt(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/moneyflow/stock", "regexp": "/^\\/api\\/moneyflow\\/stock(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/moneyflow/market", "regexp": "/^\\/api\\/moneyflow\\/market(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/limitlist/up", "regexp": "/^\\/api\\/limitlist\\/up(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/limitlist/down", "regexp": "/^\\/api\\/limitlist\\/down(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/limitlist/zhaban", "regexp": "/^\\/api\\/limitlist\\/zhaban(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/limitlist/overview", "regexp": "/^\\/api\\/limitlist\\/overview(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/screener/doji/upward", "regexp": "/^\\/api\\/v1\\/screener\\/doji\\/upward(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/patterns/doji/price-movement", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/price-movement(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/patterns/doji/stats", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/stats(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/patterns/doji/recent", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/recent(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/patterns/doji/alerts", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/patterns/doji/alerts", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/patterns/doji/alerts/:id", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/v1/patterns/doji/alerts/:id", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/doji-alerts", "regexp": "/^\\/api\\/doji-alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/doji-alerts", "regexp": "/^\\/api\\/doji-alerts(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["PUT"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/doji-alerts/:id", "regexp": "/^\\/api\\/doji-alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [{"name": "id", "prefix": "/", "delimiter": "/", "optional": false, "repeat": false, "partial": false, "asterisk": false, "pattern": "[^\\/]+?"}], "path": "/api/doji-alerts/:id", "regexp": "/^\\/api\\/doji-alerts\\/((?:[^\\/]+?))(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/patterns/doji/batch", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/batch(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/patterns/doji/cache-stats", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/cache-stats(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/patterns/doji/clean-cache", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/clean-cache(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/patterns/doji/settings", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/settings(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["POST"], "paramNames": [], "path": "/api/v1/patterns/doji/settings", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/settings(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["DELETE"], "paramNames": [], "path": "/api/v1/patterns/doji/settings", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/settings(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/patterns/doji/settings/presets", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/settings\\/presets(?:\\/(?=$))?$/", "stack": ["wrappedController"]}, {"name": null, "methods": ["HEAD", "GET"], "paramNames": [], "path": "/api/v1/patterns/doji/settings/performance", "regexp": "/^\\/api\\/v1\\/patterns\\/doji\\/settings\\/performance(?:\\/(?=$))?$/", "stack": ["auth", "wrappedController"]}]