[{"name": "Process Start", "start": 1753511604933, "end": 1753511606775, "duration": 1842, "pid": 24460, "index": 0}, {"name": "Application Start", "start": 1753511606777, "end": 1753511608883, "duration": 2106, "pid": 24460, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753511606796, "end": 1753511606832, "duration": 36, "pid": 24460, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753511606832, "end": 1753511606887, "duration": 55, "pid": 24460, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753511606833, "end": 1753511606835, "duration": 2, "pid": 24460, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753511606841, "end": 1753511606842, "duration": 1, "pid": 24460, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753511606844, "end": 1753511606844, "duration": 0, "pid": 24460, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753511606845, "end": 1753511606846, "duration": 1, "pid": 24460, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753511606847, "end": 1753511606848, "duration": 1, "pid": 24460, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753511606849, "end": 1753511606849, "duration": 0, "pid": 24460, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753511606850, "end": 1753511606850, "duration": 0, "pid": 24460, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753511606852, "end": 1753511606852, "duration": 0, "pid": 24460, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753511606853, "end": 1753511606854, "duration": 1, "pid": 24460, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753511606854, "end": 1753511606855, "duration": 1, "pid": 24460, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753511606856, "end": 1753511606857, "duration": 1, "pid": 24460, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753511606858, "end": 1753511606859, "duration": 1, "pid": 24460, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753511606859, "end": 1753511606860, "duration": 1, "pid": 24460, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753511606861, "end": 1753511606861, "duration": 0, "pid": 24460, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753511606862, "end": 1753511606863, "duration": 1, "pid": 24460, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753511606864, "end": 1753511606864, "duration": 0, "pid": 24460, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753511606865, "end": 1753511606866, "duration": 1, "pid": 24460, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753511606867, "end": 1753511606867, "duration": 0, "pid": 24460, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753511606868, "end": 1753511606869, "duration": 1, "pid": 24460, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753511606870, "end": 1753511606870, "duration": 0, "pid": 24460, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753511606872, "end": 1753511606872, "duration": 0, "pid": 24460, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753511606875, "end": 1753511606875, "duration": 0, "pid": 24460, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753511606878, "end": 1753511606878, "duration": 0, "pid": 24460, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753511606881, "end": 1753511606882, "duration": 1, "pid": 24460, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753511606886, "end": 1753511606886, "duration": 0, "pid": 24460, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753511606886, "end": 1753511606886, "duration": 0, "pid": 24460, "index": 29}, {"name": "Load extend/application.js", "start": 1753511606887, "end": 1753511607004, "duration": 117, "pid": 24460, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1753511606888, "end": 1753511606889, "duration": 1, "pid": 24460, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1753511606890, "end": 1753511606892, "duration": 2, "pid": 24460, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1753511606893, "end": 1753511606900, "duration": 7, "pid": 24460, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1753511606902, "end": 1753511606911, "duration": 9, "pid": 24460, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1753511606913, "end": 1753511606915, "duration": 2, "pid": 24460, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1753511606916, "end": 1753511606918, "duration": 2, "pid": 24460, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1753511606920, "end": 1753511606987, "duration": 67, "pid": 24460, "index": 37}, {"name": "Require(33) app/extend/application.js", "start": 1753511606990, "end": 1753511606995, "duration": 5, "pid": 24460, "index": 38}, {"name": "Load extend/request.js", "start": 1753511607004, "end": 1753511607029, "duration": 25, "pid": 24460, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1753511607016, "end": 1753511607018, "duration": 2, "pid": 24460, "index": 40}, {"name": "Load extend/response.js", "start": 1753511607029, "end": 1753511607051, "duration": 22, "pid": 24460, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1753511607038, "end": 1753511607042, "duration": 4, "pid": 24460, "index": 42}, {"name": "Load extend/context.js", "start": 1753511607051, "end": 1753511607132, "duration": 81, "pid": 24460, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1753511607052, "end": 1753511607071, "duration": 19, "pid": 24460, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1753511607072, "end": 1753511607077, "duration": 5, "pid": 24460, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1753511607079, "end": 1753511607079, "duration": 0, "pid": 24460, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1753511607082, "end": 1753511607108, "duration": 26, "pid": 24460, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1753511607111, "end": 1753511607113, "duration": 2, "pid": 24460, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753511607116, "end": 1753511607117, "duration": 1, "pid": 24460, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1753511607118, "end": 1753511607121, "duration": 3, "pid": 24460, "index": 50}, {"name": "Require(43) app/extend/context.js", "start": 1753511607122, "end": 1753511607123, "duration": 1, "pid": 24460, "index": 51}, {"name": "Load extend/helper.js", "start": 1753511607132, "end": 1753511607178, "duration": 46, "pid": 24460, "index": 52}, {"name": "Require(44) node_modules/egg-security/app/extend/helper.js", "start": 1753511607134, "end": 1753511607160, "duration": 26, "pid": 24460, "index": 53}, {"name": "Require(45) node_modules/egg/app/extend/helper.js", "start": 1753511607168, "end": 1753511607168, "duration": 0, "pid": 24460, "index": 54}, {"name": "Require(46) app/extend/helper.js", "start": 1753511607169, "end": 1753511607170, "duration": 1, "pid": 24460, "index": 55}, {"name": "Load app.js", "start": 1753511607178, "end": 1753511607270, "duration": 92, "pid": 24460, "index": 56}, {"name": "Require(47) node_modules/egg-session/app.js", "start": 1753511607179, "end": 1753511607179, "duration": 0, "pid": 24460, "index": 57}, {"name": "Require(48) node_modules/egg-security/app.js", "start": 1753511607180, "end": 1753511607183, "duration": 3, "pid": 24460, "index": 58}, {"name": "Require(49) node_modules/egg-onerror/app.js", "start": 1753511607184, "end": 1753511607198, "duration": 14, "pid": 24460, "index": 59}, {"name": "Require(50) node_modules/egg-i18n/app.js", "start": 1753511607199, "end": 1753511607214, "duration": 15, "pid": 24460, "index": 60}, {"name": "Require(51) node_modules/egg-watcher/app.js", "start": 1753511607215, "end": 1753511607229, "duration": 14, "pid": 24460, "index": 61}, {"name": "Require(52) node_modules/egg-schedule/app.js", "start": 1753511607230, "end": 1753511607231, "duration": 1, "pid": 24460, "index": 62}, {"name": "Require(53) node_modules/egg-multipart/app.js", "start": 1753511607232, "end": 1753511607235, "duration": 3, "pid": 24460, "index": 63}, {"name": "Require(54) node_modules/egg-development/app.js", "start": 1753511607236, "end": 1753511607236, "duration": 0, "pid": 24460, "index": 64}, {"name": "Require(55) node_modules/egg-logrotator/app.js", "start": 1753511607237, "end": 1753511607237, "duration": 0, "pid": 24460, "index": 65}, {"name": "Require(56) node_modules/egg-static/app.js", "start": 1753511607238, "end": 1753511607238, "duration": 0, "pid": 24460, "index": 66}, {"name": "Require(57) node_modules/egg-sequelize/app.js", "start": 1753511607240, "end": 1753511607241, "duration": 1, "pid": 24460, "index": 67}, {"name": "Require(58) node_modules/egg-jwt/app.js", "start": 1753511607241, "end": 1753511607242, "duration": 1, "pid": 24460, "index": 68}, {"name": "Require(59) node_modules/egg-cors/app.js", "start": 1753511607243, "end": 1753511607244, "duration": 1, "pid": 24460, "index": 69}, {"name": "Require(60) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1753511607245, "end": 1753511607248, "duration": 3, "pid": 24460, "index": 70}, {"name": "Require(61) node_modules/egg-mysql/app.js", "start": 1753511607248, "end": 1753511607268, "duration": 20, "pid": 24460, "index": 71}, {"name": "Require(62) app.js", "start": 1753511607270, "end": 1753511607270, "duration": 0, "pid": 24460, "index": 72}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753511607283, "end": 1753511608871, "duration": 1588, "pid": 24460, "index": 73}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753511607962, "end": 1753511608083, "duration": 121, "pid": 24460, "index": 74}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753511607994, "end": 1753511608828, "duration": 834, "pid": 24460, "index": 75}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753511608146, "end": 1753511608881, "duration": 735, "pid": 24460, "index": 76}, {"name": "Did Load in app.js:didLoad", "start": 1753511608148, "end": 1753511608636, "duration": 488, "pid": 24460, "index": 77}, {"name": "Load Service", "start": 1753511608148, "end": 1753511608314, "duration": 166, "pid": 24460, "index": 78}, {"name": "Load \"service\" to Context", "start": 1753511608149, "end": 1753511608314, "duration": 165, "pid": 24460, "index": 79}, {"name": "Load Middleware", "start": 1753511608314, "end": 1753511608477, "duration": 163, "pid": 24460, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1753511608314, "end": 1753511608462, "duration": 148, "pid": 24460, "index": 81}, {"name": "Load Controller", "start": 1753511608477, "end": 1753511608596, "duration": 119, "pid": 24460, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1753511608477, "end": 1753511608596, "duration": 119, "pid": 24460, "index": 83}, {"name": "Load Router", "start": 1753511608596, "end": 1753511608617, "duration": 21, "pid": 24460, "index": 84}, {"name": "Require(63) app/router.js", "start": 1753511608597, "end": 1753511608598, "duration": 1, "pid": 24460, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1753511608599, "end": 1753511608635, "duration": 36, "pid": 24460, "index": 86}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1753511608882, "end": 1753511608882, "duration": 0, "pid": 24460, "index": 87}]