[{"name": "Process Start", "start": 1753536112511, "end": 1753536114184, "duration": 1673, "pid": 32276, "index": 0}, {"name": "Application Start", "start": 1753536114186, "end": 1753536116138, "duration": 1952, "pid": 32276, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753536114202, "end": 1753536114237, "duration": 35, "pid": 32276, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753536114237, "end": 1753536114289, "duration": 52, "pid": 32276, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753536114238, "end": 1753536114239, "duration": 1, "pid": 32276, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753536114243, "end": 1753536114244, "duration": 1, "pid": 32276, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753536114245, "end": 1753536114246, "duration": 1, "pid": 32276, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753536114247, "end": 1753536114247, "duration": 0, "pid": 32276, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753536114249, "end": 1753536114250, "duration": 1, "pid": 32276, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753536114251, "end": 1753536114251, "duration": 0, "pid": 32276, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753536114252, "end": 1753536114253, "duration": 1, "pid": 32276, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753536114253, "end": 1753536114254, "duration": 1, "pid": 32276, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753536114255, "end": 1753536114255, "duration": 0, "pid": 32276, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753536114256, "end": 1753536114257, "duration": 1, "pid": 32276, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753536114258, "end": 1753536114259, "duration": 1, "pid": 32276, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753536114260, "end": 1753536114260, "duration": 0, "pid": 32276, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753536114261, "end": 1753536114262, "duration": 1, "pid": 32276, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753536114263, "end": 1753536114264, "duration": 1, "pid": 32276, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753536114265, "end": 1753536114266, "duration": 1, "pid": 32276, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753536114266, "end": 1753536114267, "duration": 1, "pid": 32276, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753536114268, "end": 1753536114268, "duration": 0, "pid": 32276, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753536114269, "end": 1753536114270, "duration": 1, "pid": 32276, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753536114271, "end": 1753536114271, "duration": 0, "pid": 32276, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753536114272, "end": 1753536114273, "duration": 1, "pid": 32276, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753536114275, "end": 1753536114275, "duration": 0, "pid": 32276, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753536114278, "end": 1753536114278, "duration": 0, "pid": 32276, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753536114281, "end": 1753536114281, "duration": 0, "pid": 32276, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753536114284, "end": 1753536114285, "duration": 1, "pid": 32276, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753536114288, "end": 1753536114289, "duration": 1, "pid": 32276, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753536114289, "end": 1753536114289, "duration": 0, "pid": 32276, "index": 29}, {"name": "Load extend/application.js", "start": 1753536114290, "end": 1753536114395, "duration": 105, "pid": 32276, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1753536114291, "end": 1753536114293, "duration": 2, "pid": 32276, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1753536114293, "end": 1753536114295, "duration": 2, "pid": 32276, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1753536114296, "end": 1753536114302, "duration": 6, "pid": 32276, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1753536114304, "end": 1753536114311, "duration": 7, "pid": 32276, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1753536114313, "end": 1753536114316, "duration": 3, "pid": 32276, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1753536114317, "end": 1753536114319, "duration": 2, "pid": 32276, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1753536114321, "end": 1753536114380, "duration": 59, "pid": 32276, "index": 37}, {"name": "Require(33) app/extend/application.js", "start": 1753536114383, "end": 1753536114386, "duration": 3, "pid": 32276, "index": 38}, {"name": "Load extend/request.js", "start": 1753536114395, "end": 1753536114416, "duration": 21, "pid": 32276, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1753536114404, "end": 1753536114406, "duration": 2, "pid": 32276, "index": 40}, {"name": "Load extend/response.js", "start": 1753536114416, "end": 1753536114436, "duration": 20, "pid": 32276, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1753536114424, "end": 1753536114427, "duration": 3, "pid": 32276, "index": 42}, {"name": "Load extend/context.js", "start": 1753536114436, "end": 1753536114505, "duration": 69, "pid": 32276, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1753536114437, "end": 1753536114454, "duration": 17, "pid": 32276, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1753536114455, "end": 1753536114457, "duration": 2, "pid": 32276, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1753536114459, "end": 1753536114460, "duration": 1, "pid": 32276, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1753536114461, "end": 1753536114485, "duration": 24, "pid": 32276, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1753536114486, "end": 1753536114488, "duration": 2, "pid": 32276, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753536114490, "end": 1753536114490, "duration": 0, "pid": 32276, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1753536114492, "end": 1753536114495, "duration": 3, "pid": 32276, "index": 50}, {"name": "Require(43) app/extend/context.js", "start": 1753536114496, "end": 1753536114496, "duration": 0, "pid": 32276, "index": 51}, {"name": "Load extend/helper.js", "start": 1753536114505, "end": 1753536114549, "duration": 44, "pid": 32276, "index": 52}, {"name": "Require(44) node_modules/egg-security/app/extend/helper.js", "start": 1753536114506, "end": 1753536114532, "duration": 26, "pid": 32276, "index": 53}, {"name": "Require(45) node_modules/egg/app/extend/helper.js", "start": 1753536114539, "end": 1753536114539, "duration": 0, "pid": 32276, "index": 54}, {"name": "Require(46) app/extend/helper.js", "start": 1753536114540, "end": 1753536114541, "duration": 1, "pid": 32276, "index": 55}, {"name": "Load app.js", "start": 1753536114550, "end": 1753536114626, "duration": 76, "pid": 32276, "index": 56}, {"name": "Require(47) node_modules/egg-session/app.js", "start": 1753536114550, "end": 1753536114551, "duration": 1, "pid": 32276, "index": 57}, {"name": "Require(48) node_modules/egg-security/app.js", "start": 1753536114551, "end": 1753536114554, "duration": 3, "pid": 32276, "index": 58}, {"name": "Require(49) node_modules/egg-onerror/app.js", "start": 1753536114555, "end": 1753536114567, "duration": 12, "pid": 32276, "index": 59}, {"name": "Require(50) node_modules/egg-i18n/app.js", "start": 1753536114568, "end": 1753536114582, "duration": 14, "pid": 32276, "index": 60}, {"name": "Require(51) node_modules/egg-watcher/app.js", "start": 1753536114582, "end": 1753536114595, "duration": 13, "pid": 32276, "index": 61}, {"name": "Require(52) node_modules/egg-schedule/app.js", "start": 1753536114596, "end": 1753536114597, "duration": 1, "pid": 32276, "index": 62}, {"name": "Require(53) node_modules/egg-multipart/app.js", "start": 1753536114597, "end": 1753536114599, "duration": 2, "pid": 32276, "index": 63}, {"name": "Require(54) node_modules/egg-development/app.js", "start": 1753536114600, "end": 1753536114600, "duration": 0, "pid": 32276, "index": 64}, {"name": "Require(55) node_modules/egg-logrotator/app.js", "start": 1753536114601, "end": 1753536114601, "duration": 0, "pid": 32276, "index": 65}, {"name": "Require(56) node_modules/egg-static/app.js", "start": 1753536114602, "end": 1753536114602, "duration": 0, "pid": 32276, "index": 66}, {"name": "Require(57) node_modules/egg-sequelize/app.js", "start": 1753536114604, "end": 1753536114604, "duration": 0, "pid": 32276, "index": 67}, {"name": "Require(58) node_modules/egg-jwt/app.js", "start": 1753536114604, "end": 1753536114605, "duration": 1, "pid": 32276, "index": 68}, {"name": "Require(59) node_modules/egg-cors/app.js", "start": 1753536114605, "end": 1753536114606, "duration": 1, "pid": 32276, "index": 69}, {"name": "Require(60) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1753536114606, "end": 1753536114609, "duration": 3, "pid": 32276, "index": 70}, {"name": "Require(61) node_modules/egg-mysql/app.js", "start": 1753536114609, "end": 1753536114624, "duration": 15, "pid": 32276, "index": 71}, {"name": "Require(62) app.js", "start": 1753536114626, "end": 1753536114626, "duration": 0, "pid": 32276, "index": 72}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753536114637, "end": 1753536116123, "duration": 1486, "pid": 32276, "index": 73}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753536115244, "end": 1753536115365, "duration": 121, "pid": 32276, "index": 74}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753536115272, "end": 1753536116083, "duration": 811, "pid": 32276, "index": 75}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753536115415, "end": 1753536116136, "duration": 721, "pid": 32276, "index": 76}, {"name": "Did Load in app.js:didLoad", "start": 1753536115416, "end": 1753536115894, "duration": 478, "pid": 32276, "index": 77}, {"name": "Load Service", "start": 1753536115416, "end": 1753536115575, "duration": 159, "pid": 32276, "index": 78}, {"name": "Load \"service\" to Context", "start": 1753536115416, "end": 1753536115575, "duration": 159, "pid": 32276, "index": 79}, {"name": "Load Middleware", "start": 1753536115575, "end": 1753536115740, "duration": 165, "pid": 32276, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1753536115576, "end": 1753536115727, "duration": 151, "pid": 32276, "index": 81}, {"name": "Load Controller", "start": 1753536115740, "end": 1753536115844, "duration": 104, "pid": 32276, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1753536115740, "end": 1753536115844, "duration": 104, "pid": 32276, "index": 83}, {"name": "Load Router", "start": 1753536115844, "end": 1753536115867, "duration": 23, "pid": 32276, "index": 84}, {"name": "Require(63) app/router.js", "start": 1753536115845, "end": 1753536115846, "duration": 1, "pid": 32276, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1753536115848, "end": 1753536115893, "duration": 45, "pid": 32276, "index": 86}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1753536116137, "end": 1753536116137, "duration": 0, "pid": 32276, "index": 87}]