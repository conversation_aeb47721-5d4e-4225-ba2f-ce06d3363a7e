<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/src/image/logo/logo-T.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
    <meta name="theme-color" content="#4CAF50" />
    <meta name="description" content="股票分析和投资组合管理工具" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
    <title>快乐股市</title>
    <!-- 辅助功能元数据 -->
    <meta name="application-name" content="快乐股市" />
    <meta name="apple-mobile-web-app-title" content="快乐股市" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <!-- 预连接到关键域名 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <!-- 离线回退样式 -->
    <style>
      .app-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        background-color: #f5f5f5;
      }
      .app-loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #4CAF50;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 应用加载时的占位内容 -->
      <div class="app-loading">
        <img src="/src/image/logo/logo-T.png" alt="快乐股市" width="80" height="80" />
        <div class="app-loading-spinner"></div>
        <p>加载中...</p>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
    <script>
      // 检查是否支持Service Worker
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          // Service Worker将在main.ts中注册，这里只添加错误处理
          navigator.serviceWorker.ready.catch(error => {
            console.error('Service Worker准备失败:', error);
          });
        });
      }
    </script>
  </body>
</html>
