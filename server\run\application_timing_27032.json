[{"name": "Process Start", "start": 1753511604578, "end": 1753511606599, "duration": 2021, "pid": 27032, "index": 0}, {"name": "Application Start", "start": 1753511606601, "end": 1753511608704, "duration": 2103, "pid": 27032, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753511606617, "end": 1753511606656, "duration": 39, "pid": 27032, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753511606656, "end": 1753511606724, "duration": 68, "pid": 27032, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753511606657, "end": 1753511606659, "duration": 2, "pid": 27032, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753511606663, "end": 1753511606664, "duration": 1, "pid": 27032, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753511606666, "end": 1753511606667, "duration": 1, "pid": 27032, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753511606669, "end": 1753511606669, "duration": 0, "pid": 27032, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753511606671, "end": 1753511606672, "duration": 1, "pid": 27032, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753511606674, "end": 1753511606674, "duration": 0, "pid": 27032, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753511606676, "end": 1753511606677, "duration": 1, "pid": 27032, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753511606678, "end": 1753511606678, "duration": 0, "pid": 27032, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753511606680, "end": 1753511606681, "duration": 1, "pid": 27032, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753511606682, "end": 1753511606683, "duration": 1, "pid": 27032, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753511606685, "end": 1753511606685, "duration": 0, "pid": 27032, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753511606687, "end": 1753511606687, "duration": 0, "pid": 27032, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753511606688, "end": 1753511606689, "duration": 1, "pid": 27032, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753511606691, "end": 1753511606691, "duration": 0, "pid": 27032, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753511606693, "end": 1753511606694, "duration": 1, "pid": 27032, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753511606695, "end": 1753511606696, "duration": 1, "pid": 27032, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753511606697, "end": 1753511606698, "duration": 1, "pid": 27032, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753511606699, "end": 1753511606700, "duration": 1, "pid": 27032, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753511606701, "end": 1753511606701, "duration": 0, "pid": 27032, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753511606702, "end": 1753511606703, "duration": 1, "pid": 27032, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753511606704, "end": 1753511606704, "duration": 0, "pid": 27032, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753511606707, "end": 1753511606708, "duration": 1, "pid": 27032, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753511606714, "end": 1753511606715, "duration": 1, "pid": 27032, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753511606718, "end": 1753511606719, "duration": 1, "pid": 27032, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753511606722, "end": 1753511606723, "duration": 1, "pid": 27032, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753511606724, "end": 1753511606724, "duration": 0, "pid": 27032, "index": 29}, {"name": "Load extend/application.js", "start": 1753511606725, "end": 1753511606825, "duration": 100, "pid": 27032, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1753511606726, "end": 1753511606726, "duration": 0, "pid": 27032, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1753511606727, "end": 1753511606729, "duration": 2, "pid": 27032, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1753511606730, "end": 1753511606736, "duration": 6, "pid": 27032, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1753511606738, "end": 1753511606745, "duration": 7, "pid": 27032, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1753511606747, "end": 1753511606749, "duration": 2, "pid": 27032, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1753511606750, "end": 1753511606752, "duration": 2, "pid": 27032, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1753511606754, "end": 1753511606810, "duration": 56, "pid": 27032, "index": 37}, {"name": "Require(33) app/extend/application.js", "start": 1753511606813, "end": 1753511606816, "duration": 3, "pid": 27032, "index": 38}, {"name": "Load extend/request.js", "start": 1753511606825, "end": 1753511606848, "duration": 23, "pid": 27032, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1753511606833, "end": 1753511606835, "duration": 2, "pid": 27032, "index": 40}, {"name": "Load extend/response.js", "start": 1753511606848, "end": 1753511606870, "duration": 22, "pid": 27032, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1753511606856, "end": 1753511606860, "duration": 4, "pid": 27032, "index": 42}, {"name": "Load extend/context.js", "start": 1753511606870, "end": 1753511606949, "duration": 79, "pid": 27032, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1753511606871, "end": 1753511606888, "duration": 17, "pid": 27032, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1753511606889, "end": 1753511606891, "duration": 2, "pid": 27032, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1753511606893, "end": 1753511606894, "duration": 1, "pid": 27032, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1753511606896, "end": 1753511606923, "duration": 27, "pid": 27032, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1753511606926, "end": 1753511606928, "duration": 2, "pid": 27032, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753511606931, "end": 1753511606932, "duration": 1, "pid": 27032, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1753511606934, "end": 1753511606939, "duration": 5, "pid": 27032, "index": 50}, {"name": "Require(43) app/extend/context.js", "start": 1753511606940, "end": 1753511606941, "duration": 1, "pid": 27032, "index": 51}, {"name": "Load extend/helper.js", "start": 1753511606950, "end": 1753511607002, "duration": 52, "pid": 27032, "index": 52}, {"name": "Require(44) node_modules/egg-security/app/extend/helper.js", "start": 1753511606951, "end": 1753511606981, "duration": 30, "pid": 27032, "index": 53}, {"name": "Require(45) node_modules/egg/app/extend/helper.js", "start": 1753511606989, "end": 1753511606990, "duration": 1, "pid": 27032, "index": 54}, {"name": "Require(46) app/extend/helper.js", "start": 1753511606991, "end": 1753511606992, "duration": 1, "pid": 27032, "index": 55}, {"name": "Load app.js", "start": 1753511607002, "end": 1753511607092, "duration": 90, "pid": 27032, "index": 56}, {"name": "Require(47) node_modules/egg-session/app.js", "start": 1753511607003, "end": 1753511607003, "duration": 0, "pid": 27032, "index": 57}, {"name": "Require(48) node_modules/egg-security/app.js", "start": 1753511607004, "end": 1753511607007, "duration": 3, "pid": 27032, "index": 58}, {"name": "Require(49) node_modules/egg-onerror/app.js", "start": 1753511607009, "end": 1753511607024, "duration": 15, "pid": 27032, "index": 59}, {"name": "Require(50) node_modules/egg-i18n/app.js", "start": 1753511607025, "end": 1753511607040, "duration": 15, "pid": 27032, "index": 60}, {"name": "Require(51) node_modules/egg-watcher/app.js", "start": 1753511607041, "end": 1753511607054, "duration": 13, "pid": 27032, "index": 61}, {"name": "Require(52) node_modules/egg-schedule/app.js", "start": 1753511607055, "end": 1753511607056, "duration": 1, "pid": 27032, "index": 62}, {"name": "Require(53) node_modules/egg-multipart/app.js", "start": 1753511607057, "end": 1753511607060, "duration": 3, "pid": 27032, "index": 63}, {"name": "Require(54) node_modules/egg-development/app.js", "start": 1753511607060, "end": 1753511607061, "duration": 1, "pid": 27032, "index": 64}, {"name": "Require(55) node_modules/egg-logrotator/app.js", "start": 1753511607061, "end": 1753511607062, "duration": 1, "pid": 27032, "index": 65}, {"name": "Require(56) node_modules/egg-static/app.js", "start": 1753511607062, "end": 1753511607063, "duration": 1, "pid": 27032, "index": 66}, {"name": "Require(57) node_modules/egg-sequelize/app.js", "start": 1753511607064, "end": 1753511607065, "duration": 1, "pid": 27032, "index": 67}, {"name": "Require(58) node_modules/egg-jwt/app.js", "start": 1753511607066, "end": 1753511607066, "duration": 0, "pid": 27032, "index": 68}, {"name": "Require(59) node_modules/egg-cors/app.js", "start": 1753511607067, "end": 1753511607068, "duration": 1, "pid": 27032, "index": 69}, {"name": "Require(60) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1753511607069, "end": 1753511607074, "duration": 5, "pid": 27032, "index": 70}, {"name": "Require(61) node_modules/egg-mysql/app.js", "start": 1753511607074, "end": 1753511607089, "duration": 15, "pid": 27032, "index": 71}, {"name": "Require(62) app.js", "start": 1753511607091, "end": 1753511607092, "duration": 1, "pid": 27032, "index": 72}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753511607107, "end": 1753511608692, "duration": 1585, "pid": 27032, "index": 73}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753511607786, "end": 1753511607910, "duration": 124, "pid": 27032, "index": 74}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753511607820, "end": 1753511608652, "duration": 832, "pid": 27032, "index": 75}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753511607970, "end": 1753511608702, "duration": 732, "pid": 27032, "index": 76}, {"name": "Did Load in app.js:didLoad", "start": 1753511607970, "end": 1753511608459, "duration": 489, "pid": 27032, "index": 77}, {"name": "Load Service", "start": 1753511607970, "end": 1753511608150, "duration": 180, "pid": 27032, "index": 78}, {"name": "Load \"service\" to Context", "start": 1753511607971, "end": 1753511608150, "duration": 179, "pid": 27032, "index": 79}, {"name": "Load Middleware", "start": 1753511608150, "end": 1753511608314, "duration": 164, "pid": 27032, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1753511608151, "end": 1753511608301, "duration": 150, "pid": 27032, "index": 81}, {"name": "Load Controller", "start": 1753511608315, "end": 1753511608420, "duration": 105, "pid": 27032, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1753511608315, "end": 1753511608420, "duration": 105, "pid": 27032, "index": 83}, {"name": "Load Router", "start": 1753511608420, "end": 1753511608440, "duration": 20, "pid": 27032, "index": 84}, {"name": "Require(63) app/router.js", "start": 1753511608421, "end": 1753511608422, "duration": 1, "pid": 27032, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1753511608424, "end": 1753511608458, "duration": 34, "pid": 27032, "index": 86}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1753511608703, "end": 1753511608703, "duration": 0, "pid": 27032, "index": 87}]