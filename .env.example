# 股票数据源API配置示例
# 复制此文件为 .env.local 并填入真实的API密钥

# ===========================================
# 智兔数服配置
# ===========================================
# 前端配置
VITE_ZHITU_API_KEY=D564FC55-057B-4F6F-932C-C115E78BFAE4
VITE_ZHITU_BASE_URL=https://api.zhitudata.com

# 后端配置（与前端保持一致）
ZHITU_API_KEY=D564FC55-057B-4F6F-932C-C115E78BFAE4
ZHITU_BASE_URL=https://api.zhitudata.com

# ===========================================
# Yahoo Finance配置
# ===========================================
# 免费版本（无需API密钥）
VITE_YAHOO_FINANCE_FREE=true

# 付费版本（RapidAPI）
# VITE_YAHOO_FINANCE_RAPIDAPI_KEY=your_rapidapi_key_here
# VITE_YAHOO_FINANCE_RAPIDAPI_HOST=yahoo-finance1.p.rapidapi.com

# ===========================================
# Google Finance配置（使用Alpha Vantage替代）
# ===========================================
VITE_ALPHA_VANTAGE_API_KEY=f6235795d0b5310a44d87a6a41cd9dfc-c-app
VITE_ALPHA_VANTAGE_BASE_URL=https://www.alphavantage.co

# ===========================================
# 聚合数据配置
# ===========================================
VITE_JUHE_API_KEY=4191aa94e0f3ba88c66b827fbbe56624
VITE_JUHE_BASE_URL=http://web.juhe.cn/finance

# ===========================================
# 通用API配置
# ===========================================
VITE_API_REQUEST_TIMEOUT=10000
VITE_API_RETRY_COUNT=3
VITE_ENABLE_API_CACHE=true
VITE_CACHE_DURATION=300000
VITE_MAX_CONCURRENT_REQUESTS=10

# ===========================================
# 开发环境配置
# ===========================================
# 是否启用API配置检查
VITE_ENABLE_CONFIG_CHECK=true

# 是否显示详细的API日志
VITE_ENABLE_API_LOGS=true

# 是否启用API性能监控
VITE_ENABLE_API_MONITORING=true
