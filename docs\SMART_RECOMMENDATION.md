# 🤖 智能股票推荐功能说明

## 功能概述

智能股票推荐功能是基于技术分析、量价关系和趋势识别的股票推荐系统，通过多维度算法分析为用户提供个性化的投资建议。

## 核心特性

### 🎯 智能推荐算法
- **技术分析评分 (40%)**：集成MA、MACD、RSI、KDJ、布林带等技术指标
- **量价分析评分 (30%)**：分析成交量与价格的配合关系
- **趋势分析评分 (20%)**：识别短期和中期趋势方向
- **动量分析评分 (10%)**：评估价格和成交量动量

### 📊 推荐结果格式
- 股票基本信息（代码、名称、当前价格）
- 综合评分和分项评分
- 推荐等级（强烈买入/买入/适度买入/持有观望）
- 风险等级评估（低/中/高风险）
- 预期收益率和目标价位
- 详细推荐理由
- 具体交易建议

### 💡 买卖时机提示
- **买入建议**：建议买入价格区间和最优价位
- **止损设置**：智能止损价位建议
- **持有周期**：根据风险等级推荐持有时间
- **仓位管理**：基于风险等级的仓位建议

## 使用方法

### 1. 访问推荐页面
访问 `/smart-recommendation` 路径进入智能推荐页面。

### 2. 配置推荐参数
- **风险偏好**：选择低风险、中等风险或高风险
- **预期收益**：设置期望的收益率目标
- **投资周期**：选择3天、7天、15天或30天
- **推荐数量**：设置需要的推荐股票数量

### 3. 获取推荐结果
点击"获取推荐"按钮，系统将分析股票池并返回推荐结果。

### 4. 查看推荐详情
每个推荐股票包含：
- 综合评分和分项评分
- 推荐理由和风险提示
- 具体的买卖建议
- 目标价位和预期收益

## API接口说明

### 获取推荐列表
```
GET /api/smart-recommendation
```

**参数：**
- `riskLevel`: 风险等级 (low/medium/high)
- `expectedReturn`: 预期收益率 (0.02-0.12)
- `timeHorizon`: 投资周期天数 (3/7/15/30)
- `limit`: 推荐数量限制 (1-20)

**响应：**
```json
{
  "success": true,
  "data": [
    {
      "symbol": "000001.SZ",
      "name": "平安银行",
      "currentPrice": 12.50,
      "totalScore": 85,
      "recommendation": "buy",
      "riskLevel": "medium",
      "expectedReturn": 0.08,
      "targetPrice": {
        "target": 13.50,
        "upside": 8.0,
        "confidence": "高"
      },
      "tradingAdvice": {
        "buyPriceRange": {
          "min": 12.25,
          "max": 12.75,
          "optimal": 12.50
        },
        "stopLoss": 11.50,
        "holdingPeriod": "5-10个交易日"
      },
      "reasons": [
        "技术指标表现优异，多项指标显示买入信号",
        "量价配合良好，成交量支撑价格上涨"
      ]
    }
  ],
  "meta": {
    "totalAnalyzed": 100,
    "qualified": 25,
    "recommended": 10
  }
}
```

### 分析单个股票
```
GET /api/smart-recommendation/analyze/:symbol
```

### 获取推荐统计
```
GET /api/smart-recommendation/stats?days=30
```

### 获取推荐配置
```
GET /api/smart-recommendation/config
```

### 刷新推荐数据
```
POST /api/smart-recommendation/refresh
```

## 会员权限限制

| 会员等级 | 每日推荐数量 | 功能限制 |
|---------|-------------|----------|
| 免费用户 | 3个 | 基础推荐功能 |
| 基础会员 | 8个 | 推荐历史查看 |
| 高级会员 | 无限制 | 全部功能 + 刷新数据 |

## 风险控制措施

### 1. 免责声明
- 明确标注算法建议性质
- 不构成投资建议的声明
- 投资风险提示

### 2. 历史准确率展示
- 显示历史推荐成功率
- 平均收益率统计
- 风险分布情况

### 3. 推荐有效期
- 每个推荐设置7天有效期
- 过期推荐自动标记
- 建议及时更新数据

## 算法说明

### 技术分析算法
1. **移动平均线分析**：多头排列判断
2. **MACD分析**：金叉死叉和柱状图趋势
3. **RSI分析**：超买超卖区域判断
4. **KDJ分析**：随机指标金叉判断
5. **布林带分析**：价格位置和突破判断

### 量价分析算法
1. **量价配合度**：价涨量增的配合程度
2. **成交量趋势**：成交量放大缩小趋势
3. **价格突破**：突破前期高低点分析

### 趋势分析算法
1. **短期趋势**：5日趋势方向判断
2. **中期趋势**：20日趋势方向判断
3. **趋势一致性**：短中期趋势一致性分析

### 动量分析算法
1. **价格动量**：近期价格变化动量
2. **成交量动量**：近期成交量变化动量
3. **波动率分析**：价格波动率评估

## 测试验证

运行测试脚本验证功能：
```bash
node test-smart-recommendation.js
```

测试内容包括：
- 推荐配置获取
- 推荐统计查询
- 智能推荐生成
- 单股票分析
- 不同参数组合测试

## 注意事项

1. **数据依赖**：需要充足的历史数据支持（至少30个交易日）
2. **计算性能**：大量股票分析可能需要较长时间
3. **市场适应性**：算法适用于正常市场环境，极端行情下需谨慎
4. **更新频率**：建议每日更新推荐数据
5. **风险控制**：严格按照止损建议执行风险控制

## 后续优化方向

1. **机器学习集成**：引入更复杂的ML模型
2. **基本面分析**：集成财务数据分析
3. **新闻情感分析**：整合市场情绪因子
4. **回测验证**：建立推荐效果回测系统
5. **个性化推荐**：基于用户历史偏好优化推荐
