USE stock_analysis;

CREATE TABLE IF NOT EXISTS stock_daily_data (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  ts_code VARCHAR(20) NOT NULL COMMENT '股票代码（如000001.SZ）',
  trade_date VARCHAR(8) NOT NULL COMMENT '交易日期（YYYYMMDD格式）',
  open DECIMAL(10,3) NULL COMMENT '开盘价',
  high DECIMAL(10,3) NULL COMMENT '最高价',
  low DECIMAL(10,3) NULL COMMENT '最低价',
  close DECIMAL(10,3) NULL COMMENT '收盘价',
  pre_close DECIMAL(10,3) NULL COMMENT '昨收价',
  `change` DECIMAL(10,3) NULL COMMENT '涨跌额',
  pct_chg DECIMAL(10,3) NULL COMMENT '涨跌幅（%）',
  vol DECIMAL(20,2) NULL COMMENT '成交量（手）',
  amount DECIMAL(20,3) NULL COMMENT '成交额（千元）',
  turnover_rate DECIMAL(10,3) NULL COMMENT '换手率（%）',
  turnover_rate_f DECIMAL(10,3) NULL COMMENT '换手率（自由流通股）',
  volume_ratio DECIMAL(10,3) NULL COMMENT '量比',
  pe DECIMAL(10,3) NULL COMMENT '市盈率（总市值/净利润，亏损的PE为空）',
  pe_ttm DECIMAL(10,3) NULL COMMENT '市盈率（TTM，亏损的PE为空）',
  pb DECIMAL(10,3) NULL COMMENT '市净率（总市值/净资产）',
  ps DECIMAL(10,3) NULL COMMENT '市销率',
  ps_ttm DECIMAL(10,3) NULL COMMENT '市销率（TTM）',
  dv_ratio DECIMAL(10,3) NULL COMMENT '股息率（%）',
  dv_ttm DECIMAL(10,3) NULL COMMENT '股息率（TTM）（%）',
  total_share DECIMAL(20,2) NULL COMMENT '总股本（万股）',
  float_share DECIMAL(20,2) NULL COMMENT '流通股本（万股）',
  free_share DECIMAL(20,2) NULL COMMENT '自由流通股本（万）',
  total_mv DECIMAL(20,2) NULL COMMENT '总市值（万元）',
  circ_mv DECIMAL(20,2) NULL COMMENT '流通市值（万元）',
  data_source VARCHAR(50) NOT NULL DEFAULT 'tushare' COMMENT '数据来源（tushare/manual/other）',
  cache_priority INT NOT NULL DEFAULT 1 COMMENT '缓存优先级（1=用户关注，2=搜索历史，3=热门股票，4=系统推荐）',
  is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否有效（用于软删除）',
  last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后更新时间',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY uk_stock_daily_data_code_date (ts_code, trade_date),
  KEY idx_stock_daily_data_ts_code (ts_code),
  KEY idx_stock_daily_data_trade_date (trade_date),
  KEY idx_stock_daily_data_priority (cache_priority),
  KEY idx_stock_daily_data_updated (last_updated),
  KEY idx_stock_daily_data_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票日线数据缓存表';
