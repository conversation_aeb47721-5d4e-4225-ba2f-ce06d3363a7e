[{"name": "Process Start", "start": 1753537205394, "end": 1753537207436, "duration": 2042, "pid": 29592, "index": 0}, {"name": "Application Start", "start": 1753537207437, "end": 1753537209990, "duration": 2553, "pid": 29592, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753537207460, "end": 1753537207512, "duration": 52, "pid": 29592, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753537207512, "end": 1753537207585, "duration": 73, "pid": 29592, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753537207513, "end": 1753537207516, "duration": 3, "pid": 29592, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753537207522, "end": 1753537207523, "duration": 1, "pid": 29592, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753537207524, "end": 1753537207525, "duration": 1, "pid": 29592, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753537207526, "end": 1753537207527, "duration": 1, "pid": 29592, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753537207528, "end": 1753537207529, "duration": 1, "pid": 29592, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753537207530, "end": 1753537207531, "duration": 1, "pid": 29592, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753537207532, "end": 1753537207533, "duration": 1, "pid": 29592, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753537207534, "end": 1753537207535, "duration": 1, "pid": 29592, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753537207536, "end": 1753537207536, "duration": 0, "pid": 29592, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753537207538, "end": 1753537207539, "duration": 1, "pid": 29592, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753537207540, "end": 1753537207541, "duration": 1, "pid": 29592, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753537207542, "end": 1753537207543, "duration": 1, "pid": 29592, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753537207544, "end": 1753537207545, "duration": 1, "pid": 29592, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753537207546, "end": 1753537207547, "duration": 1, "pid": 29592, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753537207548, "end": 1753537207549, "duration": 1, "pid": 29592, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753537207550, "end": 1753537207551, "duration": 1, "pid": 29592, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753537207552, "end": 1753537207553, "duration": 1, "pid": 29592, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753537207554, "end": 1753537207555, "duration": 1, "pid": 29592, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753537207556, "end": 1753537207557, "duration": 1, "pid": 29592, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753537207558, "end": 1753537207559, "duration": 1, "pid": 29592, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753537207562, "end": 1753537207562, "duration": 0, "pid": 29592, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753537207565, "end": 1753537207566, "duration": 1, "pid": 29592, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753537207569, "end": 1753537207570, "duration": 1, "pid": 29592, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753537207576, "end": 1753537207578, "duration": 2, "pid": 29592, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753537207584, "end": 1753537207585, "duration": 1, "pid": 29592, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753537207585, "end": 1753537207585, "duration": 0, "pid": 29592, "index": 29}, {"name": "Load extend/application.js", "start": 1753537207587, "end": 1753537207727, "duration": 140, "pid": 29592, "index": 30}, {"name": "Require(26) node_modules/egg-session/app/extend/application.js", "start": 1753537207588, "end": 1753537207589, "duration": 1, "pid": 29592, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/application.js", "start": 1753537207590, "end": 1753537207594, "duration": 4, "pid": 29592, "index": 32}, {"name": "Require(28) node_modules/egg-jsonp/app/extend/application.js", "start": 1753537207595, "end": 1753537207605, "duration": 10, "pid": 29592, "index": 33}, {"name": "Require(29) node_modules/egg-schedule/app/extend/application.js", "start": 1753537207608, "end": 1753537207619, "duration": 11, "pid": 29592, "index": 34}, {"name": "Require(30) node_modules/egg-logrotator/app/extend/application.js", "start": 1753537207621, "end": 1753537207625, "duration": 4, "pid": 29592, "index": 35}, {"name": "Require(31) node_modules/egg-view/app/extend/application.js", "start": 1753537207627, "end": 1753537207630, "duration": 3, "pid": 29592, "index": 36}, {"name": "Require(32) node_modules/egg-jwt/app/extend/application.js", "start": 1753537207632, "end": 1753537207710, "duration": 78, "pid": 29592, "index": 37}, {"name": "Require(33) app/extend/application.js", "start": 1753537207713, "end": 1753537207717, "duration": 4, "pid": 29592, "index": 38}, {"name": "Load extend/request.js", "start": 1753537207727, "end": 1753537207747, "duration": 20, "pid": 29592, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1753537207736, "end": 1753537207738, "duration": 2, "pid": 29592, "index": 40}, {"name": "Load extend/response.js", "start": 1753537207748, "end": 1753537207771, "duration": 23, "pid": 29592, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1753537207757, "end": 1753537207761, "duration": 4, "pid": 29592, "index": 42}, {"name": "Load extend/context.js", "start": 1753537207771, "end": 1753537207864, "duration": 93, "pid": 29592, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1753537207772, "end": 1753537207793, "duration": 21, "pid": 29592, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1753537207793, "end": 1753537207797, "duration": 4, "pid": 29592, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1753537207799, "end": 1753537207800, "duration": 1, "pid": 29592, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1753537207801, "end": 1753537207836, "duration": 35, "pid": 29592, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1753537207838, "end": 1753537207840, "duration": 2, "pid": 29592, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753537207843, "end": 1753537207843, "duration": 0, "pid": 29592, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1753537207845, "end": 1753537207850, "duration": 5, "pid": 29592, "index": 50}, {"name": "Require(43) app/extend/context.js", "start": 1753537207851, "end": 1753537207852, "duration": 1, "pid": 29592, "index": 51}, {"name": "Load extend/helper.js", "start": 1753537207864, "end": 1753537207917, "duration": 53, "pid": 29592, "index": 52}, {"name": "Require(44) node_modules/egg-security/app/extend/helper.js", "start": 1753537207866, "end": 1753537207896, "duration": 30, "pid": 29592, "index": 53}, {"name": "Require(45) node_modules/egg/app/extend/helper.js", "start": 1753537207903, "end": 1753537207904, "duration": 1, "pid": 29592, "index": 54}, {"name": "Require(46) app/extend/helper.js", "start": 1753537207905, "end": 1753537207905, "duration": 0, "pid": 29592, "index": 55}, {"name": "Load app.js", "start": 1753537207918, "end": 1753537208045, "duration": 127, "pid": 29592, "index": 56}, {"name": "Require(47) node_modules/egg-session/app.js", "start": 1753537207918, "end": 1753537207919, "duration": 1, "pid": 29592, "index": 57}, {"name": "Require(48) node_modules/egg-security/app.js", "start": 1753537207920, "end": 1753537207924, "duration": 4, "pid": 29592, "index": 58}, {"name": "Require(49) node_modules/egg-onerror/app.js", "start": 1753537207927, "end": 1753537207949, "duration": 22, "pid": 29592, "index": 59}, {"name": "Require(50) node_modules/egg-i18n/app.js", "start": 1753537207950, "end": 1753537207972, "duration": 22, "pid": 29592, "index": 60}, {"name": "Require(51) node_modules/egg-watcher/app.js", "start": 1753537207973, "end": 1753537207996, "duration": 23, "pid": 29592, "index": 61}, {"name": "Require(52) node_modules/egg-schedule/app.js", "start": 1753537207997, "end": 1753537207999, "duration": 2, "pid": 29592, "index": 62}, {"name": "Require(53) node_modules/egg-multipart/app.js", "start": 1753537208000, "end": 1753537208004, "duration": 4, "pid": 29592, "index": 63}, {"name": "Require(54) node_modules/egg-development/app.js", "start": 1753537208005, "end": 1753537208005, "duration": 0, "pid": 29592, "index": 64}, {"name": "Require(55) node_modules/egg-logrotator/app.js", "start": 1753537208006, "end": 1753537208007, "duration": 1, "pid": 29592, "index": 65}, {"name": "Require(56) node_modules/egg-static/app.js", "start": 1753537208008, "end": 1753537208011, "duration": 3, "pid": 29592, "index": 66}, {"name": "Require(57) node_modules/egg-sequelize/app.js", "start": 1753537208013, "end": 1753537208014, "duration": 1, "pid": 29592, "index": 67}, {"name": "Require(58) node_modules/egg-jwt/app.js", "start": 1753537208015, "end": 1753537208016, "duration": 1, "pid": 29592, "index": 68}, {"name": "Require(59) node_modules/egg-cors/app.js", "start": 1753537208016, "end": 1753537208017, "duration": 1, "pid": 29592, "index": 69}, {"name": "Require(60) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1753537208018, "end": 1753537208022, "duration": 4, "pid": 29592, "index": 70}, {"name": "Require(61) node_modules/egg-mysql/app.js", "start": 1753537208023, "end": 1753537208043, "duration": 20, "pid": 29592, "index": 71}, {"name": "Require(62) app.js", "start": 1753537208044, "end": 1753537208045, "duration": 1, "pid": 29592, "index": 72}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753537208058, "end": 1753537209973, "duration": 1915, "pid": 29592, "index": 73}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753537208867, "end": 1753537209000, "duration": 133, "pid": 29592, "index": 74}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753537208904, "end": 1753537209924, "duration": 1020, "pid": 29592, "index": 75}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753537209062, "end": 1753537209988, "duration": 926, "pid": 29592, "index": 76}, {"name": "Did Load in app.js:didLoad", "start": 1753537209063, "end": 1753537209699, "duration": 636, "pid": 29592, "index": 77}, {"name": "Load Service", "start": 1753537209063, "end": 1753537209289, "duration": 226, "pid": 29592, "index": 78}, {"name": "Load \"service\" to Context", "start": 1753537209063, "end": 1753537209289, "duration": 226, "pid": 29592, "index": 79}, {"name": "Load Middleware", "start": 1753537209289, "end": 1753537209518, "duration": 229, "pid": 29592, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1753537209289, "end": 1753537209503, "duration": 214, "pid": 29592, "index": 81}, {"name": "Load Controller", "start": 1753537209518, "end": 1753537209645, "duration": 127, "pid": 29592, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1753537209518, "end": 1753537209645, "duration": 127, "pid": 29592, "index": 83}, {"name": "Load Router", "start": 1753537209645, "end": 1753537209671, "duration": 26, "pid": 29592, "index": 84}, {"name": "Require(63) app/router.js", "start": 1753537209645, "end": 1753537209647, "duration": 2, "pid": 29592, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1753537209649, "end": 1753537209699, "duration": 50, "pid": 29592, "index": 86}, {"name": "Will Ready in app.js:will<PERSON><PERSON>y", "start": 1753537209988, "end": 1753537209988, "duration": 0, "pid": 29592, "index": 87}]