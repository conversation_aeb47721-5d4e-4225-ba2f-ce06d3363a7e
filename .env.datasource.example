# 数据源API配置示例
# 复制此文件为 .env 并填入真实的API密钥

# ================================
# 智兔数服API配置
# ================================
# 获取地址: https://www.zhitudata.com/
ZHITU_API_KEY=D564FC55-057B-4F6F-932C-C115E78BFAE4
ZHITU_BASE_URL=https://api.zhitudata.com

# ================================
# Alpha Vantage API配置 (用于Yahoo/Google Finance替代)
# ================================
# 获取地址: https://www.alphavantage.co/support/#api-key
ALPHA_VANTAGE_API_KEY=f6235795d0b5310a44d87a6a41cd9dfc-c-app
ALPHA_VANTAGE_BASE_URL=https://www.alphavantage.co

# ================================
# 聚合数据API配置
# ================================
# 获取地址: https://www.juhe.cn/
JUHE_API_KEY=4191aa94e0f3ba88c66b827fbbe56624
JUHE_BASE_URL=http://web.juhe.cn/finance

# ================================
# 通用API配置
# ================================
# API请求超时时间(毫秒)
API_REQUEST_TIMEOUT=15000

# API重试次数
API_RETRY_COUNT=3

# ================================
# Python环境配置 (用于AKShare)
# ================================
# Python可执行文件路径 (可选，默认使用系统PATH中的python)
PYTHON_PATH=python

# AKShare数据缓存时间(秒)
AKSHARE_CACHE_DURATION=300

# ================================
# 数据源优先级配置
# ================================
# 主要数据源 (推荐: tencent_enhanced)
PRIMARY_DATA_SOURCE=tencent_enhanced

# 备用数据源列表 (逗号分隔)
BACKUP_DATA_SOURCES=netease_enhanced,alphavantage,eastmoney

# ================================
# 网络配置
# ================================
# 代理设置 (可选)
# HTTP_PROXY=http://proxy.example.com:8080
# HTTPS_PROXY=https://proxy.example.com:8080

# 用户代理字符串
USER_AGENT=HappyStockMarket/1.0 (https://github.com/your-repo)

# ================================
# 调试配置
# ================================
# 启用数据源调试日志
DEBUG_DATA_SOURCES=false

# 启用API调用日志
DEBUG_API_CALLS=false
