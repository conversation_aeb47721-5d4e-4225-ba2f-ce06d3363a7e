/* 响应式设计样式 */

/* 基础响应式网格系统 */
.grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--spacing-md);
}

/* 响应式容器 */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

/* 响应式断点容器宽度 */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

@media (min-width: 1600px) {
  .container {
    max-width: 1500px;
  }
}

/* 响应式列 */
.col-1 { grid-column: span 1; }
.col-2 { grid-column: span 2; }
.col-3 { grid-column: span 3; }
.col-4 { grid-column: span 4; }
.col-5 { grid-column: span 5; }
.col-6 { grid-column: span 6; }
.col-7 { grid-column: span 7; }
.col-8 { grid-column: span 8; }
.col-9 { grid-column: span 9; }
.col-10 { grid-column: span 10; }
.col-11 { grid-column: span 11; }
.col-12 { grid-column: span 12; }

/* 平板断点 */
@media (max-width: 991px) {
  .col-md-1 { grid-column: span 1; }
  .col-md-2 { grid-column: span 2; }
  .col-md-3 { grid-column: span 3; }
  .col-md-4 { grid-column: span 4; }
  .col-md-5 { grid-column: span 5; }
  .col-md-6 { grid-column: span 6; }
  .col-md-7 { grid-column: span 7; }
  .col-md-8 { grid-column: span 8; }
  .col-md-9 { grid-column: span 9; }
  .col-md-10 { grid-column: span 10; }
  .col-md-11 { grid-column: span 11; }
  .col-md-12 { grid-column: span 12; }
}

/* 手机断点 */
@media (max-width: 767px) {
  .col-sm-1 { grid-column: span 1; }
  .col-sm-2 { grid-column: span 2; }
  .col-sm-3 { grid-column: span 3; }
  .col-sm-4 { grid-column: span 4; }
  .col-sm-5 { grid-column: span 5; }
  .col-sm-6 { grid-column: span 6; }
  .col-sm-7 { grid-column: span 7; }
  .col-sm-8 { grid-column: span 8; }
  .col-sm-9 { grid-column: span 9; }
  .col-sm-10 { grid-column: span 10; }
  .col-sm-11 { grid-column: span 11; }
  .col-sm-12 { grid-column: span 12; }
  
  .grid {
    gap: var(--spacing-sm);
  }
}

/* 超小屏幕断点 */
@media (max-width: 575px) {
  .col-xs-1 { grid-column: span 1; }
  .col-xs-2 { grid-column: span 2; }
  .col-xs-3 { grid-column: span 3; }
  .col-xs-4 { grid-column: span 4; }
  .col-xs-5 { grid-column: span 5; }
  .col-xs-6 { grid-column: span 6; }
  .col-xs-7 { grid-column: span 7; }
  .col-xs-8 { grid-column: span 8; }
  .col-xs-9 { grid-column: span 9; }
  .col-xs-10 { grid-column: span 10; }
  .col-xs-11 { grid-column: span 11; }
  .col-xs-12 { grid-column: span 12; }
}

/* 响应式显示/隐藏 */
.hide-xs, .hide-sm, .hide-md, .hide-lg, .hide-xl {
  display: initial;
}

.show-xs, .show-sm, .show-md, .show-lg, .show-xl {
  display: none;
}

@media (max-width: 575px) {
  .hide-xs {
    display: none !important;
  }
  .show-xs {
    display: initial !important;
  }
}

@media (max-width: 767px) {
  .hide-sm {
    display: none !important;
  }
  .show-sm {
    display: initial !important;
  }
}

@media (max-width: 991px) {
  .hide-md {
    display: none !important;
  }
  .show-md {
    display: initial !important;
  }
}

@media (max-width: 1199px) {
  .hide-lg {
    display: none !important;
  }
  .show-lg {
    display: initial !important;
  }
}

@media (max-width: 1599px) {
  .hide-xl {
    display: none !important;
  }
  .show-xl {
    display: initial !important;
  }
}

/* 响应式间距 */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-xs); }
.ml-2 { margin-left: var(--spacing-sm); }
.ml-3 { margin-left: var(--spacing-md); }
.ml-4 { margin-left: var(--spacing-lg); }
.ml-5 { margin-left: var(--spacing-xl); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-xs); }
.mr-2 { margin-right: var(--spacing-sm); }
.mr-3 { margin-right: var(--spacing-md); }
.mr-4 { margin-right: var(--spacing-lg); }
.mr-5 { margin-right: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--spacing-xs); }
.pt-2 { padding-top: var(--spacing-sm); }
.pt-3 { padding-top: var(--spacing-md); }
.pt-4 { padding-top: var(--spacing-lg); }
.pt-5 { padding-top: var(--spacing-xl); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--spacing-xs); }
.pb-2 { padding-bottom: var(--spacing-sm); }
.pb-3 { padding-bottom: var(--spacing-md); }
.pb-4 { padding-bottom: var(--spacing-lg); }
.pb-5 { padding-bottom: var(--spacing-xl); }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: var(--spacing-xs); }
.pl-2 { padding-left: var(--spacing-sm); }
.pl-3 { padding-left: var(--spacing-md); }
.pl-4 { padding-left: var(--spacing-lg); }
.pl-5 { padding-left: var(--spacing-xl); }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: var(--spacing-xs); }
.pr-2 { padding-right: var(--spacing-sm); }
.pr-3 { padding-right: var(--spacing-md); }
.pr-4 { padding-right: var(--spacing-lg); }
.pr-5 { padding-right: var(--spacing-xl); }

/* 响应式文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

@media (max-width: 767px) {
  .text-sm-left { text-align: left; }
  .text-sm-center { text-align: center; }
  .text-sm-right { text-align: right; }
}

/* 响应式弹性布局 */
.d-flex { display: flex; }
.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }
.align-items-center { align-items: center; }
.align-items-baseline { align-items: baseline; }
.align-items-stretch { align-items: stretch; }

@media (max-width: 767px) {
  .flex-sm-row { flex-direction: row; }
  .flex-sm-column { flex-direction: column; }
  .flex-sm-wrap { flex-wrap: wrap; }
  .justify-content-sm-start { justify-content: flex-start; }
  .justify-content-sm-end { justify-content: flex-end; }
  .justify-content-sm-center { justify-content: center; }
  .justify-content-sm-between { justify-content: space-between; }
  .justify-content-sm-around { justify-content: space-around; }
  .align-items-sm-start { align-items: flex-start; }
  .align-items-sm-end { align-items: flex-end; }
  .align-items-sm-center { align-items: center; }
  .align-items-sm-baseline { align-items: baseline; }
  .align-items-sm-stretch { align-items: stretch; }
}

/* 响应式卡片网格 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
}

@media (max-width: 767px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-sm);
  }
}

/* 响应式表格 */
.responsive-table {
  width: 100%;
  border-collapse: collapse;
}

@media (max-width: 767px) {
  .responsive-table thead {
    display: none;
  }
  
  .responsive-table tbody tr {
    display: block;
    margin-bottom: var(--spacing-md);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm);
  }
  
  .responsive-table tbody td {
    display: block;
    text-align: right;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-light);
  }
  
  .responsive-table tbody td:last-child {
    border-bottom: none;
  }
  
  .responsive-table tbody td::before {
    content: attr(data-label);
    float: left;
    font-weight: bold;
    color: var(--text-secondary);
  }
}

/* 响应式导航 */
.responsive-nav {
  display: flex;
  align-items: center;
}

@media (max-width: 767px) {
  .responsive-nav {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .responsive-nav-toggle {
    display: block;
  }
  
  .responsive-nav-items {
    display: none;
    width: 100%;
  }
  
  .responsive-nav-items.active {
    display: flex;
    flex-direction: column;
  }
  
  .responsive-nav-item {
    width: 100%;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-light);
  }
}

/* 触摸友好的交互元素 */
@media (hover: none) {
  .touch-friendly {
    min-height: 44px;
    min-width: 44px;
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .touch-friendly-spacing > * {
    margin-bottom: var(--spacing-md);
  }
  
  .touch-feedback:active {
    opacity: 0.7;
  }
}

/* 响应式图像 */
.img-fluid {
  max-width: 100%;
  height: auto;
}

/* 响应式视频容器 */
.video-container {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 比例 */
  height: 0;
  overflow: hidden;
}

.video-container iframe,
.video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* 响应式字体大小 */
@media (max-width: 767px) {
  h1 {
    font-size: 1.8rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
  
  h3 {
    font-size: 1.3rem;
  }
  
  h4 {
    font-size: 1.2rem;
  }
  
  h5, h6 {
    font-size: 1.1rem;
  }
}

/* 响应式间距调整 */
@media (max-width: 767px) {
  .container {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
  }
  
  .section {
    padding: var(--spacing-md) 0;
  }
}

/* 响应式打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
}