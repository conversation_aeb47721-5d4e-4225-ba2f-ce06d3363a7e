<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新数据源测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .data-source-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .data-source-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #fafafa;
        }
        .data-source-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .data-source-card p {
            margin: 5px 0;
            color: #666;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-results {
            margin-top: 30px;
        }
        .result-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .result-item h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .result-data {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>新数据源功能测试</h1>
        
        <div class="data-source-grid">
            <div class="data-source-card">
                <h3>智兔数服</h3>
                <p><strong>描述:</strong> 专业股票数据API服务商，提供全面的股票数据接口</p>
                <p><strong>特点:</strong> 数据接口全面，服务稳定，费用低</p>
                <p><strong>API限制:</strong> 专业服务，费用在日常运营成本中几乎可以忽略不计</p>
                <button class="test-button" onclick="testDataSource('zhitu')">测试智兔数服</button>
                <div id="zhitu-status" class="status" style="display: none;"></div>
            </div>

            <div class="data-source-card">
                <h3>Yahoo Finance API</h3>
                <p><strong>描述:</strong> 广泛使用的免费股票API，提供美国和加拿大股票市场数据</p>
                <p><strong>特点:</strong> 免费使用，支持批量获取和自定义请求</p>
                <p><strong>API限制:</strong> 免费使用，支持批量获取和自定义请求</p>
                <button class="test-button" onclick="testDataSource('yahoo_finance')">测试Yahoo Finance</button>
                <div id="yahoo_finance-status" class="status" style="display: none;"></div>
            </div>

            <div class="data-source-card">
                <h3>Google Finance API</h3>
                <p><strong>描述:</strong> 谷歌提供的免费股票API，支持全球多个股票市场</p>
                <p><strong>特点:</strong> 免费使用，支持全球多个股票市场</p>
                <p><strong>API限制:</strong> 免费使用，支持全球多个股票市场</p>
                <button class="test-button" onclick="testDataSource('google_finance')">测试Google Finance</button>
                <div id="google_finance-status" class="status" style="display: none;"></div>
            </div>

            <div class="data-source-card">
                <h3>聚合数据</h3>
                <p><strong>描述:</strong> 专业数据服务平台，提供实时股票交易数据</p>
                <p><strong>特点:</strong> 只提供实时交易数据，适合轻量级使用</p>
                <p><strong>API限制:</strong> 每天免费调用50次</p>
                <button class="test-button" onclick="testDataSource('juhe')">测试聚合数据</button>
                <div id="juhe-status" class="status" style="display: none;"></div>
            </div>
        </div>

        <div class="test-results">
            <h2>测试结果</h2>
            <div id="results-container">
                <p>点击上方按钮开始测试数据源...</p>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:7001';
        
        async function testDataSource(source) {
            const statusElement = document.getElementById(`${source}-status`);
            const resultsContainer = document.getElementById('results-container');
            
            // 显示加载状态
            statusElement.style.display = 'block';
            statusElement.className = 'status loading';
            statusElement.textContent = '测试中...';
            
            // 禁用按钮
            const button = event.target;
            button.disabled = true;
            
            try {
                // 测试获取股票列表
                const response = await fetch(`${API_BASE_URL}/api/data-source/stocks?source=${source}`);
                const data = await response.json();
                
                if (data.success) {
                    // 成功
                    statusElement.className = 'status success';
                    statusElement.textContent = '测试成功';
                    
                    // 添加结果到结果区域
                    addTestResult(source, data);
                } else {
                    // 失败
                    statusElement.className = 'status error';
                    statusElement.textContent = `测试失败: ${data.message}`;
                }
            } catch (error) {
                // 错误
                statusElement.className = 'status error';
                statusElement.textContent = `测试错误: ${error.message}`;
            } finally {
                // 重新启用按钮
                button.disabled = false;
            }
        }
        
        function addTestResult(source, data) {
            const resultsContainer = document.getElementById('results-container');
            
            // 如果是第一个结果，清空提示文本
            if (resultsContainer.children.length === 1 && resultsContainer.children[0].tagName === 'P') {
                resultsContainer.innerHTML = '';
            }
            
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            
            const sourceNames = {
                'zhitu': '智兔数服',
                'yahoo_finance': 'Yahoo Finance API',
                'google_finance': 'Google Finance API',
                'juhe': '聚合数据'
            };
            
            resultItem.innerHTML = `
                <h4>${sourceNames[source]} - 测试结果</h4>
                <p><strong>状态:</strong> ${data.success ? '成功' : '失败'}</p>
                <p><strong>消息:</strong> ${data.message}</p>
                <p><strong>数据源:</strong> ${data.data_source}</p>
                <p><strong>数据源消息:</strong> ${data.data_source_message}</p>
                <p><strong>返回数据数量:</strong> ${data.data ? data.data.length : 0} 条</p>
                <div class="result-data">
                    <strong>返回数据示例:</strong><br>
                    ${JSON.stringify(data, null, 2)}
                </div>
            `;
            
            resultsContainer.appendChild(resultItem);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('新数据源测试页面已加载');
            console.log('可用的测试数据源: 智兔数服, Yahoo Finance API, Google Finance API, 聚合数据');
        });
    </script>
</body>
</html>
