{"version": 3, "file": "sw.js", "sources": ["node_modules/workbox-core/_version.js", "node_modules/workbox-core/_private/logger.js", "node_modules/workbox-core/models/messages/messages.js", "node_modules/workbox-core/models/messages/messageGenerator.js", "node_modules/workbox-core/_private/WorkboxError.js", "node_modules/workbox-core/_private/assert.js", "node_modules/workbox-routing/_version.js", "node_modules/workbox-routing/utils/constants.js", "node_modules/workbox-routing/utils/normalizeHandler.js", "node_modules/workbox-routing/Route.js", "node_modules/workbox-routing/RegExpRoute.js", "node_modules/workbox-core/_private/getFriendlyURL.js", "node_modules/workbox-routing/Router.js", "node_modules/workbox-routing/utils/getOrCreateDefaultRouter.js", "node_modules/workbox-routing/registerRoute.js", "node_modules/workbox-core/_private/cacheNames.js", "node_modules/workbox-core/_private/dontWaitFor.js", "node_modules/workbox-core/models/quotaErrorCallbacks.js", "node_modules/workbox-core/registerQuotaErrorCallback.js", "node_modules/idb/build/wrap-idb-value.js", "node_modules/idb/build/index.js", "node_modules/workbox-expiration/_version.js", "node_modules/workbox-expiration/models/CacheTimestampsModel.js", "node_modules/workbox-expiration/CacheExpiration.js", "node_modules/workbox-expiration/ExpirationPlugin.js", "node_modules/workbox-core/_private/cacheMatchIgnoreParams.js", "node_modules/workbox-core/_private/Deferred.js", "node_modules/workbox-core/_private/executeQuotaErrorCallbacks.js", "node_modules/workbox-core/_private/timeout.js", "node_modules/workbox-strategies/_version.js", "node_modules/workbox-strategies/StrategyHandler.js", "node_modules/workbox-strategies/Strategy.js", "node_modules/workbox-strategies/utils/messages.js", "node_modules/workbox-strategies/CacheFirst.js", "node_modules/workbox-cacheable-response/_version.js", "node_modules/workbox-cacheable-response/CacheableResponse.js", "node_modules/workbox-cacheable-response/CacheableResponsePlugin.js", "node_modules/workbox-strategies/plugins/cacheOkAndOpaquePlugin.js", "node_modules/workbox-strategies/StaleWhileRevalidate.js", "node_modules/workbox-strategies/NetworkFirst.js", "node_modules/workbox-background-sync/_version.js", "node_modules/workbox-background-sync/lib/QueueDb.js", "node_modules/workbox-background-sync/lib/QueueStore.js", "node_modules/workbox-background-sync/lib/StorableRequest.js", "node_modules/workbox-background-sync/Queue.js", "node_modules/workbox-background-sync/BackgroundSyncPlugin.js", "node_modules/workbox-core/clientsClaim.js", "node_modules/workbox-core/_private/waitUntil.js", "node_modules/workbox-precaching/_version.js", "node_modules/workbox-precaching/utils/createCacheKey.js", "node_modules/workbox-precaching/utils/PrecacheInstallReportPlugin.js", "node_modules/workbox-precaching/utils/PrecacheCacheKeyPlugin.js", "node_modules/workbox-precaching/utils/printCleanupDetails.js", "node_modules/workbox-precaching/utils/printInstallDetails.js", "node_modules/workbox-core/_private/canConstructResponseFromBodyStream.js", "node_modules/workbox-core/copyResponse.js", "node_modules/workbox-precaching/PrecacheStrategy.js", "node_modules/workbox-precaching/PrecacheController.js", "node_modules/workbox-precaching/utils/getOrCreatePrecacheController.js", "node_modules/workbox-precaching/utils/removeIgnoredSearchParams.js", "node_modules/workbox-precaching/utils/generateURLVariations.js", "node_modules/workbox-precaching/PrecacheRoute.js", "node_modules/workbox-precaching/addRoute.js", "node_modules/workbox-precaching/precache.js", "node_modules/workbox-precaching/precacheAndRoute.js", "node_modules/workbox-precaching/utils/deleteOutdatedCaches.js", "node_modules/workbox-precaching/cleanupOutdatedCaches.js", "node_modules/workbox-routing/NavigationRoute.js", "node_modules/workbox-precaching/createHandlerBoundToURL.js", "C:/Users/<USER>/AppData/Local/Temp/d2e7275075b89de24eba3e7027ccc452/sw.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:core:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst logger = (process.env.NODE_ENV === 'production'\n    ? null\n    : (() => {\n        // Don't overwrite this value if it's already set.\n        // See https://github.com/GoogleChrome/workbox/pull/2284#issuecomment-*********\n        if (!('__WB_DISABLE_DEV_LOGS' in globalThis)) {\n            self.__WB_DISABLE_DEV_LOGS = false;\n        }\n        let inGroup = false;\n        const methodToColorMap = {\n            debug: `#7f8c8d`,\n            log: `#2ecc71`,\n            warn: `#f39c12`,\n            error: `#c0392b`,\n            groupCollapsed: `#3498db`,\n            groupEnd: null, // No colored prefix on groupEnd\n        };\n        const print = function (method, args) {\n            if (self.__WB_DISABLE_DEV_LOGS) {\n                return;\n            }\n            if (method === 'groupCollapsed') {\n                // Safari doesn't print all console.groupCollapsed() arguments:\n                // https://bugs.webkit.org/show_bug.cgi?id=182754\n                if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n                    console[method](...args);\n                    return;\n                }\n            }\n            const styles = [\n                `background: ${methodToColorMap[method]}`,\n                `border-radius: 0.5em`,\n                `color: white`,\n                `font-weight: bold`,\n                `padding: 2px 0.5em`,\n            ];\n            // When in a group, the workbox prefix is not displayed.\n            const logPrefix = inGroup ? [] : ['%cworkbox', styles.join(';')];\n            console[method](...logPrefix, ...args);\n            if (method === 'groupCollapsed') {\n                inGroup = true;\n            }\n            if (method === 'groupEnd') {\n                inGroup = false;\n            }\n        };\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        const api = {};\n        const loggerMethods = Object.keys(methodToColorMap);\n        for (const key of loggerMethods) {\n            const method = key;\n            api[method] = (...args) => {\n                print(method, args);\n            };\n        }\n        return api;\n    })());\nexport { logger };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../../_version.js';\nexport const messages = {\n    'invalid-value': ({ paramName, validValueDescription, value }) => {\n        if (!paramName || !validValueDescription) {\n            throw new Error(`Unexpected input to 'invalid-value' error.`);\n        }\n        return (`The '${paramName}' parameter was given a value with an ` +\n            `unexpected value. ${validValueDescription} Received a value of ` +\n            `${JSON.stringify(value)}.`);\n    },\n    'not-an-array': ({ moduleName, className, funcName, paramName }) => {\n        if (!moduleName || !className || !funcName || !paramName) {\n            throw new Error(`Unexpected input to 'not-an-array' error.`);\n        }\n        return (`The parameter '${paramName}' passed into ` +\n            `'${moduleName}.${className}.${funcName}()' must be an array.`);\n    },\n    'incorrect-type': ({ expectedType, paramName, moduleName, className, funcName, }) => {\n        if (!expectedType || !paramName || !moduleName || !funcName) {\n            throw new Error(`Unexpected input to 'incorrect-type' error.`);\n        }\n        const classNameStr = className ? `${className}.` : '';\n        return (`The parameter '${paramName}' passed into ` +\n            `'${moduleName}.${classNameStr}` +\n            `${funcName}()' must be of type ${expectedType}.`);\n    },\n    'incorrect-class': ({ expectedClassName, paramName, moduleName, className, funcName, isReturnValueProblem, }) => {\n        if (!expectedClassName || !moduleName || !funcName) {\n            throw new Error(`Unexpected input to 'incorrect-class' error.`);\n        }\n        const classNameStr = className ? `${className}.` : '';\n        if (isReturnValueProblem) {\n            return (`The return value from ` +\n                `'${moduleName}.${classNameStr}${funcName}()' ` +\n                `must be an instance of class ${expectedClassName}.`);\n        }\n        return (`The parameter '${paramName}' passed into ` +\n            `'${moduleName}.${classNameStr}${funcName}()' ` +\n            `must be an instance of class ${expectedClassName}.`);\n    },\n    'missing-a-method': ({ expectedMethod, paramName, moduleName, className, funcName, }) => {\n        if (!expectedMethod ||\n            !paramName ||\n            !moduleName ||\n            !className ||\n            !funcName) {\n            throw new Error(`Unexpected input to 'missing-a-method' error.`);\n        }\n        return (`${moduleName}.${className}.${funcName}() expected the ` +\n            `'${paramName}' parameter to expose a '${expectedMethod}' method.`);\n    },\n    'add-to-cache-list-unexpected-type': ({ entry }) => {\n        return (`An unexpected entry was passed to ` +\n            `'workbox-precaching.PrecacheController.addToCacheList()' The entry ` +\n            `'${JSON.stringify(entry)}' isn't supported. You must supply an array of ` +\n            `strings with one or more characters, objects with a url property or ` +\n            `Request objects.`);\n    },\n    'add-to-cache-list-conflicting-entries': ({ firstEntry, secondEntry }) => {\n        if (!firstEntry || !secondEntry) {\n            throw new Error(`Unexpected input to ` + `'add-to-cache-list-duplicate-entries' error.`);\n        }\n        return (`Two of the entries passed to ` +\n            `'workbox-precaching.PrecacheController.addToCacheList()' had the URL ` +\n            `${firstEntry} but different revision details. Workbox is ` +\n            `unable to cache and version the asset correctly. Please remove one ` +\n            `of the entries.`);\n    },\n    'plugin-error-request-will-fetch': ({ thrownErrorMessage }) => {\n        if (!thrownErrorMessage) {\n            throw new Error(`Unexpected input to ` + `'plugin-error-request-will-fetch', error.`);\n        }\n        return (`An error was thrown by a plugins 'requestWillFetch()' method. ` +\n            `The thrown error message was: '${thrownErrorMessage}'.`);\n    },\n    'invalid-cache-name': ({ cacheNameId, value }) => {\n        if (!cacheNameId) {\n            throw new Error(`Expected a 'cacheNameId' for error 'invalid-cache-name'`);\n        }\n        return (`You must provide a name containing at least one character for ` +\n            `setCacheDetails({${cacheNameId}: '...'}). Received a value of ` +\n            `'${JSON.stringify(value)}'`);\n    },\n    'unregister-route-but-not-found-with-method': ({ method }) => {\n        if (!method) {\n            throw new Error(`Unexpected input to ` +\n                `'unregister-route-but-not-found-with-method' error.`);\n        }\n        return (`The route you're trying to unregister was not  previously ` +\n            `registered for the method type '${method}'.`);\n    },\n    'unregister-route-route-not-registered': () => {\n        return (`The route you're trying to unregister was not previously ` +\n            `registered.`);\n    },\n    'queue-replay-failed': ({ name }) => {\n        return `Replaying the background sync queue '${name}' failed.`;\n    },\n    'duplicate-queue-name': ({ name }) => {\n        return (`The Queue name '${name}' is already being used. ` +\n            `All instances of backgroundSync.Queue must be given unique names.`);\n    },\n    'expired-test-without-max-age': ({ methodName, paramName }) => {\n        return (`The '${methodName}()' method can only be used when the ` +\n            `'${paramName}' is used in the constructor.`);\n    },\n    'unsupported-route-type': ({ moduleName, className, funcName, paramName }) => {\n        return (`The supplied '${paramName}' parameter was an unsupported type. ` +\n            `Please check the docs for ${moduleName}.${className}.${funcName} for ` +\n            `valid input types.`);\n    },\n    'not-array-of-class': ({ value, expectedClass, moduleName, className, funcName, paramName, }) => {\n        return (`The supplied '${paramName}' parameter must be an array of ` +\n            `'${expectedClass}' objects. Received '${JSON.stringify(value)},'. ` +\n            `Please check the call to ${moduleName}.${className}.${funcName}() ` +\n            `to fix the issue.`);\n    },\n    'max-entries-or-age-required': ({ moduleName, className, funcName }) => {\n        return (`You must define either config.maxEntries or config.maxAgeSeconds` +\n            `in ${moduleName}.${className}.${funcName}`);\n    },\n    'statuses-or-headers-required': ({ moduleName, className, funcName }) => {\n        return (`You must define either config.statuses or config.headers` +\n            `in ${moduleName}.${className}.${funcName}`);\n    },\n    'invalid-string': ({ moduleName, funcName, paramName }) => {\n        if (!paramName || !moduleName || !funcName) {\n            throw new Error(`Unexpected input to 'invalid-string' error.`);\n        }\n        return (`When using strings, the '${paramName}' parameter must start with ` +\n            `'http' (for cross-origin matches) or '/' (for same-origin matches). ` +\n            `Please see the docs for ${moduleName}.${funcName}() for ` +\n            `more info.`);\n    },\n    'channel-name-required': () => {\n        return (`You must provide a channelName to construct a ` +\n            `BroadcastCacheUpdate instance.`);\n    },\n    'invalid-responses-are-same-args': () => {\n        return (`The arguments passed into responsesAreSame() appear to be ` +\n            `invalid. Please ensure valid Responses are used.`);\n    },\n    'expire-custom-caches-only': () => {\n        return (`You must provide a 'cacheName' property when using the ` +\n            `expiration plugin with a runtime caching strategy.`);\n    },\n    'unit-must-be-bytes': ({ normalizedRangeHeader }) => {\n        if (!normalizedRangeHeader) {\n            throw new Error(`Unexpected input to 'unit-must-be-bytes' error.`);\n        }\n        return (`The 'unit' portion of the Range header must be set to 'bytes'. ` +\n            `The Range header provided was \"${normalizedRangeHeader}\"`);\n    },\n    'single-range-only': ({ normalizedRangeHeader }) => {\n        if (!normalizedRangeHeader) {\n            throw new Error(`Unexpected input to 'single-range-only' error.`);\n        }\n        return (`Multiple ranges are not supported. Please use a  single start ` +\n            `value, and optional end value. The Range header provided was ` +\n            `\"${normalizedRangeHeader}\"`);\n    },\n    'invalid-range-values': ({ normalizedRangeHeader }) => {\n        if (!normalizedRangeHeader) {\n            throw new Error(`Unexpected input to 'invalid-range-values' error.`);\n        }\n        return (`The Range header is missing both start and end values. At least ` +\n            `one of those values is needed. The Range header provided was ` +\n            `\"${normalizedRangeHeader}\"`);\n    },\n    'no-range-header': () => {\n        return `No Range header was found in the Request provided.`;\n    },\n    'range-not-satisfiable': ({ size, start, end }) => {\n        return (`The start (${start}) and end (${end}) values in the Range are ` +\n            `not satisfiable by the cached response, which is ${size} bytes.`);\n    },\n    'attempt-to-cache-non-get-request': ({ url, method }) => {\n        return (`Unable to cache '${url}' because it is a '${method}' request and ` +\n            `only 'GET' requests can be cached.`);\n    },\n    'cache-put-with-no-response': ({ url }) => {\n        return (`There was an attempt to cache '${url}' but the response was not ` +\n            `defined.`);\n    },\n    'no-response': ({ url, error }) => {\n        let message = `The strategy could not generate a response for '${url}'.`;\n        if (error) {\n            message += ` The underlying error is ${error}.`;\n        }\n        return message;\n    },\n    'bad-precaching-response': ({ url, status }) => {\n        return (`The precaching request for '${url}' failed` +\n            (status ? ` with an HTTP status of ${status}.` : `.`));\n    },\n    'non-precached-url': ({ url }) => {\n        return (`createHandlerBoundToURL('${url}') was called, but that URL is ` +\n            `not precached. Please pass in a URL that is precached instead.`);\n    },\n    'add-to-cache-list-conflicting-integrities': ({ url }) => {\n        return (`Two of the entries passed to ` +\n            `'workbox-precaching.PrecacheController.addToCacheList()' had the URL ` +\n            `${url} with different integrity values. Please remove one of them.`);\n    },\n    'missing-precache-entry': ({ cacheName, url }) => {\n        return `Unable to find a precached response in ${cacheName} for ${url}.`;\n    },\n    'cross-origin-copy-response': ({ origin }) => {\n        return (`workbox-core.copyResponse() can only be used with same-origin ` +\n            `responses. It was passed a response with origin ${origin}.`);\n    },\n    'opaque-streams-source': ({ type }) => {\n        const message = `One of the workbox-streams sources resulted in an ` +\n            `'${type}' response.`;\n        if (type === 'opaqueredirect') {\n            return (`${message} Please do not use a navigation request that results ` +\n                `in a redirect as a source.`);\n        }\n        return `${message} Please ensure your sources are CORS-enabled.`;\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messages } from './messages.js';\nimport '../../_version.js';\nconst fallback = (code, ...args) => {\n    let msg = code;\n    if (args.length > 0) {\n        msg += ` :: ${JSON.stringify(args)}`;\n    }\n    return msg;\n};\nconst generatorFunction = (code, details = {}) => {\n    const message = messages[code];\n    if (!message) {\n        throw new Error(`Unable to find message for code '${code}'.`);\n    }\n    return message(details);\n};\nexport const messageGenerator = process.env.NODE_ENV === 'production' ? fallback : generatorFunction;\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { messageGenerator } from '../models/messages/messageGenerator.js';\nimport '../_version.js';\n/**\n * Workbox errors should be thrown with this class.\n * This allows use to ensure the type easily in tests,\n * helps developers identify errors from workbox\n * easily and allows use to optimise error\n * messages correctly.\n *\n * @private\n */\nclass WorkboxError extends Error {\n    /**\n     *\n     * @param {string} errorCode The error code that\n     * identifies this particular error.\n     * @param {Object=} details Any relevant arguments\n     * that will help developers identify issues should\n     * be added as a key on the context object.\n     */\n    constructor(errorCode, details) {\n        const message = messageGenerator(errorCode, details);\n        super(message);\n        this.name = errorCode;\n        this.details = details;\n    }\n}\nexport { WorkboxError };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from '../_private/WorkboxError.js';\nimport '../_version.js';\n/*\n * This method throws if the supplied value is not an array.\n * The destructed values are required to produce a meaningful error for users.\n * The destructed and restructured object is so it's clear what is\n * needed.\n */\nconst isArray = (value, details) => {\n    if (!Array.isArray(value)) {\n        throw new WorkboxError('not-an-array', details);\n    }\n};\nconst hasMethod = (object, expectedMethod, details) => {\n    const type = typeof object[expectedMethod];\n    if (type !== 'function') {\n        details['expectedMethod'] = expectedMethod;\n        throw new WorkboxError('missing-a-method', details);\n    }\n};\nconst isType = (object, expectedType, details) => {\n    if (typeof object !== expectedType) {\n        details['expectedType'] = expectedType;\n        throw new WorkboxError('incorrect-type', details);\n    }\n};\nconst isInstance = (object, \n// Need the general type to do the check later.\n// eslint-disable-next-line @typescript-eslint/ban-types\nexpectedClass, details) => {\n    if (!(object instanceof expectedClass)) {\n        details['expectedClassName'] = expectedClass.name;\n        throw new WorkboxError('incorrect-class', details);\n    }\n};\nconst isOneOf = (value, validValues, details) => {\n    if (!validValues.includes(value)) {\n        details['validValueDescription'] = `Valid values are ${JSON.stringify(validValues)}.`;\n        throw new WorkboxError('invalid-value', details);\n    }\n};\nconst isArrayOfClass = (value, \n// Need general type to do check later.\nexpectedClass, // eslint-disable-line\ndetails) => {\n    const error = new WorkboxError('not-array-of-class', details);\n    if (!Array.isArray(value)) {\n        throw error;\n    }\n    for (const item of value) {\n        if (!(item instanceof expectedClass)) {\n            throw error;\n        }\n    }\n};\nconst finalAssertExports = process.env.NODE_ENV === 'production'\n    ? null\n    : {\n        hasMethod,\n        isArray,\n        isInstance,\n        isOneOf,\n        isType,\n        isArrayOfClass,\n    };\nexport { finalAssertExports as assert };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:routing:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The default HTTP method, 'GET', used when there's no specific method\n * configured for a route.\n *\n * @type {string}\n *\n * @private\n */\nexport const defaultMethod = 'GET';\n/**\n * The list of valid HTTP methods associated with requests that could be routed.\n *\n * @type {Array<string>}\n *\n * @private\n */\nexport const validMethods = [\n    'DELETE',\n    'GET',\n    'HEAD',\n    'PATCH',\n    'POST',\n    'PUT',\n];\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\n/**\n * @param {function()|Object} handler Either a function, or an object with a\n * 'handle' method.\n * @return {Object} An object with a handle method.\n *\n * @private\n */\nexport const normalizeHandler = (handler) => {\n    if (handler && typeof handler === 'object') {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.hasMethod(handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return handler;\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(handler, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return { handle: handler };\n    }\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { defaultMethod, validMethods } from './utils/constants.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport './_version.js';\n/**\n * A `Route` consists of a pair of callback functions, \"match\" and \"handler\".\n * The \"match\" callback determine if a route should be used to \"handle\" a\n * request by returning a non-falsy value if it can. The \"handler\" callback\n * is called when there is a match and should return a Promise that resolves\n * to a `Response`.\n *\n * @memberof workbox-routing\n */\nclass Route {\n    /**\n     * Constructor for Route class.\n     *\n     * @param {workbox-routing~matchCallback} match\n     * A callback function that determines whether the route matches a given\n     * `fetch` event by returning a non-falsy value.\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(match, handler, method = defaultMethod) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(match, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'match',\n            });\n            if (method) {\n                assert.isOneOf(method, validMethods, { paramName: 'method' });\n            }\n        }\n        // These values are referenced directly by Router so cannot be\n        // altered by minificaton.\n        this.handler = normalizeHandler(handler);\n        this.match = match;\n        this.method = method;\n    }\n    /**\n     *\n     * @param {workbox-routing-handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response\n     */\n    setCatchHandler(handler) {\n        this.catchHandler = normalizeHandler(handler);\n    }\n}\nexport { Route };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * RegExpRoute makes it easy to create a regular expression based\n * {@link workbox-routing.Route}.\n *\n * For same-origin requests the RegExp only needs to match part of the URL. For\n * requests against third-party servers, you must define a RegExp that matches\n * the start of the URL.\n *\n * @memberof workbox-routing\n * @extends workbox-routing.Route\n */\nclass RegExpRoute extends Route {\n    /**\n     * If the regular expression contains\n     * [capture groups]{@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#grouping-back-references},\n     * the captured values will be passed to the\n     * {@link workbox-routing~handlerCallback} `params`\n     * argument.\n     *\n     * @param {RegExp} regExp The regular expression to match against URLs.\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(regExp, handler, method) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(regExp, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'RegExpRoute',\n                funcName: 'constructor',\n                paramName: 'pattern',\n            });\n        }\n        const match = ({ url }) => {\n            const result = regExp.exec(url.href);\n            // Return immediately if there's no match.\n            if (!result) {\n                return;\n            }\n            // Require that the match start at the first character in the URL string\n            // if it's a cross-origin request.\n            // See https://github.com/GoogleChrome/workbox/issues/281 for the context\n            // behind this behavior.\n            if (url.origin !== location.origin && result.index !== 0) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`The regular expression '${regExp.toString()}' only partially matched ` +\n                        `against the cross-origin URL '${url.toString()}'. RegExpRoute's will only ` +\n                        `handle cross-origin requests if they match the entire URL.`);\n                }\n                return;\n            }\n            // If the route matches, but there aren't any capture groups defined, then\n            // this will return [], which is truthy and therefore sufficient to\n            // indicate a match.\n            // If there are capture groups, then it will return their values.\n            return result.slice(1);\n        };\n        super(match, handler, method);\n    }\n}\nexport { RegExpRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst getFriendlyURL = (url) => {\n    const urlObj = new URL(String(url), location.href);\n    // See https://github.com/GoogleChrome/workbox/issues/2323\n    // We want to include everything, except for the origin if it's same-origin.\n    return urlObj.href.replace(new RegExp(`^${location.origin}`), '');\n};\nexport { getFriendlyURL };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { defaultMethod } from './utils/constants.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\n/**\n * The Router can be used to process a `FetchEvent` using one or more\n * {@link workbox-routing.Route}, responding with a `Response` if\n * a matching route exists.\n *\n * If no route matches a given a request, the Router will use a \"default\"\n * handler if one is defined.\n *\n * Should the matching Route throw an error, the Router will use a \"catch\"\n * handler if one is defined to gracefully deal with issues and respond with a\n * Request.\n *\n * If a request matches multiple routes, the **earliest** registered route will\n * be used to respond to the request.\n *\n * @memberof workbox-routing\n */\nclass Router {\n    /**\n     * Initializes a new Router.\n     */\n    constructor() {\n        this._routes = new Map();\n        this._defaultHandlerMap = new Map();\n    }\n    /**\n     * @return {Map<string, Array<workbox-routing.Route>>} routes A `Map` of HTTP\n     * method name ('GET', etc.) to an array of all the corresponding `Route`\n     * instances that are registered.\n     */\n    get routes() {\n        return this._routes;\n    }\n    /**\n     * Adds a fetch event listener to respond to events when a route matches\n     * the event's request.\n     */\n    addFetchListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('fetch', ((event) => {\n            const { request } = event;\n            const responsePromise = this.handleRequest({ request, event });\n            if (responsePromise) {\n                event.respondWith(responsePromise);\n            }\n        }));\n    }\n    /**\n     * Adds a message event listener for URLs to cache from the window.\n     * This is useful to cache resources loaded on the page prior to when the\n     * service worker started controlling it.\n     *\n     * The format of the message data sent from the window should be as follows.\n     * Where the `urlsToCache` array may consist of URL strings or an array of\n     * URL string + `requestInit` object (the same as you'd pass to `fetch()`).\n     *\n     * ```\n     * {\n     *   type: 'CACHE_URLS',\n     *   payload: {\n     *     urlsToCache: [\n     *       './script1.js',\n     *       './script2.js',\n     *       ['./script3.js', {mode: 'no-cors'}],\n     *     ],\n     *   },\n     * }\n     * ```\n     */\n    addCacheListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('message', ((event) => {\n            // event.data is type 'any'\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n            if (event.data && event.data.type === 'CACHE_URLS') {\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                const { payload } = event.data;\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`Caching URLs from the window`, payload.urlsToCache);\n                }\n                const requestPromises = Promise.all(payload.urlsToCache.map((entry) => {\n                    if (typeof entry === 'string') {\n                        entry = [entry];\n                    }\n                    const request = new Request(...entry);\n                    return this.handleRequest({ request, event });\n                    // TODO(philipwalton): TypeScript errors without this typecast for\n                    // some reason (probably a bug). The real type here should work but\n                    // doesn't: `Array<Promise<Response> | undefined>`.\n                })); // TypeScript\n                event.waitUntil(requestPromises);\n                // If a MessageChannel was used, reply to the message on success.\n                if (event.ports && event.ports[0]) {\n                    void requestPromises.then(() => event.ports[0].postMessage(true));\n                }\n            }\n        }));\n    }\n    /**\n     * Apply the routing rules to a FetchEvent object to get a Response from an\n     * appropriate Route's handler.\n     *\n     * @param {Object} options\n     * @param {Request} options.request The request to handle.\n     * @param {ExtendableEvent} options.event The event that triggered the\n     *     request.\n     * @return {Promise<Response>|undefined} A promise is returned if a\n     *     registered route can handle the request. If there is no matching\n     *     route and there's no `defaultHandler`, `undefined` is returned.\n     */\n    handleRequest({ request, event, }) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'handleRequest',\n                paramName: 'options.request',\n            });\n        }\n        const url = new URL(request.url, location.href);\n        if (!url.protocol.startsWith('http')) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Workbox Router only supports URLs that start with 'http'.`);\n            }\n            return;\n        }\n        const sameOrigin = url.origin === location.origin;\n        const { params, route } = this.findMatchingRoute({\n            event,\n            request,\n            sameOrigin,\n            url,\n        });\n        let handler = route && route.handler;\n        const debugMessages = [];\n        if (process.env.NODE_ENV !== 'production') {\n            if (handler) {\n                debugMessages.push([`Found a route to handle this request:`, route]);\n                if (params) {\n                    debugMessages.push([\n                        `Passing the following params to the route's handler:`,\n                        params,\n                    ]);\n                }\n            }\n        }\n        // If we don't have a handler because there was no matching route, then\n        // fall back to defaultHandler if that's defined.\n        const method = request.method;\n        if (!handler && this._defaultHandlerMap.has(method)) {\n            if (process.env.NODE_ENV !== 'production') {\n                debugMessages.push(`Failed to find a matching route. Falling ` +\n                    `back to the default handler for ${method}.`);\n            }\n            handler = this._defaultHandlerMap.get(method);\n        }\n        if (!handler) {\n            if (process.env.NODE_ENV !== 'production') {\n                // No handler so Workbox will do nothing. If logs is set of debug\n                // i.e. verbose, we should print out this information.\n                logger.debug(`No route found for: ${getFriendlyURL(url)}`);\n            }\n            return;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            // We have a handler, meaning Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Router is responding to: ${getFriendlyURL(url)}`);\n            debugMessages.forEach((msg) => {\n                if (Array.isArray(msg)) {\n                    logger.log(...msg);\n                }\n                else {\n                    logger.log(msg);\n                }\n            });\n            logger.groupEnd();\n        }\n        // Wrap in try and catch in case the handle method throws a synchronous\n        // error. It should still callback to the catch handler.\n        let responsePromise;\n        try {\n            responsePromise = handler.handle({ url, request, event, params });\n        }\n        catch (err) {\n            responsePromise = Promise.reject(err);\n        }\n        // Get route's catch handler, if it exists\n        const catchHandler = route && route.catchHandler;\n        if (responsePromise instanceof Promise &&\n            (this._catchHandler || catchHandler)) {\n            responsePromise = responsePromise.catch(async (err) => {\n                // If there's a route catch handler, process that first\n                if (catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to route's Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    try {\n                        return await catchHandler.handle({ url, request, event, params });\n                    }\n                    catch (catchErr) {\n                        if (catchErr instanceof Error) {\n                            err = catchErr;\n                        }\n                    }\n                }\n                if (this._catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to global Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    return this._catchHandler.handle({ url, request, event });\n                }\n                throw err;\n            });\n        }\n        return responsePromise;\n    }\n    /**\n     * Checks a request and URL (and optionally an event) against the list of\n     * registered routes, and if there's a match, returns the corresponding\n     * route along with any params generated by the match.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {boolean} options.sameOrigin The result of comparing `url.origin`\n     *     against the current origin.\n     * @param {Request} options.request The request to match.\n     * @param {Event} options.event The corresponding event.\n     * @return {Object} An object with `route` and `params` properties.\n     *     They are populated if a matching route was found or `undefined`\n     *     otherwise.\n     */\n    findMatchingRoute({ url, sameOrigin, request, event, }) {\n        const routes = this._routes.get(request.method) || [];\n        for (const route of routes) {\n            let params;\n            // route.match returns type any, not possible to change right now.\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            const matchResult = route.match({ url, sameOrigin, request, event });\n            if (matchResult) {\n                if (process.env.NODE_ENV !== 'production') {\n                    // Warn developers that using an async matchCallback is almost always\n                    // not the right thing to do.\n                    if (matchResult instanceof Promise) {\n                        logger.warn(`While routing ${getFriendlyURL(url)}, an async ` +\n                            `matchCallback function was used. Please convert the ` +\n                            `following route to use a synchronous matchCallback function:`, route);\n                    }\n                }\n                // See https://github.com/GoogleChrome/workbox/issues/2079\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                params = matchResult;\n                if (Array.isArray(params) && params.length === 0) {\n                    // Instead of passing an empty array in as params, use undefined.\n                    params = undefined;\n                }\n                else if (matchResult.constructor === Object && // eslint-disable-line\n                    Object.keys(matchResult).length === 0) {\n                    // Instead of passing an empty object in as params, use undefined.\n                    params = undefined;\n                }\n                else if (typeof matchResult === 'boolean') {\n                    // For the boolean value true (rather than just something truth-y),\n                    // don't set params.\n                    // See https://github.com/GoogleChrome/workbox/pull/2134#issuecomment-513924353\n                    params = undefined;\n                }\n                // Return early if have a match.\n                return { route, params };\n            }\n        }\n        // If no match was found above, return and empty object.\n        return {};\n    }\n    /**\n     * Define a default `handler` that's called when no routes explicitly\n     * match the incoming request.\n     *\n     * Each HTTP method ('GET', 'POST', etc.) gets its own default handler.\n     *\n     * Without a default handler, unmatched requests will go against the\n     * network as if there were no service worker present.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to associate with this\n     * default handler. Each method has its own default.\n     */\n    setDefaultHandler(handler, method = defaultMethod) {\n        this._defaultHandlerMap.set(method, normalizeHandler(handler));\n    }\n    /**\n     * If a Route throws an error while handling a request, this `handler`\n     * will be called and given a chance to provide a response.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     */\n    setCatchHandler(handler) {\n        this._catchHandler = normalizeHandler(handler);\n    }\n    /**\n     * Registers a route with the router.\n     *\n     * @param {workbox-routing.Route} route The route to register.\n     */\n    registerRoute(route) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(route, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route, 'match', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.isType(route.handler, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route.handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.handler',\n            });\n            assert.isType(route.method, 'string', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.method',\n            });\n        }\n        if (!this._routes.has(route.method)) {\n            this._routes.set(route.method, []);\n        }\n        // Give precedence to all of the earlier routes by adding this additional\n        // route to the end of the array.\n        this._routes.get(route.method).push(route);\n    }\n    /**\n     * Unregisters a route with the router.\n     *\n     * @param {workbox-routing.Route} route The route to unregister.\n     */\n    unregisterRoute(route) {\n        if (!this._routes.has(route.method)) {\n            throw new WorkboxError('unregister-route-but-not-found-with-method', {\n                method: route.method,\n            });\n        }\n        const routeIndex = this._routes.get(route.method).indexOf(route);\n        if (routeIndex > -1) {\n            this._routes.get(route.method).splice(routeIndex, 1);\n        }\n        else {\n            throw new WorkboxError('unregister-route-route-not-registered');\n        }\n    }\n}\nexport { Router };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Router } from '../Router.js';\nimport '../_version.js';\nlet defaultRouter;\n/**\n * Creates a new, singleton Router instance if one does not exist. If one\n * does already exist, that instance is returned.\n *\n * @private\n * @return {Router}\n */\nexport const getOrCreateDefaultRouter = () => {\n    if (!defaultRouter) {\n        defaultRouter = new Router();\n        // The helpers that use the default Router assume these listeners exist.\n        defaultRouter.addFetchListener();\n        defaultRouter.addCacheListener();\n    }\n    return defaultRouter;\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Route } from './Route.js';\nimport { RegExpRoute } from './RegExpRoute.js';\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * Easily register a RegExp, string, or function with a caching\n * strategy to a singleton Router instance.\n *\n * This method will generate a Route for you if needed and\n * call {@link workbox-routing.Router#registerRoute}.\n *\n * @param {RegExp|string|workbox-routing.Route~matchCallback|workbox-routing.Route} capture\n * If the capture param is a `Route`, all other arguments will be ignored.\n * @param {workbox-routing~handlerCallback} [handler] A callback\n * function that returns a Promise resulting in a Response. This parameter\n * is required if `capture` is not a `Route` object.\n * @param {string} [method='GET'] The HTTP method to match the Route\n * against.\n * @return {workbox-routing.Route} The generated `Route`.\n *\n * @memberof workbox-routing\n */\nfunction registerRoute(capture, handler, method) {\n    let route;\n    if (typeof capture === 'string') {\n        const captureUrl = new URL(capture, location.href);\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(capture.startsWith('/') || capture.startsWith('http'))) {\n                throw new WorkboxError('invalid-string', {\n                    moduleName: 'workbox-routing',\n                    funcName: 'registerRoute',\n                    paramName: 'capture',\n                });\n            }\n            // We want to check if Express-style wildcards are in the pathname only.\n            // TODO: Remove this log message in v4.\n            const valueToCheck = capture.startsWith('http')\n                ? captureUrl.pathname\n                : capture;\n            // See https://github.com/pillarjs/path-to-regexp#parameters\n            const wildcards = '[*:?+]';\n            if (new RegExp(`${wildcards}`).exec(valueToCheck)) {\n                logger.debug(`The '$capture' parameter contains an Express-style wildcard ` +\n                    `character (${wildcards}). Strings are now always interpreted as ` +\n                    `exact matches; use a RegExp for partial or wildcard matches.`);\n            }\n        }\n        const matchCallback = ({ url }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (url.pathname === captureUrl.pathname &&\n                    url.origin !== captureUrl.origin) {\n                    logger.debug(`${capture} only partially matches the cross-origin URL ` +\n                        `${url.toString()}. This route will only handle cross-origin requests ` +\n                        `if they match the entire URL.`);\n                }\n            }\n            return url.href === captureUrl.href;\n        };\n        // If `capture` is a string then `handler` and `method` must be present.\n        route = new Route(matchCallback, handler, method);\n    }\n    else if (capture instanceof RegExp) {\n        // If `capture` is a `RegExp` then `handler` and `method` must be present.\n        route = new RegExpRoute(capture, handler, method);\n    }\n    else if (typeof capture === 'function') {\n        // If `capture` is a function then `handler` and `method` must be present.\n        route = new Route(capture, handler, method);\n    }\n    else if (capture instanceof Route) {\n        route = capture;\n    }\n    else {\n        throw new WorkboxError('unsupported-route-type', {\n            moduleName: 'workbox-routing',\n            funcName: 'registerRoute',\n            paramName: 'capture',\n        });\n    }\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.registerRoute(route);\n    return route;\n}\nexport { registerRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst _cacheNameDetails = {\n    googleAnalytics: 'googleAnalytics',\n    precache: 'precache-v2',\n    prefix: 'workbox',\n    runtime: 'runtime',\n    suffix: typeof registration !== 'undefined' ? registration.scope : '',\n};\nconst _createCacheName = (cacheName) => {\n    return [_cacheNameDetails.prefix, cacheName, _cacheNameDetails.suffix]\n        .filter((value) => value && value.length > 0)\n        .join('-');\n};\nconst eachCacheNameDetail = (fn) => {\n    for (const key of Object.keys(_cacheNameDetails)) {\n        fn(key);\n    }\n};\nexport const cacheNames = {\n    updateDetails: (details) => {\n        eachCacheNameDetail((key) => {\n            if (typeof details[key] === 'string') {\n                _cacheNameDetails[key] = details[key];\n            }\n        });\n    },\n    getGoogleAnalyticsName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.googleAnalytics);\n    },\n    getPrecacheName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.precache);\n    },\n    getPrefix: () => {\n        return _cacheNameDetails.prefix;\n    },\n    getRuntimeName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.runtime);\n    },\n    getSuffix: () => {\n        return _cacheNameDetails.suffix;\n    },\n};\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A helper function that prevents a promise from being flagged as unused.\n *\n * @private\n **/\nexport function dontWaitFor(promise) {\n    // Effective no-op.\n    void promise.then(() => { });\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n// Callbacks to be executed whenever there's a quota error.\n// Can't change Function type right now.\n// eslint-disable-next-line @typescript-eslint/ban-types\nconst quotaErrorCallbacks = new Set();\nexport { quotaErrorCallbacks };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from './_private/logger.js';\nimport { assert } from './_private/assert.js';\nimport { quotaErrorCallbacks } from './models/quotaErrorCallbacks.js';\nimport './_version.js';\n/**\n * Adds a function to the set of quotaErrorCallbacks that will be executed if\n * there's a quota error.\n *\n * @param {Function} callback\n * @memberof workbox-core\n */\n// Can't change Function type\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction registerQuotaErrorCallback(callback) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isType(callback, 'function', {\n            moduleName: 'workbox-core',\n            funcName: 'register',\n            paramName: 'callback',\n        });\n    }\n    quotaErrorCallbacks.add(callback);\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Registered a callback to respond to quota errors.', callback);\n    }\n}\nexport { registerQuotaErrorCallback };\n", "const instanceOfAny = (object, constructors) => constructors.some((c) => object instanceof c);\n\nlet idbProxyableTypes;\nlet cursorAdvanceMethods;\n// This is a function to prevent it throwing up in node environments.\nfunction getIdbProxyableTypes() {\n    return (idbProxyableTypes ||\n        (idbProxyableTypes = [\n            IDBDatabase,\n            IDBObjectStore,\n            IDBIndex,\n            IDBCursor,\n            IDBTransaction,\n        ]));\n}\n// This is a function to prevent it throwing up in node environments.\nfunction getCursorAdvanceMethods() {\n    return (cursorAdvanceMethods ||\n        (cursorAdvanceMethods = [\n            IDBCursor.prototype.advance,\n            IDBCursor.prototype.continue,\n            IDBCursor.prototype.continuePrimaryKey,\n        ]));\n}\nconst cursorRequestMap = new WeakMap();\nconst transactionDoneMap = new WeakMap();\nconst transactionStoreNamesMap = new WeakMap();\nconst transformCache = new WeakMap();\nconst reverseTransformCache = new WeakMap();\nfunction promisifyRequest(request) {\n    const promise = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            request.removeEventListener('success', success);\n            request.removeEventListener('error', error);\n        };\n        const success = () => {\n            resolve(wrap(request.result));\n            unlisten();\n        };\n        const error = () => {\n            reject(request.error);\n            unlisten();\n        };\n        request.addEventListener('success', success);\n        request.addEventListener('error', error);\n    });\n    promise\n        .then((value) => {\n        // Since cursoring reuses the IDBRequest (*sigh*), we cache it for later retrieval\n        // (see wrapFunction).\n        if (value instanceof IDBCursor) {\n            cursorRequestMap.set(value, request);\n        }\n        // Catching to avoid \"Uncaught Promise exceptions\"\n    })\n        .catch(() => { });\n    // This mapping exists in reverseTransformCache but doesn't doesn't exist in transformCache. This\n    // is because we create many promises from a single IDBRequest.\n    reverseTransformCache.set(promise, request);\n    return promise;\n}\nfunction cacheDonePromiseForTransaction(tx) {\n    // Early bail if we've already created a done promise for this transaction.\n    if (transactionDoneMap.has(tx))\n        return;\n    const done = new Promise((resolve, reject) => {\n        const unlisten = () => {\n            tx.removeEventListener('complete', complete);\n            tx.removeEventListener('error', error);\n            tx.removeEventListener('abort', error);\n        };\n        const complete = () => {\n            resolve();\n            unlisten();\n        };\n        const error = () => {\n            reject(tx.error || new DOMException('AbortError', 'AbortError'));\n            unlisten();\n        };\n        tx.addEventListener('complete', complete);\n        tx.addEventListener('error', error);\n        tx.addEventListener('abort', error);\n    });\n    // Cache it for later retrieval.\n    transactionDoneMap.set(tx, done);\n}\nlet idbProxyTraps = {\n    get(target, prop, receiver) {\n        if (target instanceof IDBTransaction) {\n            // Special handling for transaction.done.\n            if (prop === 'done')\n                return transactionDoneMap.get(target);\n            // Polyfill for objectStoreNames because of Edge.\n            if (prop === 'objectStoreNames') {\n                return target.objectStoreNames || transactionStoreNamesMap.get(target);\n            }\n            // Make tx.store return the only store in the transaction, or undefined if there are many.\n            if (prop === 'store') {\n                return receiver.objectStoreNames[1]\n                    ? undefined\n                    : receiver.objectStore(receiver.objectStoreNames[0]);\n            }\n        }\n        // Else transform whatever we get back.\n        return wrap(target[prop]);\n    },\n    set(target, prop, value) {\n        target[prop] = value;\n        return true;\n    },\n    has(target, prop) {\n        if (target instanceof IDBTransaction &&\n            (prop === 'done' || prop === 'store')) {\n            return true;\n        }\n        return prop in target;\n    },\n};\nfunction replaceTraps(callback) {\n    idbProxyTraps = callback(idbProxyTraps);\n}\nfunction wrapFunction(func) {\n    // Due to expected object equality (which is enforced by the caching in `wrap`), we\n    // only create one new func per func.\n    // Edge doesn't support objectStoreNames (booo), so we polyfill it here.\n    if (func === IDBDatabase.prototype.transaction &&\n        !('objectStoreNames' in IDBTransaction.prototype)) {\n        return function (storeNames, ...args) {\n            const tx = func.call(unwrap(this), storeNames, ...args);\n            transactionStoreNamesMap.set(tx, storeNames.sort ? storeNames.sort() : [storeNames]);\n            return wrap(tx);\n        };\n    }\n    // Cursor methods are special, as the behaviour is a little more different to standard IDB. In\n    // IDB, you advance the cursor and wait for a new 'success' on the IDBRequest that gave you the\n    // cursor. It's kinda like a promise that can resolve with many values. That doesn't make sense\n    // with real promises, so each advance methods returns a new promise for the cursor object, or\n    // undefined if the end of the cursor has been reached.\n    if (getCursorAdvanceMethods().includes(func)) {\n        return function (...args) {\n            // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n            // the original object.\n            func.apply(unwrap(this), args);\n            return wrap(cursorRequestMap.get(this));\n        };\n    }\n    return function (...args) {\n        // Calling the original function with the proxy as 'this' causes ILLEGAL INVOCATION, so we use\n        // the original object.\n        return wrap(func.apply(unwrap(this), args));\n    };\n}\nfunction transformCachableValue(value) {\n    if (typeof value === 'function')\n        return wrapFunction(value);\n    // This doesn't return, it just creates a 'done' promise for the transaction,\n    // which is later returned for transaction.done (see idbObjectHandler).\n    if (value instanceof IDBTransaction)\n        cacheDonePromiseForTransaction(value);\n    if (instanceOfAny(value, getIdbProxyableTypes()))\n        return new Proxy(value, idbProxyTraps);\n    // Return the same value back if we're not going to transform it.\n    return value;\n}\nfunction wrap(value) {\n    // We sometimes generate multiple promises from a single IDBRequest (eg when cursoring), because\n    // IDB is weird and a single IDBRequest can yield many responses, so these can't be cached.\n    if (value instanceof IDBRequest)\n        return promisifyRequest(value);\n    // If we've already transformed this value before, reuse the transformed value.\n    // This is faster, but it also provides object equality.\n    if (transformCache.has(value))\n        return transformCache.get(value);\n    const newValue = transformCachableValue(value);\n    // Not all types are transformed.\n    // These may be primitive types, so they can't be WeakMap keys.\n    if (newValue !== value) {\n        transformCache.set(value, newValue);\n        reverseTransformCache.set(newValue, value);\n    }\n    return newValue;\n}\nconst unwrap = (value) => reverseTransformCache.get(value);\n\nexport { reverseTransformCache as a, instanceOfAny as i, replaceTraps as r, unwrap as u, wrap as w };\n", "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:expiration:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { openDB, deleteDB } from 'idb';\nimport '../_version.js';\nconst DB_NAME = 'workbox-expiration';\nconst CACHE_OBJECT_STORE = 'cache-entries';\nconst normalizeURL = (unNormalizedUrl) => {\n    const url = new URL(unNormalizedUrl, location.href);\n    url.hash = '';\n    return url.href;\n};\n/**\n * Returns the timestamp model.\n *\n * @private\n */\nclass CacheTimestampsModel {\n    /**\n     *\n     * @param {string} cacheName\n     *\n     * @private\n     */\n    constructor(cacheName) {\n        this._db = null;\n        this._cacheName = cacheName;\n    }\n    /**\n     * Performs an upgrade of indexedDB.\n     *\n     * @param {IDBPDatabase<CacheDbSchema>} db\n     *\n     * @private\n     */\n    _upgradeDb(db) {\n        // TODO(philipwalton): EdgeHTML doesn't support arrays as a keyPath, so we\n        // have to use the `id` keyPath here and create our own values (a\n        // concatenation of `url + cacheName`) instead of simply using\n        // `keyPath: ['url', 'cacheName']`, which is supported in other browsers.\n        const objStore = db.createObjectStore(CACHE_OBJECT_STORE, { keyPath: 'id' });\n        // TODO(philipwalton): once we don't have to support EdgeHTML, we can\n        // create a single index with the keyPath `['cacheName', 'timestamp']`\n        // instead of doing both these indexes.\n        objStore.createIndex('cacheName', 'cacheName', { unique: false });\n        objStore.createIndex('timestamp', 'timestamp', { unique: false });\n    }\n    /**\n     * Performs an upgrade of indexedDB and deletes deprecated DBs.\n     *\n     * @param {IDBPDatabase<CacheDbSchema>} db\n     *\n     * @private\n     */\n    _upgradeDbAndDeleteOldDbs(db) {\n        this._upgradeDb(db);\n        if (this._cacheName) {\n            void deleteDB(this._cacheName);\n        }\n    }\n    /**\n     * @param {string} url\n     * @param {number} timestamp\n     *\n     * @private\n     */\n    async setTimestamp(url, timestamp) {\n        url = normalizeURL(url);\n        const entry = {\n            url,\n            timestamp,\n            cacheName: this._cacheName,\n            // Creating an ID from the URL and cache name won't be necessary once\n            // Edge switches to Chromium and all browsers we support work with\n            // array keyPaths.\n            id: this._getId(url),\n        };\n        const db = await this.getDb();\n        const tx = db.transaction(CACHE_OBJECT_STORE, 'readwrite', {\n            durability: 'relaxed',\n        });\n        await tx.store.put(entry);\n        await tx.done;\n    }\n    /**\n     * Returns the timestamp stored for a given URL.\n     *\n     * @param {string} url\n     * @return {number | undefined}\n     *\n     * @private\n     */\n    async getTimestamp(url) {\n        const db = await this.getDb();\n        const entry = await db.get(CACHE_OBJECT_STORE, this._getId(url));\n        return entry === null || entry === void 0 ? void 0 : entry.timestamp;\n    }\n    /**\n     * Iterates through all the entries in the object store (from newest to\n     * oldest) and removes entries once either `maxCount` is reached or the\n     * entry's timestamp is less than `minTimestamp`.\n     *\n     * @param {number} minTimestamp\n     * @param {number} maxCount\n     * @return {Array<string>}\n     *\n     * @private\n     */\n    async expireEntries(minTimestamp, maxCount) {\n        const db = await this.getDb();\n        let cursor = await db\n            .transaction(CACHE_OBJECT_STORE)\n            .store.index('timestamp')\n            .openCursor(null, 'prev');\n        const entriesToDelete = [];\n        let entriesNotDeletedCount = 0;\n        while (cursor) {\n            const result = cursor.value;\n            // TODO(philipwalton): once we can use a multi-key index, we\n            // won't have to check `cacheName` here.\n            if (result.cacheName === this._cacheName) {\n                // Delete an entry if it's older than the max age or\n                // if we already have the max number allowed.\n                if ((minTimestamp && result.timestamp < minTimestamp) ||\n                    (maxCount && entriesNotDeletedCount >= maxCount)) {\n                    // TODO(philipwalton): we should be able to delete the\n                    // entry right here, but doing so causes an iteration\n                    // bug in Safari stable (fixed in TP). Instead we can\n                    // store the keys of the entries to delete, and then\n                    // delete the separate transactions.\n                    // https://github.com/GoogleChrome/workbox/issues/1978\n                    // cursor.delete();\n                    // We only need to return the URL, not the whole entry.\n                    entriesToDelete.push(cursor.value);\n                }\n                else {\n                    entriesNotDeletedCount++;\n                }\n            }\n            cursor = await cursor.continue();\n        }\n        // TODO(philipwalton): once the Safari bug in the following issue is fixed,\n        // we should be able to remove this loop and do the entry deletion in the\n        // cursor loop above:\n        // https://github.com/GoogleChrome/workbox/issues/1978\n        const urlsDeleted = [];\n        for (const entry of entriesToDelete) {\n            await db.delete(CACHE_OBJECT_STORE, entry.id);\n            urlsDeleted.push(entry.url);\n        }\n        return urlsDeleted;\n    }\n    /**\n     * Takes a URL and returns an ID that will be unique in the object store.\n     *\n     * @param {string} url\n     * @return {string}\n     *\n     * @private\n     */\n    _getId(url) {\n        // Creating an ID from the URL and cache name won't be necessary once\n        // Edge switches to Chromium and all browsers we support work with\n        // array keyPaths.\n        return this._cacheName + '|' + normalizeURL(url);\n    }\n    /**\n     * Returns an open connection to the database.\n     *\n     * @private\n     */\n    async getDb() {\n        if (!this._db) {\n            this._db = await openDB(DB_NAME, 1, {\n                upgrade: this._upgradeDbAndDeleteOldDbs.bind(this),\n            });\n        }\n        return this._db;\n    }\n}\nexport { CacheTimestampsModel };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheTimestampsModel } from './models/CacheTimestampsModel.js';\nimport './_version.js';\n/**\n * The `CacheExpiration` class allows you define an expiration and / or\n * limit on the number of responses stored in a\n * [`Cache`](https://developer.mozilla.org/en-US/docs/Web/API/Cache).\n *\n * @memberof workbox-expiration\n */\nclass CacheExpiration {\n    /**\n     * To construct a new CacheExpiration instance you must provide at least\n     * one of the `config` properties.\n     *\n     * @param {string} cacheName Name of the cache to apply restrictions to.\n     * @param {Object} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     * @param {Object} [config.matchOptions] The [`CacheQueryOptions`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/delete#Parameters)\n     * that will be used when calling `delete()` on the cache.\n     */\n    constructor(cacheName, config = {}) {\n        this._isRunning = false;\n        this._rerunRequested = false;\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(cacheName, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'constructor',\n                paramName: 'cacheName',\n            });\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'CacheExpiration',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n            }\n        }\n        this._maxEntries = config.maxEntries;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._matchOptions = config.matchOptions;\n        this._cacheName = cacheName;\n        this._timestampModel = new CacheTimestampsModel(cacheName);\n    }\n    /**\n     * Expires entries for the given cache and given criteria.\n     */\n    async expireEntries() {\n        if (this._isRunning) {\n            this._rerunRequested = true;\n            return;\n        }\n        this._isRunning = true;\n        const minTimestamp = this._maxAgeSeconds\n            ? Date.now() - this._maxAgeSeconds * 1000\n            : 0;\n        const urlsExpired = await this._timestampModel.expireEntries(minTimestamp, this._maxEntries);\n        // Delete URLs from the cache\n        const cache = await self.caches.open(this._cacheName);\n        for (const url of urlsExpired) {\n            await cache.delete(url, this._matchOptions);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (urlsExpired.length > 0) {\n                logger.groupCollapsed(`Expired ${urlsExpired.length} ` +\n                    `${urlsExpired.length === 1 ? 'entry' : 'entries'} and removed ` +\n                    `${urlsExpired.length === 1 ? 'it' : 'them'} from the ` +\n                    `'${this._cacheName}' cache.`);\n                logger.log(`Expired the following ${urlsExpired.length === 1 ? 'URL' : 'URLs'}:`);\n                urlsExpired.forEach((url) => logger.log(`    ${url}`));\n                logger.groupEnd();\n            }\n            else {\n                logger.debug(`Cache expiration ran and found no entries to remove.`);\n            }\n        }\n        this._isRunning = false;\n        if (this._rerunRequested) {\n            this._rerunRequested = false;\n            dontWaitFor(this.expireEntries());\n        }\n    }\n    /**\n     * Update the timestamp for the given URL. This ensures the when\n     * removing entries based on maximum entries, most recently used\n     * is accurate or when expiring, the timestamp is up-to-date.\n     *\n     * @param {string} url\n     */\n    async updateTimestamp(url) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(url, 'string', {\n                moduleName: 'workbox-expiration',\n                className: 'CacheExpiration',\n                funcName: 'updateTimestamp',\n                paramName: 'url',\n            });\n        }\n        await this._timestampModel.setTimestamp(url, Date.now());\n    }\n    /**\n     * Can be used to check if a URL has expired or not before it's used.\n     *\n     * This requires a look up from IndexedDB, so can be slow.\n     *\n     * Note: This method will not remove the cached entry, call\n     * `expireEntries()` to remove indexedDB and Cache entries.\n     *\n     * @param {string} url\n     * @return {boolean}\n     */\n    async isURLExpired(url) {\n        if (!this._maxAgeSeconds) {\n            if (process.env.NODE_ENV !== 'production') {\n                throw new WorkboxError(`expired-test-without-max-age`, {\n                    methodName: 'isURLExpired',\n                    paramName: 'maxAgeSeconds',\n                });\n            }\n            return false;\n        }\n        else {\n            const timestamp = await this._timestampModel.getTimestamp(url);\n            const expireOlderThan = Date.now() - this._maxAgeSeconds * 1000;\n            return timestamp !== undefined ? timestamp < expireOlderThan : true;\n        }\n    }\n    /**\n     * Removes the IndexedDB object store used to keep track of cache expiration\n     * metadata.\n     */\n    async delete() {\n        // Make sure we don't attempt another rerun if we're called in the middle of\n        // a cache expiration.\n        this._rerunRequested = false;\n        await this._timestampModel.expireEntries(Infinity); // Expires all.\n    }\n}\nexport { CacheExpiration };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { registerQuotaErrorCallback } from 'workbox-core/registerQuotaErrorCallback.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { CacheExpiration } from './CacheExpiration.js';\nimport './_version.js';\n/**\n * This plugin can be used in a `workbox-strategy` to regularly enforce a\n * limit on the age and / or the number of cached requests.\n *\n * It can only be used with `workbox-strategy` instances that have a\n * [custom `cacheName` property set](/web/tools/workbox/guides/configure-workbox#custom_cache_names_in_strategies).\n * In other words, it can't be used to expire entries in strategy that uses the\n * default runtime cache name.\n *\n * Whenever a cached response is used or updated, this plugin will look\n * at the associated cache and remove any old or extra responses.\n *\n * When using `maxAgeSeconds`, responses may be used *once* after expiring\n * because the expiration clean up will not have occurred until *after* the\n * cached response has been used. If the response has a \"Date\" header, then\n * a light weight expiration check is performed and the response will not be\n * used immediately.\n *\n * When using `maxEntries`, the entry least-recently requested will be removed\n * from the cache first.\n *\n * @memberof workbox-expiration\n */\nclass ExpirationPlugin {\n    /**\n     * @param {ExpirationPluginOptions} config\n     * @param {number} [config.maxEntries] The maximum number of entries to cache.\n     * Entries used the least will be removed as the maximum is reached.\n     * @param {number} [config.maxAgeSeconds] The maximum age of an entry before\n     * it's treated as stale and removed.\n     * @param {Object} [config.matchOptions] The [`CacheQueryOptions`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/delete#Parameters)\n     * that will be used when calling `delete()` on the cache.\n     * @param {boolean} [config.purgeOnQuotaError] Whether to opt this cache in to\n     * automatic deletion if the available storage quota has been exceeded.\n     */\n    constructor(config = {}) {\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when a `Response` is about to be returned\n         * from a [Cache](https://developer.mozilla.org/en-US/docs/Web/API/Cache) to\n         * the handler. It allows the `Response` to be inspected for freshness and\n         * prevents it from being used if the `Response`'s `Date` header value is\n         * older than the configured `maxAgeSeconds`.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache the response is in.\n         * @param {Response} options.cachedResponse The `Response` object that's been\n         *     read from a cache and whose freshness should be checked.\n         * @return {Response} Either the `cachedResponse`, if it's\n         *     fresh, or `null` if the `Response` is older than `maxAgeSeconds`.\n         *\n         * @private\n         */\n        this.cachedResponseWillBeUsed = async ({ event, request, cacheName, cachedResponse, }) => {\n            if (!cachedResponse) {\n                return null;\n            }\n            const isFresh = this._isResponseDateFresh(cachedResponse);\n            // Expire entries to ensure that even if the expiration date has\n            // expired, it'll only be used once.\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            dontWaitFor(cacheExpiration.expireEntries());\n            // Update the metadata for the request URL to the current timestamp,\n            // but don't `await` it as we don't want to block the response.\n            const updateTimestampDone = cacheExpiration.updateTimestamp(request.url);\n            if (event) {\n                try {\n                    event.waitUntil(updateTimestampDone);\n                }\n                catch (error) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // The event may not be a fetch event; only log the URL if it is.\n                        if ('request' in event) {\n                            logger.warn(`Unable to ensure service worker stays alive when ` +\n                                `updating cache entry for ` +\n                                `'${getFriendlyURL(event.request.url)}'.`);\n                        }\n                    }\n                }\n            }\n            return isFresh ? cachedResponse : null;\n        };\n        /**\n         * A \"lifecycle\" callback that will be triggered automatically by the\n         * `workbox-strategies` handlers when an entry is added to a cache.\n         *\n         * @param {Object} options\n         * @param {string} options.cacheName Name of the cache that was updated.\n         * @param {string} options.request The Request for the cached entry.\n         *\n         * @private\n         */\n        this.cacheDidUpdate = async ({ cacheName, request, }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                assert.isType(cacheName, 'string', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'cacheName',\n                });\n                assert.isInstance(request, Request, {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'cacheDidUpdate',\n                    paramName: 'request',\n                });\n            }\n            const cacheExpiration = this._getCacheExpiration(cacheName);\n            await cacheExpiration.updateTimestamp(request.url);\n            await cacheExpiration.expireEntries();\n        };\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(config.maxEntries || config.maxAgeSeconds)) {\n                throw new WorkboxError('max-entries-or-age-required', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.maxEntries) {\n                assert.isType(config.maxEntries, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxEntries',\n                });\n            }\n            if (config.maxAgeSeconds) {\n                assert.isType(config.maxAgeSeconds, 'number', {\n                    moduleName: 'workbox-expiration',\n                    className: 'Plugin',\n                    funcName: 'constructor',\n                    paramName: 'config.maxAgeSeconds',\n                });\n            }\n        }\n        this._config = config;\n        this._maxAgeSeconds = config.maxAgeSeconds;\n        this._cacheExpirations = new Map();\n        if (config.purgeOnQuotaError) {\n            registerQuotaErrorCallback(() => this.deleteCacheAndMetadata());\n        }\n    }\n    /**\n     * A simple helper method to return a CacheExpiration instance for a given\n     * cache name.\n     *\n     * @param {string} cacheName\n     * @return {CacheExpiration}\n     *\n     * @private\n     */\n    _getCacheExpiration(cacheName) {\n        if (cacheName === cacheNames.getRuntimeName()) {\n            throw new WorkboxError('expire-custom-caches-only');\n        }\n        let cacheExpiration = this._cacheExpirations.get(cacheName);\n        if (!cacheExpiration) {\n            cacheExpiration = new CacheExpiration(cacheName, this._config);\n            this._cacheExpirations.set(cacheName, cacheExpiration);\n        }\n        return cacheExpiration;\n    }\n    /**\n     * @param {Response} cachedResponse\n     * @return {boolean}\n     *\n     * @private\n     */\n    _isResponseDateFresh(cachedResponse) {\n        if (!this._maxAgeSeconds) {\n            // We aren't expiring by age, so return true, it's fresh\n            return true;\n        }\n        // Check if the 'date' header will suffice a quick expiration check.\n        // See https://github.com/GoogleChromeLabs/sw-toolbox/issues/164 for\n        // discussion.\n        const dateHeaderTimestamp = this._getDateHeaderTimestamp(cachedResponse);\n        if (dateHeaderTimestamp === null) {\n            // Unable to parse date, so assume it's fresh.\n            return true;\n        }\n        // If we have a valid headerTime, then our response is fresh iff the\n        // headerTime plus maxAgeSeconds is greater than the current time.\n        const now = Date.now();\n        return dateHeaderTimestamp >= now - this._maxAgeSeconds * 1000;\n    }\n    /**\n     * This method will extract the data header and parse it into a useful\n     * value.\n     *\n     * @param {Response} cachedResponse\n     * @return {number|null}\n     *\n     * @private\n     */\n    _getDateHeaderTimestamp(cachedResponse) {\n        if (!cachedResponse.headers.has('date')) {\n            return null;\n        }\n        const dateHeader = cachedResponse.headers.get('date');\n        const parsedDate = new Date(dateHeader);\n        const headerTime = parsedDate.getTime();\n        // If the Date header was invalid for some reason, parsedDate.getTime()\n        // will return NaN.\n        if (isNaN(headerTime)) {\n            return null;\n        }\n        return headerTime;\n    }\n    /**\n     * This is a helper method that performs two operations:\n     *\n     * - Deletes *all* the underlying Cache instances associated with this plugin\n     * instance, by calling caches.delete() on your behalf.\n     * - Deletes the metadata from IndexedDB used to keep track of expiration\n     * details for each Cache instance.\n     *\n     * When using cache expiration, calling this method is preferable to calling\n     * `caches.delete()` directly, since this will ensure that the IndexedDB\n     * metadata is also cleanly removed and open IndexedDB instances are deleted.\n     *\n     * Note that if you're *not* using cache expiration for a given cache, calling\n     * `caches.delete()` and passing in the cache's name should be sufficient.\n     * There is no Workbox-specific method needed for cleanup in that case.\n     */\n    async deleteCacheAndMetadata() {\n        // Do this one at a time instead of all at once via `Promise.all()` to\n        // reduce the chance of inconsistency if a promise rejects.\n        for (const [cacheName, cacheExpiration] of this._cacheExpirations) {\n            await self.caches.delete(cacheName);\n            await cacheExpiration.delete();\n        }\n        // Reset this._cacheExpirations to its initial state.\n        this._cacheExpirations = new Map();\n    }\n}\nexport { ExpirationPlugin };\n", "/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nfunction stripParams(fullURL, ignoreParams) {\n    const strippedURL = new URL(fullURL);\n    for (const param of ignoreParams) {\n        strippedURL.searchParams.delete(param);\n    }\n    return strippedURL.href;\n}\n/**\n * Matches an item in the cache, ignoring specific URL params. This is similar\n * to the `ignoreSearch` option, but it allows you to ignore just specific\n * params (while continuing to match on the others).\n *\n * @private\n * @param {Cache} cache\n * @param {Request} request\n * @param {Object} matchOptions\n * @param {Array<string>} ignoreParams\n * @return {Promise<Response|undefined>}\n */\nasync function cacheMatchIgnoreParams(cache, request, ignoreParams, matchOptions) {\n    const strippedRequestURL = stripParams(request.url, ignoreParams);\n    // If the request doesn't include any ignored params, match as normal.\n    if (request.url === strippedRequestURL) {\n        return cache.match(request, matchOptions);\n    }\n    // Otherwise, match by comparing keys\n    const keysOptions = Object.assign(Object.assign({}, matchOptions), { ignoreSearch: true });\n    const cacheKeys = await cache.keys(request, keysOptions);\n    for (const cacheKey of cacheKeys) {\n        const strippedCacheKeyURL = stripParams(cacheKey.url, ignoreParams);\n        if (strippedRequestURL === strippedCacheKeyURL) {\n            return cache.match(cacheKey, matchOptions);\n        }\n    }\n    return;\n}\nexport { cacheMatchIgnoreParams };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The Deferred class composes Promises in a way that allows for them to be\n * resolved or rejected from outside the constructor. In most cases promises\n * should be used directly, but Deferreds can be necessary when the logic to\n * resolve a promise must be separate.\n *\n * @private\n */\nclass Deferred {\n    /**\n     * Creates a promise and exposes its resolve and reject functions as methods.\n     */\n    constructor() {\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n}\nexport { Deferred };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from '../_private/logger.js';\nimport { quotaErrorCallbacks } from '../models/quotaErrorCallbacks.js';\nimport '../_version.js';\n/**\n * Runs all of the callback functions, one at a time sequentially, in the order\n * in which they were registered.\n *\n * @memberof workbox-core\n * @private\n */\nasync function executeQuotaErrorCallbacks() {\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log(`About to run ${quotaErrorCallbacks.size} ` +\n            `callbacks to clean up caches.`);\n    }\n    for (const callback of quotaErrorCallbacks) {\n        await callback();\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(callback, 'is complete.');\n        }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Finished running callbacks.');\n    }\n}\nexport { executeQuotaErrorCallbacks };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Returns a promise that resolves and the passed number of milliseconds.\n * This utility is an async/await-friendly version of `setTimeout`.\n *\n * @param {number} ms\n * @return {Promise}\n * @private\n */\nexport function timeout(ms) {\n    return new Promise((resolve) => setTimeout(resolve, ms));\n}\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:strategies:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheMatchIgnoreParams } from 'workbox-core/_private/cacheMatchIgnoreParams.js';\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { executeQuotaErrorCallbacks } from 'workbox-core/_private/executeQuotaErrorCallbacks.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { timeout } from 'workbox-core/_private/timeout.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\nfunction toRequest(input) {\n    return typeof input === 'string' ? new Request(input) : input;\n}\n/**\n * A class created every time a Strategy instance instance calls\n * {@link workbox-strategies.Strategy~handle} or\n * {@link workbox-strategies.Strategy~handleAll} that wraps all fetch and\n * cache actions around plugin callbacks and keeps track of when the strategy\n * is \"done\" (i.e. all added `event.waitUntil()` promises have resolved).\n *\n * @memberof workbox-strategies\n */\nclass StrategyHandler {\n    /**\n     * Creates a new instance associated with the passed strategy and event\n     * that's handling the request.\n     *\n     * The constructor also initializes the state that will be passed to each of\n     * the plugins handling this request.\n     *\n     * @param {workbox-strategies.Strategy} strategy\n     * @param {Object} options\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params] The return value from the\n     *     {@link workbox-routing~matchCallback} (if applicable).\n     */\n    constructor(strategy, options) {\n        this._cacheKeys = {};\n        /**\n         * The request the strategy is performing (passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * @name request\n         * @instance\n         * @type {Request}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * The event associated with this request.\n         * @name event\n         * @instance\n         * @type {ExtendableEvent}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `URL` instance of `request.url` (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `url` param will be present if the strategy was invoked\n         * from a workbox `Route` object.\n         * @name url\n         * @instance\n         * @type {URL|undefined}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `param` value (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `param` param will be present if the strategy was invoked\n         * from a workbox `Route` object and the\n         * {@link workbox-routing~matchCallback} returned\n         * a truthy value (it will be that value).\n         * @name params\n         * @instance\n         * @type {*|undefined}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(options.event, ExtendableEvent, {\n                moduleName: 'workbox-strategies',\n                className: 'StrategyHandler',\n                funcName: 'constructor',\n                paramName: 'options.event',\n            });\n        }\n        Object.assign(this, options);\n        this.event = options.event;\n        this._strategy = strategy;\n        this._handlerDeferred = new Deferred();\n        this._extendLifetimePromises = [];\n        // Copy the plugins list (since it's mutable on the strategy),\n        // so any mutations don't affect this handler instance.\n        this._plugins = [...strategy.plugins];\n        this._pluginStateMap = new Map();\n        for (const plugin of this._plugins) {\n            this._pluginStateMap.set(plugin, {});\n        }\n        this.event.waitUntil(this._handlerDeferred.promise);\n    }\n    /**\n     * Fetches a given request (and invokes any applicable plugin callback\n     * methods) using the `fetchOptions` (for non-navigation requests) and\n     * `plugins` defined on the `Strategy` object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - `requestWillFetch()`\n     * - `fetchDidSucceed()`\n     * - `fetchDidFail()`\n     *\n     * @param {Request|string} input The URL or request to fetch.\n     * @return {Promise<Response>}\n     */\n    async fetch(input) {\n        const { event } = this;\n        let request = toRequest(input);\n        if (request.mode === 'navigate' &&\n            event instanceof FetchEvent &&\n            event.preloadResponse) {\n            const possiblePreloadResponse = (await event.preloadResponse);\n            if (possiblePreloadResponse) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Using a preloaded navigation response for ` +\n                        `'${getFriendlyURL(request.url)}'`);\n                }\n                return possiblePreloadResponse;\n            }\n        }\n        // If there is a fetchDidFail plugin, we need to save a clone of the\n        // original request before it's either modified by a requestWillFetch\n        // plugin or before the original request's body is consumed via fetch().\n        const originalRequest = this.hasCallback('fetchDidFail')\n            ? request.clone()\n            : null;\n        try {\n            for (const cb of this.iterateCallbacks('requestWillFetch')) {\n                request = await cb({ request: request.clone(), event });\n            }\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                throw new WorkboxError('plugin-error-request-will-fetch', {\n                    thrownErrorMessage: err.message,\n                });\n            }\n        }\n        // The request can be altered by plugins with `requestWillFetch` making\n        // the original request (most likely from a `fetch` event) different\n        // from the Request we make. Pass both to `fetchDidFail` to aid debugging.\n        const pluginFilteredRequest = request.clone();\n        try {\n            let fetchResponse;\n            // See https://github.com/GoogleChrome/workbox/issues/1796\n            fetchResponse = await fetch(request, request.mode === 'navigate' ? undefined : this._strategy.fetchOptions);\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' returned a response with ` +\n                    `status '${fetchResponse.status}'.`);\n            }\n            for (const callback of this.iterateCallbacks('fetchDidSucceed')) {\n                fetchResponse = await callback({\n                    event,\n                    request: pluginFilteredRequest,\n                    response: fetchResponse,\n                });\n            }\n            return fetchResponse;\n        }\n        catch (error) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' threw an error.`, error);\n            }\n            // `originalRequest` will only exist if a `fetchDidFail` callback\n            // is being used (see above).\n            if (originalRequest) {\n                await this.runCallbacks('fetchDidFail', {\n                    error: error,\n                    event,\n                    originalRequest: originalRequest.clone(),\n                    request: pluginFilteredRequest.clone(),\n                });\n            }\n            throw error;\n        }\n    }\n    /**\n     * Calls `this.fetch()` and (in the background) runs `this.cachePut()` on\n     * the response generated by `this.fetch()`.\n     *\n     * The call to `this.cachePut()` automatically invokes `this.waitUntil()`,\n     * so you do not have to manually call `waitUntil()` on the event.\n     *\n     * @param {Request|string} input The request or URL to fetch and cache.\n     * @return {Promise<Response>}\n     */\n    async fetchAndCachePut(input) {\n        const response = await this.fetch(input);\n        const responseClone = response.clone();\n        void this.waitUntil(this.cachePut(input, responseClone));\n        return response;\n    }\n    /**\n     * Matches a request from the cache (and invokes any applicable plugin\n     * callback methods) using the `cacheName`, `matchOptions`, and `plugins`\n     * defined on the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillBeUsed()\n     * - cachedResponseWillBeUsed()\n     *\n     * @param {Request|string} key The Request or URL to use as the cache key.\n     * @return {Promise<Response|undefined>} A matching response, if found.\n     */\n    async cacheMatch(key) {\n        const request = toRequest(key);\n        let cachedResponse;\n        const { cacheName, matchOptions } = this._strategy;\n        const effectiveRequest = await this.getCacheKey(request, 'read');\n        const multiMatchOptions = Object.assign(Object.assign({}, matchOptions), { cacheName });\n        cachedResponse = await caches.match(effectiveRequest, multiMatchOptions);\n        if (process.env.NODE_ENV !== 'production') {\n            if (cachedResponse) {\n                logger.debug(`Found a cached response in '${cacheName}'.`);\n            }\n            else {\n                logger.debug(`No cached response found in '${cacheName}'.`);\n            }\n        }\n        for (const callback of this.iterateCallbacks('cachedResponseWillBeUsed')) {\n            cachedResponse =\n                (await callback({\n                    cacheName,\n                    matchOptions,\n                    cachedResponse,\n                    request: effectiveRequest,\n                    event: this.event,\n                })) || undefined;\n        }\n        return cachedResponse;\n    }\n    /**\n     * Puts a request/response pair in the cache (and invokes any applicable\n     * plugin callback methods) using the `cacheName` and `plugins` defined on\n     * the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillBeUsed()\n     * - cacheWillUpdate()\n     * - cacheDidUpdate()\n     *\n     * @param {Request|string} key The request or URL to use as the cache key.\n     * @param {Response} response The response to cache.\n     * @return {Promise<boolean>} `false` if a cacheWillUpdate caused the response\n     * not be cached, and `true` otherwise.\n     */\n    async cachePut(key, response) {\n        const request = toRequest(key);\n        // Run in the next task to avoid blocking other cache reads.\n        // https://github.com/w3c/ServiceWorker/issues/1397\n        await timeout(0);\n        const effectiveRequest = await this.getCacheKey(request, 'write');\n        if (process.env.NODE_ENV !== 'production') {\n            if (effectiveRequest.method && effectiveRequest.method !== 'GET') {\n                throw new WorkboxError('attempt-to-cache-non-get-request', {\n                    url: getFriendlyURL(effectiveRequest.url),\n                    method: effectiveRequest.method,\n                });\n            }\n            // See https://github.com/GoogleChrome/workbox/issues/2818\n            const vary = response.headers.get('Vary');\n            if (vary) {\n                logger.debug(`The response for ${getFriendlyURL(effectiveRequest.url)} ` +\n                    `has a 'Vary: ${vary}' header. ` +\n                    `Consider setting the {ignoreVary: true} option on your strategy ` +\n                    `to ensure cache matching and deletion works as expected.`);\n            }\n        }\n        if (!response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error(`Cannot cache non-existent response for ` +\n                    `'${getFriendlyURL(effectiveRequest.url)}'.`);\n            }\n            throw new WorkboxError('cache-put-with-no-response', {\n                url: getFriendlyURL(effectiveRequest.url),\n            });\n        }\n        const responseToCache = await this._ensureResponseSafeToCache(response);\n        if (!responseToCache) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Response '${getFriendlyURL(effectiveRequest.url)}' ` +\n                    `will not be cached.`, responseToCache);\n            }\n            return false;\n        }\n        const { cacheName, matchOptions } = this._strategy;\n        const cache = await self.caches.open(cacheName);\n        const hasCacheUpdateCallback = this.hasCallback('cacheDidUpdate');\n        const oldResponse = hasCacheUpdateCallback\n            ? await cacheMatchIgnoreParams(\n            // TODO(philipwalton): the `__WB_REVISION__` param is a precaching\n            // feature. Consider into ways to only add this behavior if using\n            // precaching.\n            cache, effectiveRequest.clone(), ['__WB_REVISION__'], matchOptions)\n            : null;\n        if (process.env.NODE_ENV !== 'production') {\n            logger.debug(`Updating the '${cacheName}' cache with a new Response ` +\n                `for ${getFriendlyURL(effectiveRequest.url)}.`);\n        }\n        try {\n            await cache.put(effectiveRequest, hasCacheUpdateCallback ? responseToCache.clone() : responseToCache);\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                // See https://developer.mozilla.org/en-US/docs/Web/API/DOMException#exception-QuotaExceededError\n                if (error.name === 'QuotaExceededError') {\n                    await executeQuotaErrorCallbacks();\n                }\n                throw error;\n            }\n        }\n        for (const callback of this.iterateCallbacks('cacheDidUpdate')) {\n            await callback({\n                cacheName,\n                oldResponse,\n                newResponse: responseToCache.clone(),\n                request: effectiveRequest,\n                event: this.event,\n            });\n        }\n        return true;\n    }\n    /**\n     * Checks the list of plugins for the `cacheKeyWillBeUsed` callback, and\n     * executes any of those callbacks found in sequence. The final `Request`\n     * object returned by the last plugin is treated as the cache key for cache\n     * reads and/or writes. If no `cacheKeyWillBeUsed` plugin callbacks have\n     * been registered, the passed request is returned unmodified\n     *\n     * @param {Request} request\n     * @param {string} mode\n     * @return {Promise<Request>}\n     */\n    async getCacheKey(request, mode) {\n        const key = `${request.url} | ${mode}`;\n        if (!this._cacheKeys[key]) {\n            let effectiveRequest = request;\n            for (const callback of this.iterateCallbacks('cacheKeyWillBeUsed')) {\n                effectiveRequest = toRequest(await callback({\n                    mode,\n                    request: effectiveRequest,\n                    event: this.event,\n                    // params has a type any can't change right now.\n                    params: this.params, // eslint-disable-line\n                }));\n            }\n            this._cacheKeys[key] = effectiveRequest;\n        }\n        return this._cacheKeys[key];\n    }\n    /**\n     * Returns true if the strategy has at least one plugin with the given\n     * callback.\n     *\n     * @param {string} name The name of the callback to check for.\n     * @return {boolean}\n     */\n    hasCallback(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (name in plugin) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n     * Runs all plugin callbacks matching the given name, in order, passing the\n     * given param object (merged ith the current plugin state) as the only\n     * argument.\n     *\n     * Note: since this method runs all plugins, it's not suitable for cases\n     * where the return value of a callback needs to be applied prior to calling\n     * the next callback. See\n     * {@link workbox-strategies.StrategyHandler#iterateCallbacks}\n     * below for how to handle that case.\n     *\n     * @param {string} name The name of the callback to run within each plugin.\n     * @param {Object} param The object to pass as the first (and only) param\n     *     when executing each callback. This object will be merged with the\n     *     current plugin state prior to callback execution.\n     */\n    async runCallbacks(name, param) {\n        for (const callback of this.iterateCallbacks(name)) {\n            // TODO(philipwalton): not sure why `any` is needed. It seems like\n            // this should work with `as WorkboxPluginCallbackParam[C]`.\n            await callback(param);\n        }\n    }\n    /**\n     * Accepts a callback and returns an iterable of matching plugin callbacks,\n     * where each callback is wrapped with the current handler state (i.e. when\n     * you call each callback, whatever object parameter you pass it will\n     * be merged with the plugin's current state).\n     *\n     * @param {string} name The name fo the callback to run\n     * @return {Array<Function>}\n     */\n    *iterateCallbacks(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (typeof plugin[name] === 'function') {\n                const state = this._pluginStateMap.get(plugin);\n                const statefulCallback = (param) => {\n                    const statefulParam = Object.assign(Object.assign({}, param), { state });\n                    // TODO(philipwalton): not sure why `any` is needed. It seems like\n                    // this should work with `as WorkboxPluginCallbackParam[C]`.\n                    return plugin[name](statefulParam);\n                };\n                yield statefulCallback;\n            }\n        }\n    }\n    /**\n     * Adds a promise to the\n     * [extend lifetime promises]{@link https://w3c.github.io/ServiceWorker/#extendableevent-extend-lifetime-promises}\n     * of the event event associated with the request being handled (usually a\n     * `FetchEvent`).\n     *\n     * Note: you can await\n     * {@link workbox-strategies.StrategyHandler~doneWaiting}\n     * to know when all added promises have settled.\n     *\n     * @param {Promise} promise A promise to add to the extend lifetime promises\n     *     of the event that triggered the request.\n     */\n    waitUntil(promise) {\n        this._extendLifetimePromises.push(promise);\n        return promise;\n    }\n    /**\n     * Returns a promise that resolves once all promises passed to\n     * {@link workbox-strategies.StrategyHandler~waitUntil}\n     * have settled.\n     *\n     * Note: any work done after `doneWaiting()` settles should be manually\n     * passed to an event's `waitUntil()` method (not this handler's\n     * `waitUntil()` method), otherwise the service worker thread my be killed\n     * prior to your work completing.\n     */\n    async doneWaiting() {\n        let promise;\n        while ((promise = this._extendLifetimePromises.shift())) {\n            await promise;\n        }\n    }\n    /**\n     * Stops running the strategy and immediately resolves any pending\n     * `waitUntil()` promises.\n     */\n    destroy() {\n        this._handlerDeferred.resolve(null);\n    }\n    /**\n     * This method will call cacheWillUpdate on the available plugins (or use\n     * status === 200) to determine if the Response is safe and valid to cache.\n     *\n     * @param {Request} options.request\n     * @param {Response} options.response\n     * @return {Promise<Response|undefined>}\n     *\n     * @private\n     */\n    async _ensureResponseSafeToCache(response) {\n        let responseToCache = response;\n        let pluginsUsed = false;\n        for (const callback of this.iterateCallbacks('cacheWillUpdate')) {\n            responseToCache =\n                (await callback({\n                    request: this.request,\n                    response: responseToCache,\n                    event: this.event,\n                })) || undefined;\n            pluginsUsed = true;\n            if (!responseToCache) {\n                break;\n            }\n        }\n        if (!pluginsUsed) {\n            if (responseToCache && responseToCache.status !== 200) {\n                responseToCache = undefined;\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (responseToCache) {\n                    if (responseToCache.status !== 200) {\n                        if (responseToCache.status === 0) {\n                            logger.warn(`The response for '${this.request.url}' ` +\n                                `is an opaque response. The caching strategy that you're ` +\n                                `using will not cache opaque responses by default.`);\n                        }\n                        else {\n                            logger.debug(`The response for '${this.request.url}' ` +\n                                `returned a status code of '${response.status}' and won't ` +\n                                `be cached as a result.`);\n                        }\n                    }\n                }\n            }\n        }\n        return responseToCache;\n    }\n}\nexport { StrategyHandler };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { StrategyHandler } from './StrategyHandler.js';\nimport './_version.js';\n/**\n * An abstract base class that all other strategy classes must extend from:\n *\n * @memberof workbox-strategies\n */\nclass Strategy {\n    /**\n     * Creates a new instance of the strategy and sets all documented option\n     * properties as public instance properties.\n     *\n     * Note: if a custom strategy class extends the base Strategy class and does\n     * not need more than these properties, it does not need to define its own\n     * constructor.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     */\n    constructor(options = {}) {\n        /**\n         * Cache name to store and retrieve\n         * requests. Defaults to the cache names provided by\n         * {@link workbox-core.cacheNames}.\n         *\n         * @type {string}\n         */\n        this.cacheName = cacheNames.getRuntimeName(options.cacheName);\n        /**\n         * The list\n         * [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n         * used by this strategy.\n         *\n         * @type {Array<Object>}\n         */\n        this.plugins = options.plugins || [];\n        /**\n         * Values passed along to the\n         * [`init`]{@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters}\n         * of all fetch() requests made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.fetchOptions = options.fetchOptions;\n        /**\n         * The\n         * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n         * for any `cache.match()` or `cache.put()` calls made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.matchOptions = options.matchOptions;\n    }\n    /**\n     * Perform a request strategy and returns a `Promise` that will resolve with\n     * a `Response`, invoking all relevant plugin callbacks.\n     *\n     * When a strategy instance is registered with a Workbox\n     * {@link workbox-routing.Route}, this method is automatically\n     * called when the route matches.\n     *\n     * Alternatively, this method can be used in a standalone `FetchEvent`\n     * listener by passing it to `event.respondWith()`.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     */\n    handle(options) {\n        const [responseDone] = this.handleAll(options);\n        return responseDone;\n    }\n    /**\n     * Similar to {@link workbox-strategies.Strategy~handle}, but\n     * instead of just returning a `Promise` that resolves to a `Response` it\n     * it will return an tuple of `[response, done]` promises, where the former\n     * (`response`) is equivalent to what `handle()` returns, and the latter is a\n     * Promise that will resolve once any promises that were added to\n     * `event.waitUntil()` as part of performing the strategy have completed.\n     *\n     * You can await the `done` promise to ensure any extra work performed by\n     * the strategy (usually caching responses) completes successfully.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     * @return {Array<Promise>} A tuple of [response, done]\n     *     promises that can be used to determine when the response resolves as\n     *     well as when the handler has completed all its work.\n     */\n    handleAll(options) {\n        // Allow for flexible options to be passed.\n        if (options instanceof FetchEvent) {\n            options = {\n                event: options,\n                request: options.request,\n            };\n        }\n        const event = options.event;\n        const request = typeof options.request === 'string'\n            ? new Request(options.request)\n            : options.request;\n        const params = 'params' in options ? options.params : undefined;\n        const handler = new StrategyHandler(this, { event, request, params });\n        const responseDone = this._getResponse(handler, request, event);\n        const handlerDone = this._awaitComplete(responseDone, handler, request, event);\n        // Return an array of promises, suitable for use with Promise.all().\n        return [responseDone, handlerDone];\n    }\n    async _getResponse(handler, request, event) {\n        await handler.runCallbacks('handlerWillStart', { event, request });\n        let response = undefined;\n        try {\n            response = await this._handle(request, handler);\n            // The \"official\" Strategy subclasses all throw this error automatically,\n            // but in case a third-party Strategy doesn't, ensure that we have a\n            // consistent failure when there's no response or an error response.\n            if (!response || response.type === 'error') {\n                throw new WorkboxError('no-response', { url: request.url });\n            }\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                for (const callback of handler.iterateCallbacks('handlerDidError')) {\n                    response = await callback({ error, event, request });\n                    if (response) {\n                        break;\n                    }\n                }\n            }\n            if (!response) {\n                throw error;\n            }\n            else if (process.env.NODE_ENV !== 'production') {\n                logger.log(`While responding to '${getFriendlyURL(request.url)}', ` +\n                    `an ${error instanceof Error ? error.toString() : ''} error occurred. Using a fallback response provided by ` +\n                    `a handlerDidError plugin.`);\n            }\n        }\n        for (const callback of handler.iterateCallbacks('handlerWillRespond')) {\n            response = await callback({ event, request, response });\n        }\n        return response;\n    }\n    async _awaitComplete(responseDone, handler, request, event) {\n        let response;\n        let error;\n        try {\n            response = await responseDone;\n        }\n        catch (error) {\n            // Ignore errors, as response errors should be caught via the `response`\n            // promise above. The `done` promise will only throw for errors in\n            // promises passed to `handler.waitUntil()`.\n        }\n        try {\n            await handler.runCallbacks('handlerDidRespond', {\n                event,\n                request,\n                response,\n            });\n            await handler.doneWaiting();\n        }\n        catch (waitUntilError) {\n            if (waitUntilError instanceof Error) {\n                error = waitUntilError;\n            }\n        }\n        await handler.runCallbacks('handlerDidComplete', {\n            event,\n            request,\n            response,\n            error: error,\n        });\n        handler.destroy();\n        if (error) {\n            throw error;\n        }\n    }\n}\nexport { Strategy };\n/**\n * Classes extending the `Strategy` based class should implement this method,\n * and leverage the {@link workbox-strategies.StrategyHandler}\n * arg to perform all fetching and cache logic, which will ensure all relevant\n * cache, cache options, fetch options and plugins are used (per the current\n * strategy instance).\n *\n * @name _handle\n * @instance\n * @abstract\n * @function\n * @param {Request} request\n * @param {workbox-strategies.StrategyHandler} handler\n * @return {Promise<Response>}\n *\n * @memberof workbox-strategies.Strategy\n */\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport '../_version.js';\nexport const messages = {\n    strategyStart: (strategyName, request) => `Using ${strategyName} to respond to '${getFriendlyURL(request.url)}'`,\n    printFinalResponse: (response) => {\n        if (response) {\n            logger.groupCollapsed(`View the final response here.`);\n            logger.log(response || '[No response returned]');\n            logger.groupEnd();\n        }\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a [cache-first](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#cache-first-falling-back-to-network)\n * request strategy.\n *\n * A cache first strategy is useful for assets that have been revisioned,\n * such as URLs like `/styles/example.a8f5f1.css`, since they\n * can be cached for long periods of time.\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass CacheFirst extends Strategy {\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'makeRequest',\n                paramName: 'request',\n            });\n        }\n        let response = await handler.cacheMatch(request);\n        let error = undefined;\n        if (!response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`No response found in the '${this.cacheName}' cache. ` +\n                    `Will respond with a network request.`);\n            }\n            try {\n                response = await handler.fetchAndCachePut(request);\n            }\n            catch (err) {\n                if (err instanceof Error) {\n                    error = err;\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (response) {\n                    logs.push(`Got response from network.`);\n                }\n                else {\n                    logs.push(`Unable to get a response from the network.`);\n                }\n            }\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`Found a cached response in the '${this.cacheName}' cache.`);\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { CacheFirst };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:cacheable-response:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport './_version.js';\n/**\n * This class allows you to set up rules determining what\n * status codes and/or headers need to be present in order for a\n * [`Response`](https://developer.mozilla.org/en-US/docs/Web/API/Response)\n * to be considered cacheable.\n *\n * @memberof workbox-cacheable-response\n */\nclass CacheableResponse {\n    /**\n     * To construct a new CacheableResponse instance you must provide at least\n     * one of the `config` properties.\n     *\n     * If both `statuses` and `headers` are specified, then both conditions must\n     * be met for the `Response` to be considered cacheable.\n     *\n     * @param {Object} config\n     * @param {Array<number>} [config.statuses] One or more status codes that a\n     * `Response` can have and be considered cacheable.\n     * @param {Object<string,string>} [config.headers] A mapping of header names\n     * and expected values that a `Response` can have and be considered cacheable.\n     * If multiple headers are provided, only one needs to be present.\n     */\n    constructor(config = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(config.statuses || config.headers)) {\n                throw new WorkboxError('statuses-or-headers-required', {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                });\n            }\n            if (config.statuses) {\n                assert.isArray(config.statuses, {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                    paramName: 'config.statuses',\n                });\n            }\n            if (config.headers) {\n                assert.isType(config.headers, 'object', {\n                    moduleName: 'workbox-cacheable-response',\n                    className: 'CacheableResponse',\n                    funcName: 'constructor',\n                    paramName: 'config.headers',\n                });\n            }\n        }\n        this._statuses = config.statuses;\n        this._headers = config.headers;\n    }\n    /**\n     * Checks a response to see whether it's cacheable or not, based on this\n     * object's configuration.\n     *\n     * @param {Response} response The response whose cacheability is being\n     * checked.\n     * @return {boolean} `true` if the `Response` is cacheable, and `false`\n     * otherwise.\n     */\n    isResponseCacheable(response) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(response, Response, {\n                moduleName: 'workbox-cacheable-response',\n                className: 'CacheableResponse',\n                funcName: 'isResponseCacheable',\n                paramName: 'response',\n            });\n        }\n        let cacheable = true;\n        if (this._statuses) {\n            cacheable = this._statuses.includes(response.status);\n        }\n        if (this._headers && cacheable) {\n            cacheable = Object.keys(this._headers).some((headerName) => {\n                return response.headers.get(headerName) === this._headers[headerName];\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (!cacheable) {\n                logger.groupCollapsed(`The request for ` +\n                    `'${getFriendlyURL(response.url)}' returned a response that does ` +\n                    `not meet the criteria for being cached.`);\n                logger.groupCollapsed(`View cacheability criteria here.`);\n                logger.log(`Cacheable statuses: ` + JSON.stringify(this._statuses));\n                logger.log(`Cacheable headers: ` + JSON.stringify(this._headers, null, 2));\n                logger.groupEnd();\n                const logFriendlyHeaders = {};\n                response.headers.forEach((value, key) => {\n                    logFriendlyHeaders[key] = value;\n                });\n                logger.groupCollapsed(`View response status and headers here.`);\n                logger.log(`Response status: ${response.status}`);\n                logger.log(`Response headers: ` + JSON.stringify(logFriendlyHeaders, null, 2));\n                logger.groupEnd();\n                logger.groupCollapsed(`View full response details here.`);\n                logger.log(response.headers);\n                logger.log(response);\n                logger.groupEnd();\n                logger.groupEnd();\n            }\n        }\n        return cacheable;\n    }\n}\nexport { CacheableResponse };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { CacheableResponse, } from './CacheableResponse.js';\nimport './_version.js';\n/**\n * A class implementing the `cacheWillUpdate` lifecycle callback. This makes it\n * easier to add in cacheability checks to requests made via Workbox's built-in\n * strategies.\n *\n * @memberof workbox-cacheable-response\n */\nclass CacheableResponsePlugin {\n    /**\n     * To construct a new CacheableResponsePlugin instance you must provide at\n     * least one of the `config` properties.\n     *\n     * If both `statuses` and `headers` are specified, then both conditions must\n     * be met for the `Response` to be considered cacheable.\n     *\n     * @param {Object} config\n     * @param {Array<number>} [config.statuses] One or more status codes that a\n     * `Response` can have and be considered cacheable.\n     * @param {Object<string,string>} [config.headers] A mapping of header names\n     * and expected values that a `Response` can have and be considered cacheable.\n     * If multiple headers are provided, only one needs to be present.\n     */\n    constructor(config) {\n        /**\n         * @param {Object} options\n         * @param {Response} options.response\n         * @return {Response|null}\n         * @private\n         */\n        this.cacheWillUpdate = async ({ response }) => {\n            if (this._cacheableResponse.isResponseCacheable(response)) {\n                return response;\n            }\n            return null;\n        };\n        this._cacheableResponse = new CacheableResponse(config);\n    }\n}\nexport { CacheableResponsePlugin };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nexport const cacheOkAndOpaquePlugin = {\n    /**\n     * Returns a valid response (to allow caching) if the status is 200 (OK) or\n     * 0 (opaque).\n     *\n     * @param {Object} options\n     * @param {Response} options.response\n     * @return {Response|null}\n     *\n     * @private\n     */\n    cacheWillUpdate: async ({ response }) => {\n        if (response.status === 200 || response.status === 0) {\n            return response;\n        }\n        return null;\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { cacheOkAndOpaquePlugin } from './plugins/cacheOkAndOpaquePlugin.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [stale-while-revalidate](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#stale-while-revalidate)\n * request strategy.\n *\n * Resources are requested from both the cache and the network in parallel.\n * The strategy will respond with the cached version if available, otherwise\n * wait for the network response. The cache is updated with the network response\n * with each successful request.\n *\n * By default, this strategy will cache responses with a 200 status code as\n * well as [opaque responses](https://developer.chrome.com/docs/workbox/caching-resources-during-runtime/#opaque-responses).\n * Opaque responses are cross-origin requests where the response doesn't\n * support [CORS](https://enable-cors.org/).\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass StaleWhileRevalidate extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] [`CacheQueryOptions`](https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions)\n     */\n    constructor(options = {}) {\n        super(options);\n        // If this instance contains no plugins with a 'cacheWillUpdate' callback,\n        // prepend the `cacheOkAndOpaquePlugin` plugin to the plugins list.\n        if (!this.plugins.some((p) => 'cacheWillUpdate' in p)) {\n            this.plugins.unshift(cacheOkAndOpaquePlugin);\n        }\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'handle',\n                paramName: 'request',\n            });\n        }\n        const fetchAndCachePromise = handler.fetchAndCachePut(request).catch(() => {\n            // Swallow this error because a 'no-response' error will be thrown in\n            // main handler return flow. This will be in the `waitUntil()` flow.\n        });\n        void handler.waitUntil(fetchAndCachePromise);\n        let response = await handler.cacheMatch(request);\n        let error;\n        if (response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`Found a cached response in the '${this.cacheName}'` +\n                    ` cache. Will update with the network response in the background.`);\n            }\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`No response found in the '${this.cacheName}' cache. ` +\n                    `Will wait for the network response.`);\n            }\n            try {\n                // NOTE(philipwalton): Really annoying that we have to type cast here.\n                // https://github.com/microsoft/TypeScript/issues/20006\n                response = (await fetchAndCachePromise);\n            }\n            catch (err) {\n                if (err instanceof Error) {\n                    error = err;\n                }\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { StaleWhileRevalidate };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { cacheOkAndOpaquePlugin } from './plugins/cacheOkAndOpaquePlugin.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [network first](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#network-first-falling-back-to-cache)\n * request strategy.\n *\n * By default, this strategy will cache responses with a 200 status code as\n * well as [opaque responses](https://developer.chrome.com/docs/workbox/caching-resources-during-runtime/#opaque-responses).\n * Opaque responses are are cross-origin requests where the response doesn't\n * support [CORS](https://enable-cors.org/).\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass NetworkFirst extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] [`CacheQueryOptions`](https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions)\n     * @param {number} [options.networkTimeoutSeconds] If set, any network requests\n     * that fail to respond within the timeout will fallback to the cache.\n     *\n     * This option can be used to combat\n     * \"[lie-fi]{@link https://developers.google.com/web/fundamentals/performance/poor-connectivity/#lie-fi}\"\n     * scenarios.\n     */\n    constructor(options = {}) {\n        super(options);\n        // If this instance contains no plugins with a 'cacheWillUpdate' callback,\n        // prepend the `cacheOkAndOpaquePlugin` plugin to the plugins list.\n        if (!this.plugins.some((p) => 'cacheWillUpdate' in p)) {\n            this.plugins.unshift(cacheOkAndOpaquePlugin);\n        }\n        this._networkTimeoutSeconds = options.networkTimeoutSeconds || 0;\n        if (process.env.NODE_ENV !== 'production') {\n            if (this._networkTimeoutSeconds) {\n                assert.isType(this._networkTimeoutSeconds, 'number', {\n                    moduleName: 'workbox-strategies',\n                    className: this.constructor.name,\n                    funcName: 'constructor',\n                    paramName: 'networkTimeoutSeconds',\n                });\n            }\n        }\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'handle',\n                paramName: 'makeRequest',\n            });\n        }\n        const promises = [];\n        let timeoutId;\n        if (this._networkTimeoutSeconds) {\n            const { id, promise } = this._getTimeoutPromise({ request, logs, handler });\n            timeoutId = id;\n            promises.push(promise);\n        }\n        const networkPromise = this._getNetworkPromise({\n            timeoutId,\n            request,\n            logs,\n            handler,\n        });\n        promises.push(networkPromise);\n        const response = await handler.waitUntil((async () => {\n            // Promise.race() will resolve as soon as the first promise resolves.\n            return ((await handler.waitUntil(Promise.race(promises))) ||\n                // If Promise.race() resolved with null, it might be due to a network\n                // timeout + a cache miss. If that were to happen, we'd rather wait until\n                // the networkPromise resolves instead of returning null.\n                // Note that it's fine to await an already-resolved promise, so we don't\n                // have to check to see if it's still \"in flight\".\n                (await networkPromise));\n        })());\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url });\n        }\n        return response;\n    }\n    /**\n     * @param {Object} options\n     * @param {Request} options.request\n     * @param {Array} options.logs A reference to the logs array\n     * @param {Event} options.event\n     * @return {Promise<Response>}\n     *\n     * @private\n     */\n    _getTimeoutPromise({ request, logs, handler, }) {\n        let timeoutId;\n        const timeoutPromise = new Promise((resolve) => {\n            const onNetworkTimeout = async () => {\n                if (process.env.NODE_ENV !== 'production') {\n                    logs.push(`Timing out the network response at ` +\n                        `${this._networkTimeoutSeconds} seconds.`);\n                }\n                resolve(await handler.cacheMatch(request));\n            };\n            timeoutId = setTimeout(onNetworkTimeout, this._networkTimeoutSeconds * 1000);\n        });\n        return {\n            promise: timeoutPromise,\n            id: timeoutId,\n        };\n    }\n    /**\n     * @param {Object} options\n     * @param {number|undefined} options.timeoutId\n     * @param {Request} options.request\n     * @param {Array} options.logs A reference to the logs Array.\n     * @param {Event} options.event\n     * @return {Promise<Response>}\n     *\n     * @private\n     */\n    async _getNetworkPromise({ timeoutId, request, logs, handler, }) {\n        let error;\n        let response;\n        try {\n            response = await handler.fetchAndCachePut(request);\n        }\n        catch (fetchError) {\n            if (fetchError instanceof Error) {\n                error = fetchError;\n            }\n        }\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (response) {\n                logs.push(`Got response from network.`);\n            }\n            else {\n                logs.push(`Unable to get a response from the network. Will respond ` +\n                    `with a cached response.`);\n            }\n        }\n        if (error || !response) {\n            response = await handler.cacheMatch(request);\n            if (process.env.NODE_ENV !== 'production') {\n                if (response) {\n                    logs.push(`Found a cached response in the '${this.cacheName}'` + ` cache.`);\n                }\n                else {\n                    logs.push(`No response found in the '${this.cacheName}' cache.`);\n                }\n            }\n        }\n        return response;\n    }\n}\nexport { NetworkFirst };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:background-sync:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2021 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { openDB } from 'idb';\nimport '../_version.js';\nconst DB_VERSION = 3;\nconst DB_NAME = 'workbox-background-sync';\nconst REQUEST_OBJECT_STORE_NAME = 'requests';\nconst QUEUE_NAME_INDEX = 'queueName';\n/**\n * A class to interact directly an IndexedDB created specifically to save and\n * retrieve QueueStoreEntries. This class encapsulates all the schema details\n * to store the representation of a Queue.\n *\n * @private\n */\nexport class QueueDb {\n    constructor() {\n        this._db = null;\n    }\n    /**\n     * Add QueueStoreEntry to underlying db.\n     *\n     * @param {UnidentifiedQueueStoreEntry} entry\n     */\n    async addEntry(entry) {\n        const db = await this.getDb();\n        const tx = db.transaction(REQUEST_OBJECT_STORE_NAME, 'readwrite', {\n            durability: 'relaxed',\n        });\n        await tx.store.add(entry);\n        await tx.done;\n    }\n    /**\n     * Returns the first entry id in the ObjectStore.\n     *\n     * @return {number | undefined}\n     */\n    async getFirstEntryId() {\n        const db = await this.getDb();\n        const cursor = await db\n            .transaction(REQUEST_OBJECT_STORE_NAME)\n            .store.openCursor();\n        return cursor === null || cursor === void 0 ? void 0 : cursor.value.id;\n    }\n    /**\n     * Get all the entries filtered by index\n     *\n     * @param queueName\n     * @return {Promise<QueueStoreEntry[]>}\n     */\n    async getAllEntriesByQueueName(queueName) {\n        const db = await this.getDb();\n        const results = await db.getAllFromIndex(REQUEST_OBJECT_STORE_NAME, QUEUE_NAME_INDEX, IDBKeyRange.only(queueName));\n        return results ? results : new Array();\n    }\n    /**\n     * Returns the number of entries filtered by index\n     *\n     * @param queueName\n     * @return {Promise<number>}\n     */\n    async getEntryCountByQueueName(queueName) {\n        const db = await this.getDb();\n        return db.countFromIndex(REQUEST_OBJECT_STORE_NAME, QUEUE_NAME_INDEX, IDBKeyRange.only(queueName));\n    }\n    /**\n     * Deletes a single entry by id.\n     *\n     * @param {number} id the id of the entry to be deleted\n     */\n    async deleteEntry(id) {\n        const db = await this.getDb();\n        await db.delete(REQUEST_OBJECT_STORE_NAME, id);\n    }\n    /**\n     *\n     * @param queueName\n     * @returns {Promise<QueueStoreEntry | undefined>}\n     */\n    async getFirstEntryByQueueName(queueName) {\n        return await this.getEndEntryFromIndex(IDBKeyRange.only(queueName), 'next');\n    }\n    /**\n     *\n     * @param queueName\n     * @returns {Promise<QueueStoreEntry | undefined>}\n     */\n    async getLastEntryByQueueName(queueName) {\n        return await this.getEndEntryFromIndex(IDBKeyRange.only(queueName), 'prev');\n    }\n    /**\n     * Returns either the first or the last entries, depending on direction.\n     * Filtered by index.\n     *\n     * @param {IDBCursorDirection} direction\n     * @param {IDBKeyRange} query\n     * @return {Promise<QueueStoreEntry | undefined>}\n     * @private\n     */\n    async getEndEntryFromIndex(query, direction) {\n        const db = await this.getDb();\n        const cursor = await db\n            .transaction(REQUEST_OBJECT_STORE_NAME)\n            .store.index(QUEUE_NAME_INDEX)\n            .openCursor(query, direction);\n        return cursor === null || cursor === void 0 ? void 0 : cursor.value;\n    }\n    /**\n     * Returns an open connection to the database.\n     *\n     * @private\n     */\n    async getDb() {\n        if (!this._db) {\n            this._db = await openDB(DB_NAME, DB_VERSION, {\n                upgrade: this._upgradeDb,\n            });\n        }\n        return this._db;\n    }\n    /**\n     * Upgrades QueueDB\n     *\n     * @param {IDBPDatabase<QueueDBSchema>} db\n     * @param {number} oldVersion\n     * @private\n     */\n    _upgradeDb(db, oldVersion) {\n        if (oldVersion > 0 && oldVersion < DB_VERSION) {\n            if (db.objectStoreNames.contains(REQUEST_OBJECT_STORE_NAME)) {\n                db.deleteObjectStore(REQUEST_OBJECT_STORE_NAME);\n            }\n        }\n        const objStore = db.createObjectStore(REQUEST_OBJECT_STORE_NAME, {\n            autoIncrement: true,\n            keyPath: 'id',\n        });\n        objStore.createIndex(QUEUE_NAME_INDEX, QUEUE_NAME_INDEX, { unique: false });\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { QueueDb, } from './QueueDb.js';\nimport '../_version.js';\n/**\n * A class to manage storing requests from a Queue in IndexedDB,\n * indexed by their queue name for easier access.\n *\n * Most developers will not need to access this class directly;\n * it is exposed for advanced use cases.\n */\nexport class QueueStore {\n    /**\n     * Associates this instance with a Queue instance, so entries added can be\n     * identified by their queue name.\n     *\n     * @param {string} queueName\n     */\n    constructor(queueName) {\n        this._queueName = queueName;\n        this._queueDb = new QueueDb();\n    }\n    /**\n     * Append an entry last in the queue.\n     *\n     * @param {Object} entry\n     * @param {Object} entry.requestData\n     * @param {number} [entry.timestamp]\n     * @param {Object} [entry.metadata]\n     */\n    async pushEntry(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'pushEntry',\n                paramName: 'entry',\n            });\n            assert.isType(entry.requestData, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'pushEntry',\n                paramName: 'entry.requestData',\n            });\n        }\n        // Don't specify an ID since one is automatically generated.\n        delete entry.id;\n        entry.queueName = this._queueName;\n        await this._queueDb.addEntry(entry);\n    }\n    /**\n     * Prepend an entry first in the queue.\n     *\n     * @param {Object} entry\n     * @param {Object} entry.requestData\n     * @param {number} [entry.timestamp]\n     * @param {Object} [entry.metadata]\n     */\n    async unshiftEntry(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'unshiftEntry',\n                paramName: 'entry',\n            });\n            assert.isType(entry.requestData, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'QueueStore',\n                funcName: 'unshiftEntry',\n                paramName: 'entry.requestData',\n            });\n        }\n        const firstId = await this._queueDb.getFirstEntryId();\n        if (firstId) {\n            // Pick an ID one less than the lowest ID in the object store.\n            entry.id = firstId - 1;\n        }\n        else {\n            // Otherwise let the auto-incrementor assign the ID.\n            delete entry.id;\n        }\n        entry.queueName = this._queueName;\n        await this._queueDb.addEntry(entry);\n    }\n    /**\n     * Removes and returns the last entry in the queue matching the `queueName`.\n     *\n     * @return {Promise<QueueStoreEntry|undefined>}\n     */\n    async popEntry() {\n        return this._removeEntry(await this._queueDb.getLastEntryByQueueName(this._queueName));\n    }\n    /**\n     * Removes and returns the first entry in the queue matching the `queueName`.\n     *\n     * @return {Promise<QueueStoreEntry|undefined>}\n     */\n    async shiftEntry() {\n        return this._removeEntry(await this._queueDb.getFirstEntryByQueueName(this._queueName));\n    }\n    /**\n     * Returns all entries in the store matching the `queueName`.\n     *\n     * @param {Object} options See {@link workbox-background-sync.Queue~getAll}\n     * @return {Promise<Array<Object>>}\n     */\n    async getAll() {\n        return await this._queueDb.getAllEntriesByQueueName(this._queueName);\n    }\n    /**\n     * Returns the number of entries in the store matching the `queueName`.\n     *\n     * @param {Object} options See {@link workbox-background-sync.Queue~size}\n     * @return {Promise<number>}\n     */\n    async size() {\n        return await this._queueDb.getEntryCountByQueueName(this._queueName);\n    }\n    /**\n     * Deletes the entry for the given ID.\n     *\n     * WARNING: this method does not ensure the deleted entry belongs to this\n     * queue (i.e. matches the `queueName`). But this limitation is acceptable\n     * as this class is not publicly exposed. An additional check would make\n     * this method slower than it needs to be.\n     *\n     * @param {number} id\n     */\n    async deleteEntry(id) {\n        await this._queueDb.deleteEntry(id);\n    }\n    /**\n     * Removes and returns the first or last entry in the queue (based on the\n     * `direction` argument) matching the `queueName`.\n     *\n     * @return {Promise<QueueStoreEntry|undefined>}\n     * @private\n     */\n    async _removeEntry(entry) {\n        if (entry) {\n            await this.deleteEntry(entry.id);\n        }\n        return entry;\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\nconst serializableProperties = [\n    'method',\n    'referrer',\n    'referrerPolicy',\n    'mode',\n    'credentials',\n    'cache',\n    'redirect',\n    'integrity',\n    'keepalive',\n];\n/**\n * A class to make it easier to serialize and de-serialize requests so they\n * can be stored in IndexedDB.\n *\n * Most developers will not need to access this class directly;\n * it is exposed for advanced use cases.\n */\nclass StorableRequest {\n    /**\n     * Converts a Request object to a plain object that can be structured\n     * cloned or JSON-stringified.\n     *\n     * @param {Request} request\n     * @return {Promise<StorableRequest>}\n     */\n    static async fromRequest(request) {\n        const requestData = {\n            url: request.url,\n            headers: {},\n        };\n        // Set the body if present.\n        if (request.method !== 'GET') {\n            // Use ArrayBuffer to support non-text request bodies.\n            // NOTE: we can't use Blobs becuse Safari doesn't support storing\n            // Blobs in IndexedDB in some cases:\n            // https://github.com/dfahlander/Dexie.js/issues/618#issuecomment-398348457\n            requestData.body = await request.clone().arrayBuffer();\n        }\n        // Convert the headers from an iterable to an object.\n        for (const [key, value] of request.headers.entries()) {\n            requestData.headers[key] = value;\n        }\n        // Add all other serializable request properties\n        for (const prop of serializableProperties) {\n            if (request[prop] !== undefined) {\n                requestData[prop] = request[prop];\n            }\n        }\n        return new StorableRequest(requestData);\n    }\n    /**\n     * Accepts an object of request data that can be used to construct a\n     * `Request` but can also be stored in IndexedDB.\n     *\n     * @param {Object} requestData An object of request data that includes the\n     *     `url` plus any relevant properties of\n     *     [requestInit]{@link https://fetch.spec.whatwg.org/#requestinit}.\n     */\n    constructor(requestData) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(requestData, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'StorableRequest',\n                funcName: 'constructor',\n                paramName: 'requestData',\n            });\n            assert.isType(requestData.url, 'string', {\n                moduleName: 'workbox-background-sync',\n                className: 'StorableRequest',\n                funcName: 'constructor',\n                paramName: 'requestData.url',\n            });\n        }\n        // If the request's mode is `navigate`, convert it to `same-origin` since\n        // navigation requests can't be constructed via script.\n        if (requestData['mode'] === 'navigate') {\n            requestData['mode'] = 'same-origin';\n        }\n        this._requestData = requestData;\n    }\n    /**\n     * Returns a deep clone of the instances `_requestData` object.\n     *\n     * @return {Object}\n     */\n    toObject() {\n        const requestData = Object.assign({}, this._requestData);\n        requestData.headers = Object.assign({}, this._requestData.headers);\n        if (requestData.body) {\n            requestData.body = requestData.body.slice(0);\n        }\n        return requestData;\n    }\n    /**\n     * Converts this instance to a Request.\n     *\n     * @return {Request}\n     */\n    toRequest() {\n        return new Request(this._requestData.url, this._requestData);\n    }\n    /**\n     * Creates and returns a deep clone of the instance.\n     *\n     * @return {StorableRequest}\n     */\n    clone() {\n        return new StorableRequest(this.toObject());\n    }\n}\nexport { StorableRequest };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { QueueStore } from './lib/QueueStore.js';\nimport { StorableRequest } from './lib/StorableRequest.js';\nimport './_version.js';\nconst TAG_PREFIX = 'workbox-background-sync';\nconst MAX_RETENTION_TIME = 60 * 24 * 7; // 7 days in minutes\nconst queueNames = new Set();\n/**\n * Converts a QueueStore entry into the format exposed by Queue. This entails\n * converting the request data into a real request and omitting the `id` and\n * `queueName` properties.\n *\n * @param {UnidentifiedQueueStoreEntry} queueStoreEntry\n * @return {Queue}\n * @private\n */\nconst convertEntry = (queueStoreEntry) => {\n    const queueEntry = {\n        request: new StorableRequest(queueStoreEntry.requestData).toRequest(),\n        timestamp: queueStoreEntry.timestamp,\n    };\n    if (queueStoreEntry.metadata) {\n        queueEntry.metadata = queueStoreEntry.metadata;\n    }\n    return queueEntry;\n};\n/**\n * A class to manage storing failed requests in IndexedDB and retrying them\n * later. All parts of the storing and replaying process are observable via\n * callbacks.\n *\n * @memberof workbox-background-sync\n */\nclass Queue {\n    /**\n     * Creates an instance of Queue with the given options\n     *\n     * @param {string} name The unique name for this queue. This name must be\n     *     unique as it's used to register sync events and store requests\n     *     in IndexedDB specific to this instance. An error will be thrown if\n     *     a duplicate name is detected.\n     * @param {Object} [options]\n     * @param {Function} [options.onSync] A function that gets invoked whenever\n     *     the 'sync' event fires. The function is invoked with an object\n     *     containing the `queue` property (referencing this instance), and you\n     *     can use the callback to customize the replay behavior of the queue.\n     *     When not set the `replayRequests()` method is called.\n     *     Note: if the replay fails after a sync event, make sure you throw an\n     *     error, so the browser knows to retry the sync event later.\n     * @param {number} [options.maxRetentionTime=7 days] The amount of time (in\n     *     minutes) a request may be retried. After this amount of time has\n     *     passed, the request will be deleted from the queue.\n     * @param {boolean} [options.forceSyncFallback=false] If `true`, instead\n     *     of attempting to use background sync events, always attempt to replay\n     *     queued request at service worker startup. Most folks will not need\n     *     this, unless you explicitly target a runtime like Electron that\n     *     exposes the interfaces for background sync, but does not have a working\n     *     implementation.\n     */\n    constructor(name, { forceSyncFallback, onSync, maxRetentionTime } = {}) {\n        this._syncInProgress = false;\n        this._requestsAddedDuringSync = false;\n        // Ensure the store name is not already being used\n        if (queueNames.has(name)) {\n            throw new WorkboxError('duplicate-queue-name', { name });\n        }\n        else {\n            queueNames.add(name);\n        }\n        this._name = name;\n        this._onSync = onSync || this.replayRequests;\n        this._maxRetentionTime = maxRetentionTime || MAX_RETENTION_TIME;\n        this._forceSyncFallback = Boolean(forceSyncFallback);\n        this._queueStore = new QueueStore(this._name);\n        this._addSyncListener();\n    }\n    /**\n     * @return {string}\n     */\n    get name() {\n        return this._name;\n    }\n    /**\n     * Stores the passed request in IndexedDB (with its timestamp and any\n     * metadata) at the end of the queue.\n     *\n     * @param {QueueEntry} entry\n     * @param {Request} entry.request The request to store in the queue.\n     * @param {Object} [entry.metadata] Any metadata you want associated with the\n     *     stored request. When requests are replayed you'll have access to this\n     *     metadata object in case you need to modify the request beforehand.\n     * @param {number} [entry.timestamp] The timestamp (Epoch time in\n     *     milliseconds) when the request was first added to the queue. This is\n     *     used along with `maxRetentionTime` to remove outdated requests. In\n     *     general you don't need to set this value, as it's automatically set\n     *     for you (defaulting to `Date.now()`), but you can update it if you\n     *     don't want particular requests to expire.\n     */\n    async pushRequest(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'pushRequest',\n                paramName: 'entry',\n            });\n            assert.isInstance(entry.request, Request, {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'pushRequest',\n                paramName: 'entry.request',\n            });\n        }\n        await this._addRequest(entry, 'push');\n    }\n    /**\n     * Stores the passed request in IndexedDB (with its timestamp and any\n     * metadata) at the beginning of the queue.\n     *\n     * @param {QueueEntry} entry\n     * @param {Request} entry.request The request to store in the queue.\n     * @param {Object} [entry.metadata] Any metadata you want associated with the\n     *     stored request. When requests are replayed you'll have access to this\n     *     metadata object in case you need to modify the request beforehand.\n     * @param {number} [entry.timestamp] The timestamp (Epoch time in\n     *     milliseconds) when the request was first added to the queue. This is\n     *     used along with `maxRetentionTime` to remove outdated requests. In\n     *     general you don't need to set this value, as it's automatically set\n     *     for you (defaulting to `Date.now()`), but you can update it if you\n     *     don't want particular requests to expire.\n     */\n    async unshiftRequest(entry) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(entry, 'object', {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'unshiftRequest',\n                paramName: 'entry',\n            });\n            assert.isInstance(entry.request, Request, {\n                moduleName: 'workbox-background-sync',\n                className: 'Queue',\n                funcName: 'unshiftRequest',\n                paramName: 'entry.request',\n            });\n        }\n        await this._addRequest(entry, 'unshift');\n    }\n    /**\n     * Removes and returns the last request in the queue (along with its\n     * timestamp and any metadata). The returned object takes the form:\n     * `{request, timestamp, metadata}`.\n     *\n     * @return {Promise<QueueEntry | undefined>}\n     */\n    async popRequest() {\n        return this._removeRequest('pop');\n    }\n    /**\n     * Removes and returns the first request in the queue (along with its\n     * timestamp and any metadata). The returned object takes the form:\n     * `{request, timestamp, metadata}`.\n     *\n     * @return {Promise<QueueEntry | undefined>}\n     */\n    async shiftRequest() {\n        return this._removeRequest('shift');\n    }\n    /**\n     * Returns all the entries that have not expired (per `maxRetentionTime`).\n     * Any expired entries are removed from the queue.\n     *\n     * @return {Promise<Array<QueueEntry>>}\n     */\n    async getAll() {\n        const allEntries = await this._queueStore.getAll();\n        const now = Date.now();\n        const unexpiredEntries = [];\n        for (const entry of allEntries) {\n            // Ignore requests older than maxRetentionTime. Call this function\n            // recursively until an unexpired request is found.\n            const maxRetentionTimeInMs = this._maxRetentionTime * 60 * 1000;\n            if (now - entry.timestamp > maxRetentionTimeInMs) {\n                await this._queueStore.deleteEntry(entry.id);\n            }\n            else {\n                unexpiredEntries.push(convertEntry(entry));\n            }\n        }\n        return unexpiredEntries;\n    }\n    /**\n     * Returns the number of entries present in the queue.\n     * Note that expired entries (per `maxRetentionTime`) are also included in this count.\n     *\n     * @return {Promise<number>}\n     */\n    async size() {\n        return await this._queueStore.size();\n    }\n    /**\n     * Adds the entry to the QueueStore and registers for a sync event.\n     *\n     * @param {Object} entry\n     * @param {Request} entry.request\n     * @param {Object} [entry.metadata]\n     * @param {number} [entry.timestamp=Date.now()]\n     * @param {string} operation ('push' or 'unshift')\n     * @private\n     */\n    async _addRequest({ request, metadata, timestamp = Date.now() }, operation) {\n        const storableRequest = await StorableRequest.fromRequest(request.clone());\n        const entry = {\n            requestData: storableRequest.toObject(),\n            timestamp,\n        };\n        // Only include metadata if it's present.\n        if (metadata) {\n            entry.metadata = metadata;\n        }\n        switch (operation) {\n            case 'push':\n                await this._queueStore.pushEntry(entry);\n                break;\n            case 'unshift':\n                await this._queueStore.unshiftEntry(entry);\n                break;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`Request for '${getFriendlyURL(request.url)}' has ` +\n                `been added to background sync queue '${this._name}'.`);\n        }\n        // Don't register for a sync if we're in the middle of a sync. Instead,\n        // we wait until the sync is complete and call register if\n        // `this._requestsAddedDuringSync` is true.\n        if (this._syncInProgress) {\n            this._requestsAddedDuringSync = true;\n        }\n        else {\n            await this.registerSync();\n        }\n    }\n    /**\n     * Removes and returns the first or last (depending on `operation`) entry\n     * from the QueueStore that's not older than the `maxRetentionTime`.\n     *\n     * @param {string} operation ('pop' or 'shift')\n     * @return {Object|undefined}\n     * @private\n     */\n    async _removeRequest(operation) {\n        const now = Date.now();\n        let entry;\n        switch (operation) {\n            case 'pop':\n                entry = await this._queueStore.popEntry();\n                break;\n            case 'shift':\n                entry = await this._queueStore.shiftEntry();\n                break;\n        }\n        if (entry) {\n            // Ignore requests older than maxRetentionTime. Call this function\n            // recursively until an unexpired request is found.\n            const maxRetentionTimeInMs = this._maxRetentionTime * 60 * 1000;\n            if (now - entry.timestamp > maxRetentionTimeInMs) {\n                return this._removeRequest(operation);\n            }\n            return convertEntry(entry);\n        }\n        else {\n            return undefined;\n        }\n    }\n    /**\n     * Loops through each request in the queue and attempts to re-fetch it.\n     * If any request fails to re-fetch, it's put back in the same position in\n     * the queue (which registers a retry for the next sync event).\n     */\n    async replayRequests() {\n        let entry;\n        while ((entry = await this.shiftRequest())) {\n            try {\n                await fetch(entry.request.clone());\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Request for '${getFriendlyURL(entry.request.url)}' ` +\n                        `has been replayed in queue '${this._name}'`);\n                }\n            }\n            catch (error) {\n                await this.unshiftRequest(entry);\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Request for '${getFriendlyURL(entry.request.url)}' ` +\n                        `failed to replay, putting it back in queue '${this._name}'`);\n                }\n                throw new WorkboxError('queue-replay-failed', { name: this._name });\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`All requests in queue '${this.name}' have successfully ` +\n                `replayed; the queue is now empty!`);\n        }\n    }\n    /**\n     * Registers a sync event with a tag unique to this instance.\n     */\n    async registerSync() {\n        // See https://github.com/GoogleChrome/workbox/issues/2393\n        if ('sync' in self.registration && !this._forceSyncFallback) {\n            try {\n                await self.registration.sync.register(`${TAG_PREFIX}:${this._name}`);\n            }\n            catch (err) {\n                // This means the registration failed for some reason, possibly due to\n                // the user disabling it.\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.warn(`Unable to register sync event for '${this._name}'.`, err);\n                }\n            }\n        }\n    }\n    /**\n     * In sync-supporting browsers, this adds a listener for the sync event.\n     * In non-sync-supporting browsers, or if _forceSyncFallback is true, this\n     * will retry the queue on service worker startup.\n     *\n     * @private\n     */\n    _addSyncListener() {\n        // See https://github.com/GoogleChrome/workbox/issues/2393\n        if ('sync' in self.registration && !this._forceSyncFallback) {\n            self.addEventListener('sync', (event) => {\n                if (event.tag === `${TAG_PREFIX}:${this._name}`) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        logger.log(`Background sync for tag '${event.tag}' ` + `has been received`);\n                    }\n                    const syncComplete = async () => {\n                        this._syncInProgress = true;\n                        let syncError;\n                        try {\n                            await this._onSync({ queue: this });\n                        }\n                        catch (error) {\n                            if (error instanceof Error) {\n                                syncError = error;\n                                // Rethrow the error. Note: the logic in the finally clause\n                                // will run before this gets rethrown.\n                                throw syncError;\n                            }\n                        }\n                        finally {\n                            // New items may have been added to the queue during the sync,\n                            // so we need to register for a new sync if that's happened...\n                            // Unless there was an error during the sync, in which\n                            // case the browser will automatically retry later, as long\n                            // as `event.lastChance` is not true.\n                            if (this._requestsAddedDuringSync &&\n                                !(syncError && !event.lastChance)) {\n                                await this.registerSync();\n                            }\n                            this._syncInProgress = false;\n                            this._requestsAddedDuringSync = false;\n                        }\n                    };\n                    event.waitUntil(syncComplete());\n                }\n            });\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log(`Background sync replaying without background sync event`);\n            }\n            // If the browser doesn't support background sync, or the developer has\n            // opted-in to not using it, retry every time the service worker starts up\n            // as a fallback.\n            void this._onSync({ queue: this });\n        }\n    }\n    /**\n     * Returns the set of queue names. This is primarily used to reset the list\n     * of queue names in tests.\n     *\n     * @return {Set<string>}\n     *\n     * @private\n     */\n    static get _queueNames() {\n        return queueNames;\n    }\n}\nexport { Queue };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Queue } from './Queue.js';\nimport './_version.js';\n/**\n * A class implementing the `fetchDidFail` lifecycle callback. This makes it\n * easier to add failed requests to a background sync Queue.\n *\n * @memberof workbox-background-sync\n */\nclass BackgroundSyncPlugin {\n    /**\n     * @param {string} name See the {@link workbox-background-sync.Queue}\n     *     documentation for parameter details.\n     * @param {Object} [options] See the\n     *     {@link workbox-background-sync.Queue} documentation for\n     *     parameter details.\n     */\n    constructor(name, options) {\n        /**\n         * @param {Object} options\n         * @param {Request} options.request\n         * @private\n         */\n        this.fetchDidFail = async ({ request }) => {\n            await this._queue.pushRequest({ request });\n        };\n        this._queue = new Queue(name, options);\n    }\n}\nexport { BackgroundSyncPlugin };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * Claim any currently available clients once the service worker\n * becomes active. This is normally used in conjunction with `skipWaiting()`.\n *\n * @memberof workbox-core\n */\nfunction clientsClaim() {\n    self.addEventListener('activate', () => self.clients.claim());\n}\nexport { clientsClaim };\n", "/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A utility method that makes it easier to use `event.waitUntil` with\n * async functions and return the result.\n *\n * @param {ExtendableEvent} event\n * @param {Function} asyncFn\n * @return {Function}\n * @private\n */\nfunction waitUntil(event, asyncFn) {\n    const returnPromise = asyncFn();\n    event.waitUntil(returnPromise);\n    return returnPromise;\n}\nexport { waitUntil };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:precaching:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport '../_version.js';\n// Name of the search parameter used to store revision info.\nconst REVISION_SEARCH_PARAM = '__WB_REVISION__';\n/**\n * Converts a manifest entry into a versioned URL suitable for precaching.\n *\n * @param {Object|string} entry\n * @return {string} A URL with versioning info.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function createCacheKey(entry) {\n    if (!entry) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If a precache manifest entry is a string, it's assumed to be a versioned\n    // URL, like '/app.abcd1234.js'. Return as-is.\n    if (typeof entry === 'string') {\n        const urlObject = new URL(entry, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    const { revision, url } = entry;\n    if (!url) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If there's just a URL and no revision, then it's also assumed to be a\n    // versioned URL.\n    if (!revision) {\n        const urlObject = new URL(url, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    // Otherwise, construct a properly versioned URL using the custom Workbox\n    // search parameter along with the revision info.\n    const cacheKeyURL = new URL(url, location.href);\n    const originalURL = new URL(url, location.href);\n    cacheKeyURL.searchParams.set(REVISION_SEARCH_PARAM, revision);\n    return {\n        cacheKey: cacheKeyURL.href,\n        url: originalURL.href,\n    };\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to determine the\n * of assets that were updated (or not updated) during the install event.\n *\n * @private\n */\nclass PrecacheInstallReportPlugin {\n    constructor() {\n        this.updatedURLs = [];\n        this.notUpdatedURLs = [];\n        this.handlerWillStart = async ({ request, state, }) => {\n            // TODO: `state` should never be undefined...\n            if (state) {\n                state.originalRequest = request;\n            }\n        };\n        this.cachedResponseWillBeUsed = async ({ event, state, cachedResponse, }) => {\n            if (event.type === 'install') {\n                if (state &&\n                    state.originalRequest &&\n                    state.originalRequest instanceof Request) {\n                    // TODO: `state` should never be undefined...\n                    const url = state.originalRequest.url;\n                    if (cachedResponse) {\n                        this.notUpdatedURLs.push(url);\n                    }\n                    else {\n                        this.updatedURLs.push(url);\n                    }\n                }\n            }\n            return cachedResponse;\n        };\n    }\n}\nexport { PrecacheInstallReportPlugin };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to translate URLs into\n * the corresponding cache key, based on the current revision info.\n *\n * @private\n */\nclass PrecacheCacheKeyPlugin {\n    constructor({ precacheController }) {\n        this.cacheKeyWillBeUsed = async ({ request, params, }) => {\n            // Params is type any, can't change right now.\n            /* eslint-disable */\n            const cacheKey = (params === null || params === void 0 ? void 0 : params.cacheKey) ||\n                this._precacheController.getCacheKeyForURL(request.url);\n            /* eslint-enable */\n            return cacheKey\n                ? new Request(cacheKey, { headers: request.headers })\n                : request;\n        };\n        this._precacheController = precacheController;\n    }\n}\nexport { PrecacheCacheKeyPlugin };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport '../_version.js';\n/**\n * @param {string} groupTitle\n * @param {Array<string>} deletedURLs\n *\n * @private\n */\nconst logGroup = (groupTitle, deletedURLs) => {\n    logger.groupCollapsed(groupTitle);\n    for (const url of deletedURLs) {\n        logger.log(url);\n    }\n    logger.groupEnd();\n};\n/**\n * @param {Array<string>} deletedURLs\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function printCleanupDetails(deletedURLs) {\n    const deletionCount = deletedURLs.length;\n    if (deletionCount > 0) {\n        logger.groupCollapsed(`During precaching cleanup, ` +\n            `${deletionCount} cached ` +\n            `request${deletionCount === 1 ? ' was' : 's were'} deleted.`);\n        logGroup('Deleted Cache Requests', deletedURLs);\n        logger.groupEnd();\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport '../_version.js';\n/**\n * @param {string} groupTitle\n * @param {Array<string>} urls\n *\n * @private\n */\nfunction _nestedGroup(groupTitle, urls) {\n    if (urls.length === 0) {\n        return;\n    }\n    logger.groupCollapsed(groupTitle);\n    for (const url of urls) {\n        logger.log(url);\n    }\n    logger.groupEnd();\n}\n/**\n * @param {Array<string>} urlsToPrecache\n * @param {Array<string>} urlsAlreadyPrecached\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function printInstallDetails(urlsToPrecache, urlsAlreadyPrecached) {\n    const precachedCount = urlsToPrecache.length;\n    const alreadyPrecachedCount = urlsAlreadyPrecached.length;\n    if (precachedCount || alreadyPrecachedCount) {\n        let message = `Precaching ${precachedCount} file${precachedCount === 1 ? '' : 's'}.`;\n        if (alreadyPrecachedCount > 0) {\n            message +=\n                ` ${alreadyPrecachedCount} ` +\n                    `file${alreadyPrecachedCount === 1 ? ' is' : 's are'} already cached.`;\n        }\n        logger.groupCollapsed(message);\n        _nestedGroup(`View newly precached URLs.`, urlsToPrecache);\n        _nestedGroup(`View previously precached URLs.`, urlsAlreadyPrecached);\n        logger.groupEnd();\n    }\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nlet supportStatus;\n/**\n * A utility function that determines whether the current browser supports\n * constructing a new `Response` from a `response.body` stream.\n *\n * @return {boolean} `true`, if the current browser can successfully\n *     construct a `Response` from a `response.body` stream, `false` otherwise.\n *\n * @private\n */\nfunction canConstructResponseFromBodyStream() {\n    if (supportStatus === undefined) {\n        const testResponse = new Response('');\n        if ('body' in testResponse) {\n            try {\n                new Response(testResponse.body);\n                supportStatus = true;\n            }\n            catch (error) {\n                supportStatus = false;\n            }\n        }\n        supportStatus = false;\n    }\n    return supportStatus;\n}\nexport { canConstructResponseFromBodyStream };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { canConstructResponseFromBodyStream } from './_private/canConstructResponseFromBodyStream.js';\nimport { WorkboxError } from './_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Allows developers to copy a response and modify its `headers`, `status`,\n * or `statusText` values (the values settable via a\n * [`ResponseInit`]{@link https://developer.mozilla.org/en-US/docs/Web/API/Response/Response#Syntax}\n * object in the constructor).\n * To modify these values, pass a function as the second argument. That\n * function will be invoked with a single object with the response properties\n * `{headers, status, statusText}`. The return value of this function will\n * be used as the `ResponseInit` for the new `Response`. To change the values\n * either modify the passed parameter(s) and return it, or return a totally\n * new object.\n *\n * This method is intentionally limited to same-origin responses, regardless of\n * whether CORS was used or not.\n *\n * @param {Response} response\n * @param {Function} modifier\n * @memberof workbox-core\n */\nasync function copyResponse(response, modifier) {\n    let origin = null;\n    // If response.url isn't set, assume it's cross-origin and keep origin null.\n    if (response.url) {\n        const responseURL = new URL(response.url);\n        origin = responseURL.origin;\n    }\n    if (origin !== self.location.origin) {\n        throw new WorkboxError('cross-origin-copy-response', { origin });\n    }\n    const clonedResponse = response.clone();\n    // Create a fresh `ResponseInit` object by cloning the headers.\n    const responseInit = {\n        headers: new Headers(clonedResponse.headers),\n        status: clonedResponse.status,\n        statusText: clonedResponse.statusText,\n    };\n    // Apply any user modifications.\n    const modifiedResponseInit = modifier ? modifier(responseInit) : responseInit;\n    // Create the new response from the body stream and `ResponseInit`\n    // modifications. Note: not all browsers support the Response.body stream,\n    // so fall back to reading the entire body into memory as a blob.\n    const body = canConstructResponseFromBodyStream()\n        ? clonedResponse.body\n        : await clonedResponse.blob();\n    return new Response(body, modifiedResponseInit);\n}\nexport { copyResponse };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { copyResponse } from 'workbox-core/copyResponse.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from 'workbox-strategies/Strategy.js';\nimport './_version.js';\n/**\n * A {@link workbox-strategies.Strategy} implementation\n * specifically designed to work with\n * {@link workbox-precaching.PrecacheController}\n * to both cache and fetch precached assets.\n *\n * Note: an instance of this class is created automatically when creating a\n * `PrecacheController`; it's generally not necessary to create this yourself.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-precaching\n */\nclass PrecacheStrategy extends Strategy {\n    /**\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] {@link https://developers.google.com/web/tools/workbox/guides/using-plugins|Plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters|init}\n     * of all fetch() requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * {@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions|CacheQueryOptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor(options = {}) {\n        options.cacheName = cacheNames.getPrecacheName(options.cacheName);\n        super(options);\n        this._fallbackToNetwork =\n            options.fallbackToNetwork === false ? false : true;\n        // Redirected responses cannot be used to satisfy a navigation request, so\n        // any redirected response must be \"copied\" rather than cloned, so the new\n        // response doesn't contain the `redirected` flag. See:\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=669363&desc=2#c1\n        this.plugins.push(PrecacheStrategy.copyRedirectedCacheableResponsesPlugin);\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const response = await handler.cacheMatch(request);\n        if (response) {\n            return response;\n        }\n        // If this is an `install` event for an entry that isn't already cached,\n        // then populate the cache.\n        if (handler.event && handler.event.type === 'install') {\n            return await this._handleInstall(request, handler);\n        }\n        // Getting here means something went wrong. An entry that should have been\n        // precached wasn't found in the cache.\n        return await this._handleFetch(request, handler);\n    }\n    async _handleFetch(request, handler) {\n        let response;\n        const params = (handler.params || {});\n        // Fall back to the network if we're configured to do so.\n        if (this._fallbackToNetwork) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.warn(`The precached response for ` +\n                    `${getFriendlyURL(request.url)} in ${this.cacheName} was not ` +\n                    `found. Falling back to the network.`);\n            }\n            const integrityInManifest = params.integrity;\n            const integrityInRequest = request.integrity;\n            const noIntegrityConflict = !integrityInRequest || integrityInRequest === integrityInManifest;\n            // Do not add integrity if the original request is no-cors\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            response = await handler.fetch(new Request(request, {\n                integrity: request.mode !== 'no-cors'\n                    ? integrityInRequest || integrityInManifest\n                    : undefined,\n            }));\n            // It's only \"safe\" to repair the cache if we're using SRI to guarantee\n            // that the response matches the precache manifest's expectations,\n            // and there's either a) no integrity property in the incoming request\n            // or b) there is an integrity, and it matches the precache manifest.\n            // See https://github.com/GoogleChrome/workbox/issues/2858\n            // Also if the original request users no-cors we don't use integrity.\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            if (integrityInManifest &&\n                noIntegrityConflict &&\n                request.mode !== 'no-cors') {\n                this._useDefaultCacheabilityPluginIfNeeded();\n                const wasCached = await handler.cachePut(request, response.clone());\n                if (process.env.NODE_ENV !== 'production') {\n                    if (wasCached) {\n                        logger.log(`A response for ${getFriendlyURL(request.url)} ` +\n                            `was used to \"repair\" the precache.`);\n                    }\n                }\n            }\n        }\n        else {\n            // This shouldn't normally happen, but there are edge cases:\n            // https://github.com/GoogleChrome/workbox/issues/1441\n            throw new WorkboxError('missing-precache-entry', {\n                cacheName: this.cacheName,\n                url: request.url,\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            const cacheKey = params.cacheKey || (await handler.getCacheKey(request, 'read'));\n            // Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Precaching is responding to: ` + getFriendlyURL(request.url));\n            logger.log(`Serving the precached url: ${getFriendlyURL(cacheKey instanceof Request ? cacheKey.url : cacheKey)}`);\n            logger.groupCollapsed(`View request details here.`);\n            logger.log(request);\n            logger.groupEnd();\n            logger.groupCollapsed(`View response details here.`);\n            logger.log(response);\n            logger.groupEnd();\n            logger.groupEnd();\n        }\n        return response;\n    }\n    async _handleInstall(request, handler) {\n        this._useDefaultCacheabilityPluginIfNeeded();\n        const response = await handler.fetch(request);\n        // Make sure we defer cachePut() until after we know the response\n        // should be cached; see https://github.com/GoogleChrome/workbox/issues/2737\n        const wasCached = await handler.cachePut(request, response.clone());\n        if (!wasCached) {\n            // Throwing here will lead to the `install` handler failing, which\n            // we want to do if *any* of the responses aren't safe to cache.\n            throw new WorkboxError('bad-precaching-response', {\n                url: request.url,\n                status: response.status,\n            });\n        }\n        return response;\n    }\n    /**\n     * This method is complex, as there a number of things to account for:\n     *\n     * The `plugins` array can be set at construction, and/or it might be added to\n     * to at any time before the strategy is used.\n     *\n     * At the time the strategy is used (i.e. during an `install` event), there\n     * needs to be at least one plugin that implements `cacheWillUpdate` in the\n     * array, other than `copyRedirectedCacheableResponsesPlugin`.\n     *\n     * - If this method is called and there are no suitable `cacheWillUpdate`\n     * plugins, we need to add `defaultPrecacheCacheabilityPlugin`.\n     *\n     * - If this method is called and there is exactly one `cacheWillUpdate`, then\n     * we don't have to do anything (this might be a previously added\n     * `defaultPrecacheCacheabilityPlugin`, or it might be a custom plugin).\n     *\n     * - If this method is called and there is more than one `cacheWillUpdate`,\n     * then we need to check if one is `defaultPrecacheCacheabilityPlugin`. If so,\n     * we need to remove it. (This situation is unlikely, but it could happen if\n     * the strategy is used multiple times, the first without a `cacheWillUpdate`,\n     * and then later on after manually adding a custom `cacheWillUpdate`.)\n     *\n     * See https://github.com/GoogleChrome/workbox/issues/2737 for more context.\n     *\n     * @private\n     */\n    _useDefaultCacheabilityPluginIfNeeded() {\n        let defaultPluginIndex = null;\n        let cacheWillUpdatePluginCount = 0;\n        for (const [index, plugin] of this.plugins.entries()) {\n            // Ignore the copy redirected plugin when determining what to do.\n            if (plugin === PrecacheStrategy.copyRedirectedCacheableResponsesPlugin) {\n                continue;\n            }\n            // Save the default plugin's index, in case it needs to be removed.\n            if (plugin === PrecacheStrategy.defaultPrecacheCacheabilityPlugin) {\n                defaultPluginIndex = index;\n            }\n            if (plugin.cacheWillUpdate) {\n                cacheWillUpdatePluginCount++;\n            }\n        }\n        if (cacheWillUpdatePluginCount === 0) {\n            this.plugins.push(PrecacheStrategy.defaultPrecacheCacheabilityPlugin);\n        }\n        else if (cacheWillUpdatePluginCount > 1 && defaultPluginIndex !== null) {\n            // Only remove the default plugin; multiple custom plugins are allowed.\n            this.plugins.splice(defaultPluginIndex, 1);\n        }\n        // Nothing needs to be done if cacheWillUpdatePluginCount is 1\n    }\n}\nPrecacheStrategy.defaultPrecacheCacheabilityPlugin = {\n    async cacheWillUpdate({ response }) {\n        if (!response || response.status >= 400) {\n            return null;\n        }\n        return response;\n    },\n};\nPrecacheStrategy.copyRedirectedCacheableResponsesPlugin = {\n    async cacheWillUpdate({ response }) {\n        return response.redirected ? await copyResponse(response) : response;\n    },\n};\nexport { PrecacheStrategy };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { waitUntil } from 'workbox-core/_private/waitUntil.js';\nimport { createCacheKey } from './utils/createCacheKey.js';\nimport { PrecacheInstallReportPlugin } from './utils/PrecacheInstallReportPlugin.js';\nimport { PrecacheCacheKeyPlugin } from './utils/PrecacheCacheKeyPlugin.js';\nimport { printCleanupDetails } from './utils/printCleanupDetails.js';\nimport { printInstallDetails } from './utils/printInstallDetails.js';\nimport { PrecacheStrategy } from './PrecacheStrategy.js';\nimport './_version.js';\n/**\n * Performs efficient precaching of assets.\n *\n * @memberof workbox-precaching\n */\nclass PrecacheController {\n    /**\n     * Create a new PrecacheController.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] The cache to use for precaching.\n     * @param {string} [options.plugins] Plugins to use when precaching as well\n     * as responding to fetch events for precached assets.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor({ cacheName, plugins = [], fallbackToNetwork = true, } = {}) {\n        this._urlsToCacheKeys = new Map();\n        this._urlsToCacheModes = new Map();\n        this._cacheKeysToIntegrities = new Map();\n        this._strategy = new PrecacheStrategy({\n            cacheName: cacheNames.getPrecacheName(cacheName),\n            plugins: [\n                ...plugins,\n                new PrecacheCacheKeyPlugin({ precacheController: this }),\n            ],\n            fallbackToNetwork,\n        });\n        // Bind the install and activate methods to the instance.\n        this.install = this.install.bind(this);\n        this.activate = this.activate.bind(this);\n    }\n    /**\n     * @type {workbox-precaching.PrecacheStrategy} The strategy created by this controller and\n     * used to cache assets and respond to fetch events.\n     */\n    get strategy() {\n        return this._strategy;\n    }\n    /**\n     * Adds items to the precache list, removing any duplicates and\n     * stores the files in the\n     * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n     * worker installs.\n     *\n     * This method can be called multiple times.\n     *\n     * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n     */\n    precache(entries) {\n        this.addToCacheList(entries);\n        if (!this._installAndActiveListenersAdded) {\n            self.addEventListener('install', this.install);\n            self.addEventListener('activate', this.activate);\n            this._installAndActiveListenersAdded = true;\n        }\n    }\n    /**\n     * This method will add items to the precache list, removing duplicates\n     * and ensuring the information is valid.\n     *\n     * @param {Array<workbox-precaching.PrecacheController.PrecacheEntry|string>} entries\n     *     Array of entries to precache.\n     */\n    addToCacheList(entries) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArray(entries, {\n                moduleName: 'workbox-precaching',\n                className: 'PrecacheController',\n                funcName: 'addToCacheList',\n                paramName: 'entries',\n            });\n        }\n        const urlsToWarnAbout = [];\n        for (const entry of entries) {\n            // See https://github.com/GoogleChrome/workbox/issues/2259\n            if (typeof entry === 'string') {\n                urlsToWarnAbout.push(entry);\n            }\n            else if (entry && entry.revision === undefined) {\n                urlsToWarnAbout.push(entry.url);\n            }\n            const { cacheKey, url } = createCacheKey(entry);\n            const cacheMode = typeof entry !== 'string' && entry.revision ? 'reload' : 'default';\n            if (this._urlsToCacheKeys.has(url) &&\n                this._urlsToCacheKeys.get(url) !== cacheKey) {\n                throw new WorkboxError('add-to-cache-list-conflicting-entries', {\n                    firstEntry: this._urlsToCacheKeys.get(url),\n                    secondEntry: cacheKey,\n                });\n            }\n            if (typeof entry !== 'string' && entry.integrity) {\n                if (this._cacheKeysToIntegrities.has(cacheKey) &&\n                    this._cacheKeysToIntegrities.get(cacheKey) !== entry.integrity) {\n                    throw new WorkboxError('add-to-cache-list-conflicting-integrities', {\n                        url,\n                    });\n                }\n                this._cacheKeysToIntegrities.set(cacheKey, entry.integrity);\n            }\n            this._urlsToCacheKeys.set(url, cacheKey);\n            this._urlsToCacheModes.set(url, cacheMode);\n            if (urlsToWarnAbout.length > 0) {\n                const warningMessage = `Workbox is precaching URLs without revision ` +\n                    `info: ${urlsToWarnAbout.join(', ')}\\nThis is generally NOT safe. ` +\n                    `Learn more at https://bit.ly/wb-precache`;\n                if (process.env.NODE_ENV === 'production') {\n                    // Use console directly to display this warning without bloating\n                    // bundle sizes by pulling in all of the logger codebase in prod.\n                    console.warn(warningMessage);\n                }\n                else {\n                    logger.warn(warningMessage);\n                }\n            }\n        }\n    }\n    /**\n     * Precaches new and updated assets. Call this method from the service worker\n     * install event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.InstallResult>}\n     */\n    install(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const installReportPlugin = new PrecacheInstallReportPlugin();\n            this.strategy.plugins.push(installReportPlugin);\n            // Cache entries one at a time.\n            // See https://github.com/GoogleChrome/workbox/issues/2528\n            for (const [url, cacheKey] of this._urlsToCacheKeys) {\n                const integrity = this._cacheKeysToIntegrities.get(cacheKey);\n                const cacheMode = this._urlsToCacheModes.get(url);\n                const request = new Request(url, {\n                    integrity,\n                    cache: cacheMode,\n                    credentials: 'same-origin',\n                });\n                await Promise.all(this.strategy.handleAll({\n                    params: { cacheKey },\n                    request,\n                    event,\n                }));\n            }\n            const { updatedURLs, notUpdatedURLs } = installReportPlugin;\n            if (process.env.NODE_ENV !== 'production') {\n                printInstallDetails(updatedURLs, notUpdatedURLs);\n            }\n            return { updatedURLs, notUpdatedURLs };\n        });\n    }\n    /**\n     * Deletes assets that are no longer present in the current precache manifest.\n     * Call this method from the service worker activate event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.CleanupResult>}\n     */\n    activate(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            const currentlyCachedRequests = await cache.keys();\n            const expectedCacheKeys = new Set(this._urlsToCacheKeys.values());\n            const deletedURLs = [];\n            for (const request of currentlyCachedRequests) {\n                if (!expectedCacheKeys.has(request.url)) {\n                    await cache.delete(request);\n                    deletedURLs.push(request.url);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                printCleanupDetails(deletedURLs);\n            }\n            return { deletedURLs };\n        });\n    }\n    /**\n     * Returns a mapping of a precached URL to the corresponding cache key, taking\n     * into account the revision information for the URL.\n     *\n     * @return {Map<string, string>} A URL to cache key mapping.\n     */\n    getURLsToCacheKeys() {\n        return this._urlsToCacheKeys;\n    }\n    /**\n     * Returns a list of all the URLs that have been precached by the current\n     * service worker.\n     *\n     * @return {Array<string>} The precached URLs.\n     */\n    getCachedURLs() {\n        return [...this._urlsToCacheKeys.keys()];\n    }\n    /**\n     * Returns the cache key used for storing a given URL. If that URL is\n     * unversioned, like `/index.html', then the cache key will be the original\n     * URL with a search parameter appended to it.\n     *\n     * @param {string} url A URL whose cache key you want to look up.\n     * @return {string} The versioned URL that corresponds to a cache key\n     * for the original URL, or undefined if that URL isn't precached.\n     */\n    getCacheKeyForURL(url) {\n        const urlObject = new URL(url, location.href);\n        return this._urlsToCacheKeys.get(urlObject.href);\n    }\n    /**\n     * @param {string} url A cache key whose SRI you want to look up.\n     * @return {string} The subresource integrity associated with the cache key,\n     * or undefined if it's not set.\n     */\n    getIntegrityForCacheKey(cacheKey) {\n        return this._cacheKeysToIntegrities.get(cacheKey);\n    }\n    /**\n     * This acts as a drop-in replacement for\n     * [`cache.match()`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/match)\n     * with the following differences:\n     *\n     * - It knows what the name of the precache is, and only checks in that cache.\n     * - It allows you to pass in an \"original\" URL without versioning parameters,\n     * and it will automatically look up the correct cache key for the currently\n     * active revision of that URL.\n     *\n     * E.g., `matchPrecache('index.html')` will find the correct precached\n     * response for the currently active service worker, even if the actual cache\n     * key is `'/index.html?__WB_REVISION__=1234abcd'`.\n     *\n     * @param {string|Request} request The key (without revisioning parameters)\n     * to look up in the precache.\n     * @return {Promise<Response|undefined>}\n     */\n    async matchPrecache(request) {\n        const url = request instanceof Request ? request.url : request;\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (cacheKey) {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            return cache.match(cacheKey);\n        }\n        return undefined;\n    }\n    /**\n     * Returns a function that looks up `url` in the precache (taking into\n     * account revision information), and returns the corresponding `Response`.\n     *\n     * @param {string} url The precached URL which will be used to lookup the\n     * `Response`.\n     * @return {workbox-routing~handlerCallback}\n     */\n    createHandlerBoundToURL(url) {\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (!cacheKey) {\n            throw new WorkboxError('non-precached-url', { url });\n        }\n        return (options) => {\n            options.request = new Request(url);\n            options.params = Object.assign({ cacheKey }, options.params);\n            return this.strategy.handle(options);\n        };\n    }\n}\nexport { PrecacheController };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { PrecacheController } from '../PrecacheController.js';\nimport '../_version.js';\nlet precacheController;\n/**\n * @return {PrecacheController}\n * @private\n */\nexport const getOrCreatePrecacheController = () => {\n    if (!precacheController) {\n        precacheController = new PrecacheController();\n    }\n    return precacheController;\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Removes any URL search parameters that should be ignored.\n *\n * @param {URL} urlObject The original URL.\n * @param {Array<RegExp>} ignoreURLParametersMatching RegExps to test against\n * each search parameter name. Matches mean that the search parameter should be\n * ignored.\n * @return {URL} The URL with any ignored search parameters removed.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching = []) {\n    // Convert the iterable into an array at the start of the loop to make sure\n    // deletion doesn't mess up iteration.\n    for (const paramName of [...urlObject.searchParams.keys()]) {\n        if (ignoreURLParametersMatching.some((regExp) => regExp.test(paramName))) {\n            urlObject.searchParams.delete(paramName);\n        }\n    }\n    return urlObject;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { removeIgnoredSearchParams } from './removeIgnoredSearchParams.js';\nimport '../_version.js';\n/**\n * Generator function that yields possible variations on the original URL to\n * check, one at a time.\n *\n * @param {string} url\n * @param {Object} options\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function* generateURLVariations(url, { ignoreURLParametersMatching = [/^utm_/, /^fbclid$/], directoryIndex = 'index.html', cleanURLs = true, urlManipulation, } = {}) {\n    const urlObject = new URL(url, location.href);\n    urlObject.hash = '';\n    yield urlObject.href;\n    const urlWithoutIgnoredParams = removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching);\n    yield urlWithoutIgnoredParams.href;\n    if (directoryIndex && urlWithoutIgnoredParams.pathname.endsWith('/')) {\n        const directoryURL = new URL(urlWithoutIgnoredParams.href);\n        directoryURL.pathname += directoryIndex;\n        yield directoryURL.href;\n    }\n    if (cleanURLs) {\n        const cleanURL = new URL(urlWithoutIgnoredParams.href);\n        cleanURL.pathname += '.html';\n        yield cleanURL.href;\n    }\n    if (urlManipulation) {\n        const additionalURLs = urlManipulation({ url: urlObject });\n        for (const urlToAttempt of additionalURLs) {\n            yield urlToAttempt.href;\n        }\n    }\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { Route } from 'workbox-routing/Route.js';\nimport { generateURLVariations } from './utils/generateURLVariations.js';\nimport './_version.js';\n/**\n * A subclass of {@link workbox-routing.Route} that takes a\n * {@link workbox-precaching.PrecacheController}\n * instance and uses it to match incoming requests and handle fetching\n * responses from the precache.\n *\n * @memberof workbox-precaching\n * @extends workbox-routing.Route\n */\nclass PrecacheRoute extends Route {\n    /**\n     * @param {PrecacheController} precacheController A `PrecacheController`\n     * instance used to both match requests and respond to fetch events.\n     * @param {Object} [options] Options to control how requests are matched\n     * against the list of precached URLs.\n     * @param {string} [options.directoryIndex=index.html] The `directoryIndex` will\n     * check cache entries for a URLs ending with '/' to see if there is a hit when\n     * appending the `directoryIndex` value.\n     * @param {Array<RegExp>} [options.ignoreURLParametersMatching=[/^utm_/, /^fbclid$/]] An\n     * array of regex's to remove search params when looking for a cache match.\n     * @param {boolean} [options.cleanURLs=true] The `cleanURLs` option will\n     * check the cache for the URL with a `.html` added to the end of the end.\n     * @param {workbox-precaching~urlManipulation} [options.urlManipulation]\n     * This is a function that should take a URL and return an array of\n     * alternative URLs that should be checked for precache matches.\n     */\n    constructor(precacheController, options) {\n        const match = ({ request, }) => {\n            const urlsToCacheKeys = precacheController.getURLsToCacheKeys();\n            for (const possibleURL of generateURLVariations(request.url, options)) {\n                const cacheKey = urlsToCacheKeys.get(possibleURL);\n                if (cacheKey) {\n                    const integrity = precacheController.getIntegrityForCacheKey(cacheKey);\n                    return { cacheKey, integrity };\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Precaching did not find a match for ` + getFriendlyURL(request.url));\n            }\n            return;\n        };\n        super(match, precacheController.strategy);\n    }\n}\nexport { PrecacheRoute };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport { PrecacheRoute } from './PrecacheRoute.js';\nimport './_version.js';\n/**\n * Add a `fetch` listener to the service worker that will\n * respond to\n * [network requests]{@link https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers#Custom_responses_to_requests}\n * with precached assets.\n *\n * Requests for assets that aren't precached, the `FetchEvent` will not be\n * responded to, allowing the event to fall through to other `fetch` event\n * listeners.\n *\n * @param {Object} [options] See the {@link workbox-precaching.PrecacheRoute}\n * options.\n *\n * @memberof workbox-precaching\n */\nfunction addRoute(options) {\n    const precacheController = getOrCreatePrecacheController();\n    const precacheRoute = new PrecacheRoute(precacheController, options);\n    registerRoute(precacheRoute);\n}\nexport { addRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Adds items to the precache list, removing any duplicates and\n * stores the files in the\n * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n * worker installs.\n *\n * This method can be called multiple times.\n *\n * Please note: This method **will not** serve any of the cached files for you.\n * It only precaches files. To respond to a network request you call\n * {@link workbox-precaching.addRoute}.\n *\n * If you have a single array of files to precache, you can just call\n * {@link workbox-precaching.precacheAndRoute}.\n *\n * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n *\n * @memberof workbox-precaching\n */\nfunction precache(entries) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.precache(entries);\n}\nexport { precache };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { addRoute } from './addRoute.js';\nimport { precache } from './precache.js';\nimport './_version.js';\n/**\n * This method will add entries to the precache list and add a route to\n * respond to fetch events.\n *\n * This is a convenience method that will call\n * {@link workbox-precaching.precache} and\n * {@link workbox-precaching.addRoute} in a single call.\n *\n * @param {Array<Object|string>} entries Array of entries to precache.\n * @param {Object} [options] See the\n * {@link workbox-precaching.PrecacheRoute} options.\n *\n * @memberof workbox-precaching\n */\nfunction precacheAndRoute(entries, options) {\n    precache(entries);\n    addRoute(options);\n}\nexport { precacheAndRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst SUBSTRING_TO_FIND = '-precache-';\n/**\n * Cleans up incompatible precaches that were created by older versions of\n * Workbox, by a service worker registered under the current scope.\n *\n * This is meant to be called as part of the `activate` event.\n *\n * This should be safe to use as long as you don't include `substringToFind`\n * (defaulting to `-precache-`) in your non-precache cache names.\n *\n * @param {string} currentPrecacheName The cache name currently in use for\n * precaching. This cache won't be deleted.\n * @param {string} [substringToFind='-precache-'] Cache names which include this\n * substring will be deleted (excluding `currentPrecacheName`).\n * @return {Array<string>} A list of all the cache names that were deleted.\n *\n * @private\n * @memberof workbox-precaching\n */\nconst deleteOutdatedCaches = async (currentPrecacheName, substringToFind = SUBSTRING_TO_FIND) => {\n    const cacheNames = await self.caches.keys();\n    const cacheNamesToDelete = cacheNames.filter((cacheName) => {\n        return (cacheName.includes(substringToFind) &&\n            cacheName.includes(self.registration.scope) &&\n            cacheName !== currentPrecacheName);\n    });\n    await Promise.all(cacheNamesToDelete.map((cacheName) => self.caches.delete(cacheName)));\n    return cacheNamesToDelete;\n};\nexport { deleteOutdatedCaches };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { deleteOutdatedCaches } from './utils/deleteOutdatedCaches.js';\nimport './_version.js';\n/**\n * Adds an `activate` event listener which will clean up incompatible\n * precaches that were created by older versions of Workbox.\n *\n * @memberof workbox-precaching\n */\nfunction cleanupOutdatedCaches() {\n    // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n    self.addEventListener('activate', ((event) => {\n        const cacheName = cacheNames.getPrecacheName();\n        event.waitUntil(deleteOutdatedCaches(cacheName).then((cachesDeleted) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (cachesDeleted.length > 0) {\n                    logger.log(`The following out-of-date precaches were cleaned up ` +\n                        `automatically:`, cachesDeleted);\n                }\n            }\n        }));\n    }));\n}\nexport { cleanupOutdatedCaches };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * NavigationRoute makes it easy to create a\n * {@link workbox-routing.Route} that matches for browser\n * [navigation requests]{@link https://developers.google.com/web/fundamentals/primers/service-workers/high-performance-loading#first_what_are_navigation_requests}.\n *\n * It will only match incoming Requests whose\n * {@link https://fetch.spec.whatwg.org/#concept-request-mode|mode}\n * is set to `navigate`.\n *\n * You can optionally only apply this route to a subset of navigation requests\n * by using one or both of the `denylist` and `allowlist` parameters.\n *\n * @memberof workbox-routing\n * @extends workbox-routing.Route\n */\nclass NavigationRoute extends Route {\n    /**\n     * If both `denylist` and `allowlist` are provided, the `denylist` will\n     * take precedence and the request will not match this route.\n     *\n     * The regular expressions in `allowlist` and `denylist`\n     * are matched against the concatenated\n     * [`pathname`]{@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLHyperlinkElementUtils/pathname}\n     * and [`search`]{@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLHyperlinkElementUtils/search}\n     * portions of the requested URL.\n     *\n     * *Note*: These RegExps may be evaluated against every destination URL during\n     * a navigation. Avoid using\n     * [complex RegExps](https://github.com/GoogleChrome/workbox/issues/3077),\n     * or else your users may see delays when navigating your site.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {Object} options\n     * @param {Array<RegExp>} [options.denylist] If any of these patterns match,\n     * the route will not handle the request (even if a allowlist RegExp matches).\n     * @param {Array<RegExp>} [options.allowlist=[/./]] If any of these patterns\n     * match the URL's pathname and search parameter, the route will handle the\n     * request (assuming the denylist doesn't match).\n     */\n    constructor(handler, { allowlist = [/./], denylist = [] } = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArrayOfClass(allowlist, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'NavigationRoute',\n                funcName: 'constructor',\n                paramName: 'options.allowlist',\n            });\n            assert.isArrayOfClass(denylist, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'NavigationRoute',\n                funcName: 'constructor',\n                paramName: 'options.denylist',\n            });\n        }\n        super((options) => this._match(options), handler);\n        this._allowlist = allowlist;\n        this._denylist = denylist;\n    }\n    /**\n     * Routes match handler.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {Request} options.request\n     * @return {boolean}\n     *\n     * @private\n     */\n    _match({ url, request }) {\n        if (request && request.mode !== 'navigate') {\n            return false;\n        }\n        const pathnameAndSearch = url.pathname + url.search;\n        for (const regExp of this._denylist) {\n            if (regExp.test(pathnameAndSearch)) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`The navigation route ${pathnameAndSearch} is not ` +\n                        `being used, since the URL matches this denylist pattern: ` +\n                        `${regExp.toString()}`);\n                }\n                return false;\n            }\n        }\n        if (this._allowlist.some((regExp) => regExp.test(pathnameAndSearch))) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`The navigation route ${pathnameAndSearch} ` + `is being used.`);\n            }\n            return true;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`The navigation route ${pathnameAndSearch} is not ` +\n                `being used, since the URL being navigated to doesn't ` +\n                `match the allowlist.`);\n        }\n        return false;\n    }\n}\nexport { NavigationRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Helper function that calls\n * {@link PrecacheController#createHandlerBoundToURL} on the default\n * {@link PrecacheController} instance.\n *\n * If you are creating your own {@link PrecacheController}, then call the\n * {@link PrecacheController#createHandlerBoundToURL} on that instance,\n * instead of using this function.\n *\n * @param {string} url The precached URL which will be used to lookup the\n * `Response`.\n * @param {boolean} [fallbackToNetwork=true] Whether to attempt to get the\n * response from the network if there's a precache miss.\n * @return {workbox-routing~handlerCallback}\n *\n * @memberof workbox-precaching\n */\nfunction createHandlerBoundToURL(url) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.createHandlerBoundToURL(url);\n}\nexport { createHandlerBoundToURL };\n", "import {registerRoute as workbox_routing_registerRoute} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-routing/registerRoute.mjs';\nimport {ExpirationPlugin as workbox_expiration_ExpirationPlugin} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-expiration/ExpirationPlugin.mjs';\nimport {CacheFirst as workbox_strategies_CacheFirst} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-strategies/CacheFirst.mjs';\nimport {CacheableResponsePlugin as workbox_cacheable_response_CacheableResponsePlugin} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-cacheable-response/CacheableResponsePlugin.mjs';\nimport {StaleWhileRevalidate as workbox_strategies_StaleWhileRevalidate} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-strategies/StaleWhileRevalidate.mjs';\nimport {NetworkFirst as workbox_strategies_NetworkFirst} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-strategies/NetworkFirst.mjs';\nimport {BackgroundSyncPlugin as workbox_background_sync_BackgroundSyncPlugin} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-background-sync/BackgroundSyncPlugin.mjs';\nimport {clientsClaim as workbox_core_clientsClaim} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-core/clientsClaim.mjs';\nimport {precacheAndRoute as workbox_precaching_precacheAndRoute} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-precaching/precacheAndRoute.mjs';\nimport {cleanupOutdatedCaches as workbox_precaching_cleanupOutdatedCaches} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-precaching/cleanupOutdatedCaches.mjs';\nimport {NavigationRoute as workbox_routing_NavigationRoute} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-routing/NavigationRoute.mjs';\nimport {createHandlerBoundToURL as workbox_precaching_createHandlerBoundToURL} from 'E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/workbox-precaching/createHandlerBoundToURL.mjs';/**\n * Welcome to your Workbox-powered service worker!\n *\n * You'll need to register this file in your web app.\n * See https://goo.gl/nhQhGp\n *\n * The rest of the code is auto-generated. Please don't update this file\n * directly; instead, make changes to your Workbox build configuration\n * and re-run your build process.\n * See https://goo.gl/2aRDsh\n */\n\n\n\n\n\n\n\n\nself.skipWaiting();\n\nworkbox_core_clientsClaim();\n\n\n/**\n * The precacheAndRoute() method efficiently caches and responds to\n * requests for URLs in the manifest.\n * See https://goo.gl/S9QRab\n */\nworkbox_precaching_precacheAndRoute([\n  {\n    \"url\": \"registerSW.js\",\n    \"revision\": \"3ca0b8505b4bec776b69afdba2768812\"\n  },\n  {\n    \"url\": \"index.html\",\n    \"revision\": \"0.edv2qp2937g\"\n  }\n], {});\nworkbox_precaching_cleanupOutdatedCaches();\nworkbox_routing_registerRoute(new workbox_routing_NavigationRoute(workbox_precaching_createHandlerBoundToURL(\"index.html\"), {\n  allowlist: [/^\\/$/],\n  \n}));\n\n\nworkbox_routing_registerRoute(/^https:\\/\\/fonts\\.googleapis\\.com/, new workbox_strategies_CacheFirst({ \"cacheName\":\"google-fonts-stylesheets\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 10, maxAgeSeconds: ******** })] }), 'GET');\nworkbox_routing_registerRoute(/^https:\\/\\/fonts\\.gstatic\\.com/, new workbox_strategies_CacheFirst({ \"cacheName\":\"google-fonts-webfonts\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 30, maxAgeSeconds: ******** }), new workbox_cacheable_response_CacheableResponsePlugin({ statuses: [ 0, 200 ] })] }), 'GET');\nworkbox_routing_registerRoute(/\\/api\\/v1\\/stocks\\/[^\\/]+$/, new workbox_strategies_StaleWhileRevalidate({ \"cacheName\":\"stock-details\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 50, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(/\\/api\\/v1\\/stocks\\/search/, new workbox_strategies_NetworkFirst({ \"cacheName\":\"stock-search\",\"networkTimeoutSeconds\":3, plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 20, maxAgeSeconds: 3600 })] }), 'GET');\nworkbox_routing_registerRoute(/\\/api\\/v1\\/stocks\\/[^\\/]+\\/history/, new workbox_strategies_CacheFirst({ \"cacheName\":\"stock-history\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 50, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(/\\/api\\/v1\\/watchlist/, new workbox_strategies_NetworkFirst({ \"cacheName\":\"watchlist-data\",\"networkTimeoutSeconds\":3, plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 10, maxAgeSeconds: 3600 }), new workbox_background_sync_BackgroundSyncPlugin(\"watchlistSync\", { maxRetentionTime: 1440 })] }), 'GET');\nworkbox_routing_registerRoute(/\\/api\\/v1\\/portfolio/, new workbox_strategies_NetworkFirst({ \"cacheName\":\"portfolio-data\",\"networkTimeoutSeconds\":3, plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 10, maxAgeSeconds: 3600 }), new workbox_background_sync_BackgroundSyncPlugin(\"portfolioSync\", { maxRetentionTime: 1440 })] }), 'GET');\n\n\n\n\n"], "names": ["self", "_", "e", "logger", "globalThis", "__WB_DISABLE_DEV_LOGS", "inGroup", "methodToColorMap", "debug", "log", "warn", "error", "groupCollapsed", "groupEnd", "print", "method", "args", "test", "navigator", "userAgent", "console", "styles", "logPrefix", "join", "api", "loggerMethods", "Object", "keys", "key", "messages", "invalid-value", "paramName", "validValueDescription", "value", "Error", "JSON", "stringify", "not-an-array", "moduleName", "className", "funcName", "incorrect-type", "expectedType", "classNameStr", "incorrect-class", "expectedClassName", "isReturnValueProblem", "missing-a-method", "<PERSON><PERSON><PERSON><PERSON>", "add-to-cache-list-unexpected-type", "entry", "add-to-cache-list-conflicting-entries", "firstEntry", "secondEntry", "plugin-error-request-will-fetch", "thrownErrorMessage", "invalid-cache-name", "cacheNameId", "unregister-route-but-not-found-with-method", "unregister-route-route-not-registered", "queue-replay-failed", "name", "duplicate-queue-name", "expired-test-without-max-age", "methodName", "unsupported-route-type", "not-array-of-class", "expectedClass", "max-entries-or-age-required", "statuses-or-headers-required", "invalid-string", "channel-name-required", "invalid-responses-are-same-args", "expire-custom-caches-only", "unit-must-be-bytes", "normalizedRangeHeader", "single-range-only", "invalid-range-values", "no-range-header", "range-not-satisfiable", "size", "start", "end", "attempt-to-cache-non-get-request", "url", "cache-put-with-no-response", "no-response", "message", "bad-precaching-response", "status", "non-precached-url", "add-to-cache-list-conflicting-integrities", "missing-precache-entry", "cacheName", "cross-origin-copy-response", "origin", "opaque-streams-source", "type", "generatorFunction", "code", "details", "messageGenerator", "WorkboxError", "constructor", "errorCode", "isArray", "Array", "has<PERSON><PERSON><PERSON>", "object", "isType", "isInstance", "isOneOf", "validValues", "includes", "isArrayOfClass", "item", "finalAssertExports", "defaultMethod", "validMethods", "normalize<PERSON><PERSON><PERSON>", "handler", "assert", "handle", "Route", "match", "setCatchHandler", "<PERSON><PERSON><PERSON><PERSON>", "RegExpRoute", "regExp", "RegExp", "result", "exec", "href", "location", "index", "toString", "slice", "getFriendlyURL", "url<PERSON>bj", "URL", "String", "replace", "Router", "_routes", "Map", "_defaultHandlerMap", "routes", "addFetchListener", "addEventListener", "event", "request", "responsePromise", "handleRequest", "respondWith", "addCacheListener", "data", "payload", "urlsToCache", "requestPromises", "Promise", "all", "map", "Request", "waitUntil", "ports", "then", "postMessage", "protocol", "startsWith", "<PERSON><PERSON><PERSON><PERSON>", "params", "route", "findMatchingRoute", "debugMessages", "push", "has", "get", "for<PERSON>ach", "msg", "err", "reject", "_catch<PERSON><PERSON>ler", "catch", "catchErr", "matchResult", "length", "undefined", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set", "registerRoute", "unregisterRoute", "routeIndex", "indexOf", "splice", "defaultRouter", "getOrCreateDefaultRouter", "capture", "captureUrl", "valueToCheck", "pathname", "wildcards", "matchCallback", "_cacheNameDetails", "googleAnalytics", "precache", "prefix", "runtime", "suffix", "registration", "scope", "_createCacheName", "filter", "eachCacheNameDetail", "fn", "cacheNames", "updateDetails", "getGoogleAnalyticsName", "userCacheName", "getPrecacheName", "getPrefix", "getRuntimeName", "getSuffix", "dontWait<PERSON>or", "promise", "quotaErrorCallbacks", "Set", "registerQuotaErrorCallback", "callback", "add", "instanceOfAny", "constructors", "some", "c", "idbProxyableTypes", "cursorAdvanceMethods", "getIdbProxyableTypes", "IDBDatabase", "IDBObjectStore", "IDBIndex", "IDBCursor", "IDBTransaction", "getCursorAdvanceMethods", "prototype", "advance", "continue", "continuePrimaryKey", "cursorRequestMap", "WeakMap", "transactionDoneMap", "transactionStoreNamesMap", "transformCache", "reverseTransformCache", "promisifyRequest", "resolve", "unlisten", "removeEventListener", "success", "wrap", "cacheDonePromiseForTransaction", "tx", "done", "complete", "DOMException", "idbProxyTraps", "target", "prop", "receiver", "objectStoreNames", "objectStore", "replaceTraps", "wrapFunction", "func", "transaction", "storeNames", "call", "unwrap", "sort", "apply", "transformCachableValue", "Proxy", "IDBRequest", "newValue", "openDB", "version", "blocked", "upgrade", "blocking", "terminated", "indexedDB", "open", "openPromise", "oldVersion", "newVersion", "db", "deleteDB", "deleteDatabase", "readMethods", "writeMethods", "cachedMethods", "getMethod", "targetFuncName", "useIndex", "isWrite", "storeName", "store", "shift", "oldTraps", "_extends", "DB_NAME", "CACHE_OBJECT_STORE", "normalizeURL", "unNormalizedUrl", "hash", "CacheTimestampsModel", "_db", "_cacheName", "_upgradeDb", "objStore", "createObjectStore", "keyP<PERSON>", "createIndex", "unique", "_upgradeDbAndDeleteOldDbs", "setTimestamp", "timestamp", "id", "_getId", "getDb", "durability", "put", "getTimestamp", "expireEntries", "minTimestamp", "maxCount", "cursor", "openCursor", "entriesToDelete", "entriesNotDeletedCount", "urlsDeleted", "delete", "bind", "CacheExpiration", "config", "_isRunning", "_rerunRequested", "maxEntries", "maxAgeSeconds", "_maxEntries", "_maxAgeSeconds", "_matchOptions", "matchOptions", "_timestampModel", "Date", "now", "urlsExpired", "cache", "caches", "updateTimestamp", "isURLExpired", "expire<PERSON><PERSON><PERSON><PERSON>", "Infinity", "ExpirationPlugin", "cachedResponseWillBeUsed", "cachedResponse", "isFresh", "_isResponseDateFresh", "cacheExpiration", "_getCacheExpiration", "updateTimestampDone", "cacheDidUpdate", "_config", "_cacheExpirations", "purgeOnQuotaError", "deleteCacheAndMetadata", "dateHeaderTimestamp", "_getDateHeaderTimestamp", "headers", "<PERSON><PERSON><PERSON><PERSON>", "parsedDate", "headerTime", "getTime", "isNaN", "stripParams", "fullURL", "ignoreParams", "strippedURL", "param", "searchParams", "cacheMatchIgnoreParams", "strippedRequestURL", "keysOptions", "assign", "ignoreSearch", "cacheKeys", "cache<PERSON>ey", "strippedCacheKeyURL", "Deferred", "executeQuotaErrorCallbacks", "timeout", "ms", "setTimeout", "toRequest", "input", "StrategyHandler", "strategy", "options", "_cacheKeys", "ExtendableEvent", "_strategy", "_handler<PERSON><PERSON><PERSON><PERSON>", "_extendLifetimePromises", "_plugins", "plugins", "_pluginStateMap", "plugin", "fetch", "mode", "FetchEvent", "preloadResponse", "possiblePreloadResponse", "originalRequest", "<PERSON><PERSON><PERSON><PERSON>", "clone", "cb", "iterateCallbacks", "pluginFilteredRequest", "fetchResponse", "fetchOptions", "response", "runCallbacks", "fetchAndCachePut", "responseClone", "cachePut", "cacheMatch", "effectiveRequest", "get<PERSON><PERSON><PERSON><PERSON>", "multiMatchOptions", "vary", "responseToCache", "_ensureResponseSafeToCache", "hasCacheUpdateCallback", "oldResponse", "newResponse", "state", "stateful<PERSON><PERSON><PERSON>", "statefulParam", "doneWaiting", "destroy", "pluginsUsed", "Strategy", "responseDone", "handleAll", "_getResponse", "handlerDone", "_awaitComplete", "_handle", "waitUntilError", "strategyStart", "strategyName", "printFinalResponse", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logs", "CacheableResponse", "statuses", "_statuses", "_headers", "isResponseCacheable", "Response", "cacheable", "headerName", "logFriendlyHeaders", "CacheableResponsePlugin", "cacheWillUpdate", "_cacheableResponse", "cacheOkAndOpaquePlugin", "StaleWhileRevalidate", "p", "unshift", "fetchAndCachePromise", "NetworkFirst", "_networkTimeoutSeconds", "networkTimeoutSeconds", "promises", "timeoutId", "_getTimeoutPromise", "networkPromise", "_getNetworkPromise", "race", "timeoutPromise", "onNetworkTimeout", "fetchError", "clearTimeout", "DB_VERSION", "REQUEST_OBJECT_STORE_NAME", "QUEUE_NAME_INDEX", "QueueDb", "addEntry", "getFirstEntryId", "getAllEntriesByQueueName", "queueName", "results", "getAllFromIndex", "IDBKeyRange", "only", "getEntryCountByQueueName", "countFromIndex", "deleteEntry", "getFirstEntryByQueueName", "getEndEntryFromIndex", "getLastEntryByQueueName", "query", "direction", "contains", "deleteObjectStore", "autoIncrement", "QueueStore", "_queueName", "_queueDb", "pushEntry", "requestData", "unshiftEntry", "firstId", "popEntry", "_removeEntry", "shiftEntry", "getAll", "serializableProperties", "StorableRequest", "fromRequest", "body", "arrayBuffer", "entries", "_requestData", "toObject", "TAG_PREFIX", "MAX_RETENTION_TIME", "queueNames", "convertEntry", "queueStoreEntry", "queueEntry", "metadata", "Queue", "forceSyncFallback", "onSync", "maxRetentionTime", "_syncInProgress", "_requestsAddedDuringSync", "_name", "_onSync", "replayRequests", "_maxRetentionTime", "_forceSyncFallback", "Boolean", "_queueStore", "_addSyncListener", "pushRequest", "_addRequest", "unshiftRequest", "popRequest", "_removeRequest", "shiftRequest", "allEntries", "unexpiredEntries", "maxRetentionTimeInMs", "operation", "storableRequest", "registerSync", "sync", "register", "tag", "syncComplete", "syncError", "queue", "lastC<PERSON>ce", "_queueNames", "BackgroundSyncPlugin", "fetchDidFail", "_queue", "clientsClaim", "clients", "claim", "asyncFn", "returnPromise", "REVISION_SEARCH_PARAM", "createCacheKey", "urlObject", "revision", "cacheKeyURL", "originalURL", "PrecacheInstallReportPlugin", "updatedURLs", "notUpdatedURLs", "handlerWillStart", "PrecacheCacheKeyPlugin", "precacheController", "cacheKeyWillBeUsed", "_precacheController", "getCacheKeyForURL", "logGroup", "groupTitle", "deletedURLs", "printCleanupDetails", "deletionCount", "_nestedGroup", "urls", "printInstallDetails", "urlsToPrecache", "urlsAlreadyPrecached", "precachedCount", "alreadyPrecachedCount", "supportStatus", "canConstructResponseFromBodyStream", "testResponse", "copyResponse", "modifier", "responseURL", "clonedResponse", "responseInit", "Headers", "statusText", "modifiedResponseInit", "blob", "PrecacheStrategy", "_fallbackToNetwork", "fallbackToNetwork", "copyRedirectedCacheableResponsesPlugin", "_handleInstall", "_handleFetch", "integrityInManifest", "integrity", "integrityInRequest", "noIntegrityConflict", "_useDefaultCacheabilityPluginIfNeeded", "was<PERSON>ached", "defaultPluginIndex", "cacheWillUpdatePluginCount", "defaultPrecacheCacheabilityPlugin", "redirected", "PrecacheController", "_urlsTo<PERSON><PERSON><PERSON><PERSON><PERSON>", "_urlsToCacheModes", "_cacheKeysToIntegrities", "install", "activate", "addToCacheList", "_installAndActiveListenersAdded", "urlsToWarnAbout", "cacheMode", "warningMessage", "installReportPlugin", "credentials", "currentlyCachedRequests", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "values", "getURLsToCacheKeys", "getCachedURLs", "getIntegrityForCacheKey", "matchPrecache", "createHandlerBoundToURL", "getOrCreatePrecacheController", "removeIgnoredSearchParams", "ignoreURLParametersMatching", "generateURLVariations", "directoryIndex", "cleanURLs", "urlManipulation", "urlWithoutIgnoredParams", "endsWith", "directoryURL", "cleanURL", "additionalURLs", "urlToAttempt", "PrecacheRoute", "urlsTo<PERSON>ache<PERSON><PERSON>s", "possibleURL", "addRoute", "precacheRoute", "precacheAndRoute", "SUBSTRING_TO_FIND", "deleteOutdatedCaches", "currentPrecacheName", "substringToFind", "cacheNamesToDelete", "cleanupOutdatedCaches", "cachesDeleted", "NavigationRoute", "allowlist", "denylist", "_match", "_allowlist", "_denylist", "pathnameAndSearch", "search", "skipWaiting", "workbox_core_clientsClaim", "workbox_precaching_precacheAndRoute", "workbox_precaching_cleanupOutdatedCaches", "workbox_routing_registerRoute", "workbox_routing_NavigationRoute", "workbox_precaching_createHandlerBoundToURL", "workbox_strategies_CacheFirst", "workbox_expiration_ExpirationPlugin", "workbox_cacheable_response_CacheableResponsePlugin", "workbox_strategies_StaleWhileRevalidate", "workbox_strategies_NetworkFirst", "workbox_background_sync_BackgroundSyncPlugin"], "mappings": "AACA;AACA,IAAI;AACAA,EAAAA,IAAI,CAAC,oBAAoB,CAAC,IAAIC,CAAC,EAAE,CAAA;AACrC,CAAC,CACD,OAAOC,CAAC,EAAE;;ACLV;AACA;AACA;AACA;AACA;AACA;AAEA,MAAMC,MAAM,GAEN,CAAC,MAAM;AACL;AACA;AACA,EAAA,IAAI,EAAE,uBAAuB,IAAIC,UAAU,CAAC,EAAE;IAC1CJ,IAAI,CAACK,qBAAqB,GAAG,KAAK,CAAA;AACtC,GAAA;EACA,IAAIC,OAAO,GAAG,KAAK,CAAA;AACnB,EAAA,MAAMC,gBAAgB,GAAG;AACrBC,IAAAA,KAAK,EAAE,CAAS,OAAA,CAAA;AAChBC,IAAAA,GAAG,EAAE,CAAS,OAAA,CAAA;AACdC,IAAAA,IAAI,EAAE,CAAS,OAAA,CAAA;AACfC,IAAAA,KAAK,EAAE,CAAS,OAAA,CAAA;AAChBC,IAAAA,cAAc,EAAE,CAAS,OAAA,CAAA;IACzBC,QAAQ,EAAE,IAAI;GACjB,CAAA;AACD,EAAA,MAAMC,KAAK,GAAG,UAAUC,MAAM,EAAEC,IAAI,EAAE;IAClC,IAAIhB,IAAI,CAACK,qBAAqB,EAAE;AAC5B,MAAA,OAAA;AACJ,KAAA;IACA,IAAIU,MAAM,KAAK,gBAAgB,EAAE;AAC7B;AACA;MACA,IAAI,gCAAgC,CAACE,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,EAAE;AAC5DC,QAAAA,OAAO,CAACL,MAAM,CAAC,CAAC,GAAGC,IAAI,CAAC,CAAA;AACxB,QAAA,OAAA;AACJ,OAAA;AACJ,KAAA;AACA,IAAA,MAAMK,MAAM,GAAG,CACX,CAAed,YAAAA,EAAAA,gBAAgB,CAACQ,MAAM,CAAC,CAAE,CAAA,EACzC,sBAAsB,EACtB,CAAA,YAAA,CAAc,EACd,CAAmB,iBAAA,CAAA,EACnB,oBAAoB,CACvB,CAAA;AACD;AACA,IAAA,MAAMO,SAAS,GAAGhB,OAAO,GAAG,EAAE,GAAG,CAAC,WAAW,EAAEe,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;IAChEH,OAAO,CAACL,MAAM,CAAC,CAAC,GAAGO,SAAS,EAAE,GAAGN,IAAI,CAAC,CAAA;IACtC,IAAID,MAAM,KAAK,gBAAgB,EAAE;AAC7BT,MAAAA,OAAO,GAAG,IAAI,CAAA;AAClB,KAAA;IACA,IAAIS,MAAM,KAAK,UAAU,EAAE;AACvBT,MAAAA,OAAO,GAAG,KAAK,CAAA;AACnB,KAAA;GACH,CAAA;AACD;EACA,MAAMkB,GAAG,GAAG,EAAE,CAAA;AACd,EAAA,MAAMC,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACpB,gBAAgB,CAAC,CAAA;AACnD,EAAA,KAAK,MAAMqB,GAAG,IAAIH,aAAa,EAAE;IAC7B,MAAMV,MAAM,GAAGa,GAAG,CAAA;AAClBJ,IAAAA,GAAG,CAACT,MAAM,CAAC,GAAG,CAAC,GAAGC,IAAI,KAAK;AACvBF,MAAAA,KAAK,CAACC,MAAM,EAAEC,IAAI,CAAC,CAAA;KACtB,CAAA;AACL,GAAA;AACA,EAAA,OAAOQ,GAAG,CAAA;AACd,CAAC,GAAI;;AC/DT;AACA;AACA;AACA;AACA;AACA;AACA;AAEO,MAAMK,UAAQ,GAAG;AACpB,EAAA,eAAe,EAAEC,CAAC;IAAEC,SAAS;IAAEC,qBAAqB;AAAEC,IAAAA,KAAAA;AAAM,GAAC,KAAK;AAC9D,IAAA,IAAI,CAACF,SAAS,IAAI,CAACC,qBAAqB,EAAE;AACtC,MAAA,MAAM,IAAIE,KAAK,CAAC,CAAA,0CAAA,CAA4C,CAAC,CAAA;AACjE,KAAA;AACA,IAAA,OAAQ,CAAQH,KAAAA,EAAAA,SAAS,CAAwC,sCAAA,CAAA,GAC7D,qBAAqBC,qBAAqB,CAAA,qBAAA,CAAuB,GACjE,CAAA,EAAGG,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC,CAAG,CAAA,CAAA,CAAA;GAClC;AACD,EAAA,cAAc,EAAEI,CAAC;IAAEC,UAAU;IAAEC,SAAS;IAAEC,QAAQ;AAAET,IAAAA,SAAAA;AAAU,GAAC,KAAK;IAChE,IAAI,CAACO,UAAU,IAAI,CAACC,SAAS,IAAI,CAACC,QAAQ,IAAI,CAACT,SAAS,EAAE;AACtD,MAAA,MAAM,IAAIG,KAAK,CAAC,CAAA,yCAAA,CAA2C,CAAC,CAAA;AAChE,KAAA;IACA,OAAQ,CAAA,eAAA,EAAkBH,SAAS,CAAA,cAAA,CAAgB,GAC/C,CAAA,CAAA,EAAIO,UAAU,CAAIC,CAAAA,EAAAA,SAAS,CAAIC,CAAAA,EAAAA,QAAQ,CAAuB,qBAAA,CAAA,CAAA;GACrE;AACD,EAAA,gBAAgB,EAAEC,CAAC;IAAEC,YAAY;IAAEX,SAAS;IAAEO,UAAU;IAAEC,SAAS;AAAEC,IAAAA,QAAAA;AAAU,GAAC,KAAK;IACjF,IAAI,CAACE,YAAY,IAAI,CAACX,SAAS,IAAI,CAACO,UAAU,IAAI,CAACE,QAAQ,EAAE;AACzD,MAAA,MAAM,IAAIN,KAAK,CAAC,CAAA,2CAAA,CAA6C,CAAC,CAAA;AAClE,KAAA;IACA,MAAMS,YAAY,GAAGJ,SAAS,GAAG,GAAGA,SAAS,CAAA,CAAA,CAAG,GAAG,EAAE,CAAA;AACrD,IAAA,OAAQ,CAAkBR,eAAAA,EAAAA,SAAS,CAAgB,cAAA,CAAA,GAC/C,IAAIO,UAAU,CAAA,CAAA,EAAIK,YAAY,CAAA,CAAE,GAChC,CAAA,EAAGH,QAAQ,CAAA,oBAAA,EAAuBE,YAAY,CAAG,CAAA,CAAA,CAAA;GACxD;AACD,EAAA,iBAAiB,EAAEE,CAAC;IAAEC,iBAAiB;IAAEd,SAAS;IAAEO,UAAU;IAAEC,SAAS;IAAEC,QAAQ;AAAEM,IAAAA,oBAAAA;AAAsB,GAAC,KAAK;IAC7G,IAAI,CAACD,iBAAiB,IAAI,CAACP,UAAU,IAAI,CAACE,QAAQ,EAAE;AAChD,MAAA,MAAM,IAAIN,KAAK,CAAC,CAAA,4CAAA,CAA8C,CAAC,CAAA;AACnE,KAAA;IACA,MAAMS,YAAY,GAAGJ,SAAS,GAAG,GAAGA,SAAS,CAAA,CAAA,CAAG,GAAG,EAAE,CAAA;AACrD,IAAA,IAAIO,oBAAoB,EAAE;AACtB,MAAA,OAAQ,CAAwB,sBAAA,CAAA,GAC5B,CAAIR,CAAAA,EAAAA,UAAU,CAAIK,CAAAA,EAAAA,YAAY,CAAGH,EAAAA,QAAQ,CAAM,IAAA,CAAA,GAC/C,CAAgCK,6BAAAA,EAAAA,iBAAiB,CAAG,CAAA,CAAA,CAAA;AAC5D,KAAA;AACA,IAAA,OAAQ,CAAkBd,eAAAA,EAAAA,SAAS,CAAgB,cAAA,CAAA,GAC/C,IAAIO,UAAU,CAAA,CAAA,EAAIK,YAAY,CAAA,EAAGH,QAAQ,CAAA,IAAA,CAAM,GAC/C,CAAA,6BAAA,EAAgCK,iBAAiB,CAAG,CAAA,CAAA,CAAA;GAC3D;AACD,EAAA,kBAAkB,EAAEE,CAAC;IAAEC,cAAc;IAAEjB,SAAS;IAAEO,UAAU;IAAEC,SAAS;AAAEC,IAAAA,QAAAA;AAAU,GAAC,KAAK;AACrF,IAAA,IAAI,CAACQ,cAAc,IACf,CAACjB,SAAS,IACV,CAACO,UAAU,IACX,CAACC,SAAS,IACV,CAACC,QAAQ,EAAE;AACX,MAAA,MAAM,IAAIN,KAAK,CAAC,CAAA,6CAAA,CAA+C,CAAC,CAAA;AACpE,KAAA;AACA,IAAA,OAAQ,CAAGI,EAAAA,UAAU,CAAIC,CAAAA,EAAAA,SAAS,CAAIC,CAAAA,EAAAA,QAAQ,CAAkB,gBAAA,CAAA,GAC5D,CAAIT,CAAAA,EAAAA,SAAS,CAA4BiB,yBAAAA,EAAAA,cAAc,CAAW,SAAA,CAAA,CAAA;GACzE;AACD,EAAA,mCAAmC,EAAEC,CAAC;AAAEC,IAAAA,KAAAA;AAAM,GAAC,KAAK;AAChD,IAAA,OAAQ,CAAoC,kCAAA,CAAA,GACxC,CAAqE,mEAAA,CAAA,GACrE,IAAIf,IAAI,CAACC,SAAS,CAACc,KAAK,CAAC,CAAA,+CAAA,CAAiD,GAC1E,CAAA,oEAAA,CAAsE,GACtE,CAAkB,gBAAA,CAAA,CAAA;GACzB;AACD,EAAA,uCAAuC,EAAEC,CAAC;IAAEC,UAAU;AAAEC,IAAAA,WAAAA;AAAY,GAAC,KAAK;AACtE,IAAA,IAAI,CAACD,UAAU,IAAI,CAACC,WAAW,EAAE;AAC7B,MAAA,MAAM,IAAInB,KAAK,CAAC,CAAsB,oBAAA,CAAA,GAAG,8CAA8C,CAAC,CAAA;AAC5F,KAAA;IACA,OAAQ,CAAA,6BAAA,CAA+B,GACnC,CAAA,qEAAA,CAAuE,GACvE,CAAA,EAAGkB,UAAU,CAA8C,4CAAA,CAAA,GAC3D,CAAqE,mEAAA,CAAA,GACrE,CAAiB,eAAA,CAAA,CAAA;GACxB;AACD,EAAA,iCAAiC,EAAEE,CAAC;AAAEC,IAAAA,kBAAAA;AAAmB,GAAC,KAAK;IAC3D,IAAI,CAACA,kBAAkB,EAAE;AACrB,MAAA,MAAM,IAAIrB,KAAK,CAAC,CAAsB,oBAAA,CAAA,GAAG,2CAA2C,CAAC,CAAA;AACzF,KAAA;AACA,IAAA,OAAQ,CAAgE,8DAAA,CAAA,GACpE,CAAkCqB,+BAAAA,EAAAA,kBAAkB,CAAI,EAAA,CAAA,CAAA;GAC/D;AACD,EAAA,oBAAoB,EAAEC,CAAC;IAAEC,WAAW;AAAExB,IAAAA,KAAAA;AAAM,GAAC,KAAK;IAC9C,IAAI,CAACwB,WAAW,EAAE;AACd,MAAA,MAAM,IAAIvB,KAAK,CAAC,CAAA,uDAAA,CAAyD,CAAC,CAAA;AAC9E,KAAA;AACA,IAAA,OAAQ,CAAgE,8DAAA,CAAA,GACpE,CAAoBuB,iBAAAA,EAAAA,WAAW,CAAiC,+BAAA,CAAA,GAChE,CAAItB,CAAAA,EAAAA,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC,CAAG,CAAA,CAAA,CAAA;GACnC;AACD,EAAA,4CAA4C,EAAEyB,CAAC;AAAE3C,IAAAA,MAAAA;AAAO,GAAC,KAAK;IAC1D,IAAI,CAACA,MAAM,EAAE;AACT,MAAA,MAAM,IAAImB,KAAK,CAAC,CAAsB,oBAAA,CAAA,GAClC,qDAAqD,CAAC,CAAA;AAC9D,KAAA;AACA,IAAA,OAAQ,CAA4D,0DAAA,CAAA,GAChE,CAAmCnB,gCAAAA,EAAAA,MAAM,CAAI,EAAA,CAAA,CAAA;GACpD;EACD,uCAAuC,EAAE4C,MAAM;IAC3C,OAAQ,CAAA,yDAAA,CAA2D,GAC/D,CAAa,WAAA,CAAA,CAAA;GACpB;AACD,EAAA,qBAAqB,EAAEC,CAAC;AAAEC,IAAAA,IAAAA;AAAK,GAAC,KAAK;IACjC,OAAO,CAAA,qCAAA,EAAwCA,IAAI,CAAW,SAAA,CAAA,CAAA;GACjE;AACD,EAAA,sBAAsB,EAAEC,CAAC;AAAED,IAAAA,IAAAA;AAAK,GAAC,KAAK;AAClC,IAAA,OAAQ,CAAmBA,gBAAAA,EAAAA,IAAI,CAA2B,yBAAA,CAAA,GACtD,CAAmE,iEAAA,CAAA,CAAA;GAC1E;AACD,EAAA,8BAA8B,EAAEE,CAAC;IAAEC,UAAU;AAAEjC,IAAAA,SAAAA;AAAU,GAAC,KAAK;AAC3D,IAAA,OAAQ,QAAQiC,UAAU,CAAA,qCAAA,CAAuC,GAC7D,CAAA,CAAA,EAAIjC,SAAS,CAA+B,6BAAA,CAAA,CAAA;GACnD;AACD,EAAA,wBAAwB,EAAEkC,CAAC;IAAE3B,UAAU;IAAEC,SAAS;IAAEC,QAAQ;AAAET,IAAAA,SAAAA;AAAU,GAAC,KAAK;AAC1E,IAAA,OAAQ,CAAiBA,cAAAA,EAAAA,SAAS,CAAuC,qCAAA,CAAA,GACrE,CAA6BO,0BAAAA,EAAAA,UAAU,CAAIC,CAAAA,EAAAA,SAAS,CAAIC,CAAAA,EAAAA,QAAQ,CAAO,KAAA,CAAA,GACvE,CAAoB,kBAAA,CAAA,CAAA;GAC3B;AACD,EAAA,oBAAoB,EAAE0B,CAAC;IAAEjC,KAAK;IAAEkC,aAAa;IAAE7B,UAAU;IAAEC,SAAS;IAAEC,QAAQ;AAAET,IAAAA,SAAAA;AAAW,GAAC,KAAK;IAC7F,OAAQ,CAAA,cAAA,EAAiBA,SAAS,CAAkC,gCAAA,CAAA,GAChE,IAAIoC,aAAa,CAAA,qBAAA,EAAwBhC,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC,CAAA,IAAA,CAAM,GACpE,CAAA,yBAAA,EAA4BK,UAAU,CAAA,CAAA,EAAIC,SAAS,CAAIC,CAAAA,EAAAA,QAAQ,CAAK,GAAA,CAAA,GACpE,CAAmB,iBAAA,CAAA,CAAA;GAC1B;AACD,EAAA,6BAA6B,EAAE4B,CAAC;IAAE9B,UAAU;IAAEC,SAAS;AAAEC,IAAAA,QAAAA;AAAS,GAAC,KAAK;IACpE,OAAQ,CAAA,gEAAA,CAAkE,GACtE,CAAMF,GAAAA,EAAAA,UAAU,IAAIC,SAAS,CAAA,CAAA,EAAIC,QAAQ,CAAE,CAAA,CAAA;GAClD;AACD,EAAA,8BAA8B,EAAE6B,CAAC;IAAE/B,UAAU;IAAEC,SAAS;AAAEC,IAAAA,QAAAA;AAAS,GAAC,KAAK;IACrE,OAAQ,CAAA,wDAAA,CAA0D,GAC9D,CAAMF,GAAAA,EAAAA,UAAU,IAAIC,SAAS,CAAA,CAAA,EAAIC,QAAQ,CAAE,CAAA,CAAA;GAClD;AACD,EAAA,gBAAgB,EAAE8B,CAAC;IAAEhC,UAAU;IAAEE,QAAQ;AAAET,IAAAA,SAAAA;AAAU,GAAC,KAAK;IACvD,IAAI,CAACA,SAAS,IAAI,CAACO,UAAU,IAAI,CAACE,QAAQ,EAAE;AACxC,MAAA,MAAM,IAAIN,KAAK,CAAC,CAAA,2CAAA,CAA6C,CAAC,CAAA;AAClE,KAAA;AACA,IAAA,OAAQ,CAA4BH,yBAAAA,EAAAA,SAAS,CAA8B,4BAAA,CAAA,GACvE,CAAsE,oEAAA,CAAA,GACtE,CAA2BO,wBAAAA,EAAAA,UAAU,CAAIE,CAAAA,EAAAA,QAAQ,CAAS,OAAA,CAAA,GAC1D,CAAY,UAAA,CAAA,CAAA;GACnB;EACD,uBAAuB,EAAE+B,MAAM;IAC3B,OAAQ,CAAA,8CAAA,CAAgD,GACpD,CAAgC,8BAAA,CAAA,CAAA;GACvC;EACD,iCAAiC,EAAEC,MAAM;IACrC,OAAQ,CAAA,0DAAA,CAA4D,GAChE,CAAkD,gDAAA,CAAA,CAAA;GACzD;EACD,2BAA2B,EAAEC,MAAM;IAC/B,OAAQ,CAAA,uDAAA,CAAyD,GAC7D,CAAoD,kDAAA,CAAA,CAAA;GAC3D;AACD,EAAA,oBAAoB,EAAEC,CAAC;AAAEC,IAAAA,qBAAAA;AAAsB,GAAC,KAAK;IACjD,IAAI,CAACA,qBAAqB,EAAE;AACxB,MAAA,MAAM,IAAIzC,KAAK,CAAC,CAAA,+CAAA,CAAiD,CAAC,CAAA;AACtE,KAAA;AACA,IAAA,OAAQ,CAAiE,+DAAA,CAAA,GACrE,CAAkCyC,+BAAAA,EAAAA,qBAAqB,CAAG,CAAA,CAAA,CAAA;GACjE;AACD,EAAA,mBAAmB,EAAEC,CAAC;AAAED,IAAAA,qBAAAA;AAAsB,GAAC,KAAK;IAChD,IAAI,CAACA,qBAAqB,EAAE;AACxB,MAAA,MAAM,IAAIzC,KAAK,CAAC,CAAA,8CAAA,CAAgD,CAAC,CAAA;AACrE,KAAA;AACA,IAAA,OAAQ,gEAAgE,GACpE,CAAA,6DAAA,CAA+D,GAC/D,CAAA,CAAA,EAAIyC,qBAAqB,CAAG,CAAA,CAAA,CAAA;GACnC;AACD,EAAA,sBAAsB,EAAEE,CAAC;AAAEF,IAAAA,qBAAAA;AAAsB,GAAC,KAAK;IACnD,IAAI,CAACA,qBAAqB,EAAE;AACxB,MAAA,MAAM,IAAIzC,KAAK,CAAC,CAAA,iDAAA,CAAmD,CAAC,CAAA;AACxE,KAAA;AACA,IAAA,OAAQ,kEAAkE,GACtE,CAAA,6DAAA,CAA+D,GAC/D,CAAA,CAAA,EAAIyC,qBAAqB,CAAG,CAAA,CAAA,CAAA;GACnC;EACD,iBAAiB,EAAEG,MAAM;AACrB,IAAA,OAAO,CAAoD,kDAAA,CAAA,CAAA;GAC9D;AACD,EAAA,uBAAuB,EAAEC,CAAC;IAAEC,IAAI;IAAEC,KAAK;AAAEC,IAAAA,GAAAA;AAAI,GAAC,KAAK;IAC/C,OAAQ,CAAA,WAAA,EAAcD,KAAK,CAAcC,WAAAA,EAAAA,GAAG,4BAA4B,GACpE,CAAA,iDAAA,EAAoDF,IAAI,CAAS,OAAA,CAAA,CAAA;GACxE;AACD,EAAA,kCAAkC,EAAEG,CAAC;IAAEC,GAAG;AAAErE,IAAAA,MAAAA;AAAO,GAAC,KAAK;AACrD,IAAA,OAAQ,oBAAoBqE,GAAG,CAAA,mBAAA,EAAsBrE,MAAM,CAAA,cAAA,CAAgB,GACvE,CAAoC,kCAAA,CAAA,CAAA;GAC3C;AACD,EAAA,4BAA4B,EAAEsE,CAAC;AAAED,IAAAA,GAAAA;AAAI,GAAC,KAAK;AACvC,IAAA,OAAQ,CAAkCA,+BAAAA,EAAAA,GAAG,CAA6B,2BAAA,CAAA,GACtE,CAAU,QAAA,CAAA,CAAA;GACjB;AACD,EAAA,aAAa,EAAEE,CAAC;IAAEF,GAAG;AAAEzE,IAAAA,KAAAA;AAAM,GAAC,KAAK;AAC/B,IAAA,IAAI4E,OAAO,GAAG,CAAmDH,gDAAAA,EAAAA,GAAG,CAAI,EAAA,CAAA,CAAA;AACxE,IAAA,IAAIzE,KAAK,EAAE;MACP4E,OAAO,IAAI,CAA4B5E,yBAAAA,EAAAA,KAAK,CAAG,CAAA,CAAA,CAAA;AACnD,KAAA;AACA,IAAA,OAAO4E,OAAO,CAAA;GACjB;AACD,EAAA,yBAAyB,EAAEC,CAAC;IAAEJ,GAAG;AAAEK,IAAAA,MAAAA;AAAO,GAAC,KAAK;IAC5C,OAAQ,CAAA,4BAAA,EAA+BL,GAAG,CAAA,QAAA,CAAU,IAC/CK,MAAM,GAAG,CAAA,wBAAA,EAA2BA,MAAM,CAAA,CAAA,CAAG,GAAG,CAAA,CAAA,CAAG,CAAC,CAAA;GAC5D;AACD,EAAA,mBAAmB,EAAEC,CAAC;AAAEN,IAAAA,GAAAA;AAAI,GAAC,KAAK;AAC9B,IAAA,OAAQ,CAA4BA,yBAAAA,EAAAA,GAAG,CAAiC,+BAAA,CAAA,GACpE,CAAgE,8DAAA,CAAA,CAAA;GACvE;AACD,EAAA,2CAA2C,EAAEO,CAAC;AAAEP,IAAAA,GAAAA;AAAI,GAAC,KAAK;AACtD,IAAA,OAAQ,+BAA+B,GACnC,CAAA,qEAAA,CAAuE,GACvE,CAAA,EAAGA,GAAG,CAA8D,4DAAA,CAAA,CAAA;GAC3E;AACD,EAAA,wBAAwB,EAAEQ,CAAC;IAAEC,SAAS;AAAET,IAAAA,GAAAA;AAAI,GAAC,KAAK;AAC9C,IAAA,OAAO,CAA0CS,uCAAAA,EAAAA,SAAS,CAAQT,KAAAA,EAAAA,GAAG,CAAG,CAAA,CAAA,CAAA;GAC3E;AACD,EAAA,4BAA4B,EAAEU,CAAC;AAAEC,IAAAA,MAAAA;AAAO,GAAC,KAAK;AAC1C,IAAA,OAAQ,CAAgE,8DAAA,CAAA,GACpE,CAAmDA,gDAAAA,EAAAA,MAAM,CAAG,CAAA,CAAA,CAAA;GACnE;AACD,EAAA,uBAAuB,EAAEC,CAAC;AAAEC,IAAAA,IAAAA;AAAK,GAAC,KAAK;AACnC,IAAA,MAAMV,OAAO,GAAG,CAAA,kDAAA,CAAoD,GAChE,CAAA,CAAA,EAAIU,IAAI,CAAa,WAAA,CAAA,CAAA;IACzB,IAAIA,IAAI,KAAK,gBAAgB,EAAE;AAC3B,MAAA,OAAQ,CAAGV,EAAAA,OAAO,CAAuD,qDAAA,CAAA,GACrE,CAA4B,0BAAA,CAAA,CAAA;AACpC,KAAA;IACA,OAAO,CAAA,EAAGA,OAAO,CAA+C,6CAAA,CAAA,CAAA;AACpE,GAAA;AACJ,CAAC;;ACnOD;AACA;AACA;AACA;AACA;AACA;AACA;AAUA,MAAMW,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,OAAO,GAAG,EAAE,KAAK;AAC9C,EAAA,MAAMb,OAAO,GAAG1D,UAAQ,CAACsE,IAAI,CAAC,CAAA;EAC9B,IAAI,CAACZ,OAAO,EAAE;AACV,IAAA,MAAM,IAAIrD,KAAK,CAAC,CAAoCiE,iCAAAA,EAAAA,IAAI,IAAI,CAAC,CAAA;AACjE,GAAA;EACA,OAAOZ,OAAO,CAACa,OAAO,CAAC,CAAA;AAC3B,CAAC,CAAA;AACM,MAAMC,gBAAgB,GAAsDH,iBAAiB;;ACvBpG;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,YAAY,SAASpE,KAAK,CAAC;AAC7B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACIqE,EAAAA,WAAWA,CAACC,SAAS,EAAEJ,OAAO,EAAE;AAC5B,IAAA,MAAMb,OAAO,GAAGc,gBAAgB,CAACG,SAAS,EAAEJ,OAAO,CAAC,CAAA;IACpD,KAAK,CAACb,OAAO,CAAC,CAAA;IACd,IAAI,CAAC1B,IAAI,GAAG2C,SAAS,CAAA;IACrB,IAAI,CAACJ,OAAO,GAAGA,OAAO,CAAA;AAC1B,GAAA;AACJ;;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,OAAO,GAAGA,CAACxE,KAAK,EAAEmE,OAAO,KAAK;AAChC,EAAA,IAAI,CAACM,KAAK,CAACD,OAAO,CAACxE,KAAK,CAAC,EAAE;AACvB,IAAA,MAAM,IAAIqE,YAAY,CAAC,cAAc,EAAEF,OAAO,CAAC,CAAA;AACnD,GAAA;AACJ,CAAC,CAAA;AACD,MAAMO,SAAS,GAAGA,CAACC,MAAM,EAAE5D,cAAc,EAAEoD,OAAO,KAAK;AACnD,EAAA,MAAMH,IAAI,GAAG,OAAOW,MAAM,CAAC5D,cAAc,CAAC,CAAA;EAC1C,IAAIiD,IAAI,KAAK,UAAU,EAAE;AACrBG,IAAAA,OAAO,CAAC,gBAAgB,CAAC,GAAGpD,cAAc,CAAA;AAC1C,IAAA,MAAM,IAAIsD,YAAY,CAAC,kBAAkB,EAAEF,OAAO,CAAC,CAAA;AACvD,GAAA;AACJ,CAAC,CAAA;AACD,MAAMS,MAAM,GAAGA,CAACD,MAAM,EAAElE,YAAY,EAAE0D,OAAO,KAAK;AAC9C,EAAA,IAAI,OAAOQ,MAAM,KAAKlE,YAAY,EAAE;AAChC0D,IAAAA,OAAO,CAAC,cAAc,CAAC,GAAG1D,YAAY,CAAA;AACtC,IAAA,MAAM,IAAI4D,YAAY,CAAC,gBAAgB,EAAEF,OAAO,CAAC,CAAA;AACrD,GAAA;AACJ,CAAC,CAAA;AACD,MAAMU,UAAU,GAAGA,CAACF,MAAM;AAC1B;AACA;AACAzC,aAAa,EAAEiC,OAAO,KAAK;AACvB,EAAA,IAAI,EAAEQ,MAAM,YAAYzC,aAAa,CAAC,EAAE;AACpCiC,IAAAA,OAAO,CAAC,mBAAmB,CAAC,GAAGjC,aAAa,CAACN,IAAI,CAAA;AACjD,IAAA,MAAM,IAAIyC,YAAY,CAAC,iBAAiB,EAAEF,OAAO,CAAC,CAAA;AACtD,GAAA;AACJ,CAAC,CAAA;AACD,MAAMW,OAAO,GAAGA,CAAC9E,KAAK,EAAE+E,WAAW,EAAEZ,OAAO,KAAK;AAC7C,EAAA,IAAI,CAACY,WAAW,CAACC,QAAQ,CAAChF,KAAK,CAAC,EAAE;IAC9BmE,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAAA,iBAAA,EAAoBjE,IAAI,CAACC,SAAS,CAAC4E,WAAW,CAAC,CAAG,CAAA,CAAA,CAAA;AACrF,IAAA,MAAM,IAAIV,YAAY,CAAC,eAAe,EAAEF,OAAO,CAAC,CAAA;AACpD,GAAA;AACJ,CAAC,CAAA;AACD,MAAMc,cAAc,GAAGA,CAACjF,KAAK;AAC7B;AACAkC,aAAa;AAAE;AACfiC,OAAO,KAAK;EACR,MAAMzF,KAAK,GAAG,IAAI2F,YAAY,CAAC,oBAAoB,EAAEF,OAAO,CAAC,CAAA;AAC7D,EAAA,IAAI,CAACM,KAAK,CAACD,OAAO,CAACxE,KAAK,CAAC,EAAE;AACvB,IAAA,MAAMtB,KAAK,CAAA;AACf,GAAA;AACA,EAAA,KAAK,MAAMwG,IAAI,IAAIlF,KAAK,EAAE;AACtB,IAAA,IAAI,EAAEkF,IAAI,YAAYhD,aAAa,CAAC,EAAE;AAClC,MAAA,MAAMxD,KAAK,CAAA;AACf,KAAA;AACJ,GAAA;AACJ,CAAC,CAAA;AACD,MAAMyG,kBAAkB,GAElB;EACET,SAAS;EACTF,OAAO;EACPK,UAAU;EACVC,OAAO;EACPF,MAAM;AACNK,EAAAA,cAAAA;AACJ,CAAC;;ACtEL;AACA,IAAI;AACAlH,EAAAA,IAAI,CAAC,uBAAuB,CAAC,IAAIC,CAAC,EAAE,CAAA;AACxC,CAAC,CACD,OAAOC,CAAC,EAAE;;ACLV;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMmH,aAAa,GAAG,KAAK,CAAA;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,YAAY,GAAG,CACxB,QAAQ,EACR,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,KAAK,CACR;;AC/BD;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;AACzC,EAAA,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IACG;AACvCC,MAAAA,kBAAM,CAACd,SAAS,CAACa,OAAO,EAAE,QAAQ,EAAE;AAChClF,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,OAAO;AAClBC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,SAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;AACA,IAAA,OAAOyF,OAAO,CAAA;AAClB,GAAC,MACI;IAC0C;AACvCC,MAAAA,kBAAM,CAACZ,MAAM,CAACW,OAAO,EAAE,UAAU,EAAE;AAC/BlF,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,OAAO;AAClBC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,SAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;IACA,OAAO;AAAE2F,MAAAA,MAAM,EAAEF,OAAAA;KAAS,CAAA;AAC9B,GAAA;AACJ,CAAC;;ACvCD;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,KAAK,CAAC;AACR;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpB,WAAWA,CAACqB,KAAK,EAAEJ,OAAO,EAAEzG,MAAM,GAAGsG,aAAa,EAAE;IACL;AACvCI,MAAAA,kBAAM,CAACZ,MAAM,CAACe,KAAK,EAAE,UAAU,EAAE;AAC7BtF,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,OAAO;AAClBC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,OAAA;AACf,OAAC,CAAC,CAAA;AACF,MAAA,IAAIhB,MAAM,EAAE;AACR0G,QAAAA,kBAAM,CAACV,OAAO,CAAChG,MAAM,EAAEuG,YAAY,EAAE;AAAEvF,UAAAA,SAAS,EAAE,QAAA;AAAS,SAAC,CAAC,CAAA;AACjE,OAAA;AACJ,KAAA;AACA;AACA;AACA,IAAA,IAAI,CAACyF,OAAO,GAAGD,gBAAgB,CAACC,OAAO,CAAC,CAAA;IACxC,IAAI,CAACI,KAAK,GAAGA,KAAK,CAAA;IAClB,IAAI,CAAC7G,MAAM,GAAGA,MAAM,CAAA;AACxB,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI8G,eAAeA,CAACL,OAAO,EAAE;AACrB,IAAA,IAAI,CAACM,YAAY,GAAGP,gBAAgB,CAACC,OAAO,CAAC,CAAA;AACjD,GAAA;AACJ;;AC1DA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,WAAW,SAASJ,KAAK,CAAC;AAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIpB,EAAAA,WAAWA,CAACyB,MAAM,EAAER,OAAO,EAAEzG,MAAM,EAAE;IACU;AACvC0G,MAAAA,kBAAM,CAACX,UAAU,CAACkB,MAAM,EAAEC,MAAM,EAAE;AAC9B3F,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,aAAa;AACxBC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,SAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;IACA,MAAM6F,KAAK,GAAGA,CAAC;AAAExC,MAAAA,GAAAA;AAAI,KAAC,KAAK;MACvB,MAAM8C,MAAM,GAAGF,MAAM,CAACG,IAAI,CAAC/C,GAAG,CAACgD,IAAI,CAAC,CAAA;AACpC;MACA,IAAI,CAACF,MAAM,EAAE;AACT,QAAA,OAAA;AACJ,OAAA;AACA;AACA;AACA;AACA;AACA,MAAA,IAAI9C,GAAG,CAACW,MAAM,KAAKsC,QAAQ,CAACtC,MAAM,IAAImC,MAAM,CAACI,KAAK,KAAK,CAAC,EAAE;QACX;UACvCnI,MAAM,CAACK,KAAK,CAAC,CAAA,wBAAA,EAA2BwH,MAAM,CAACO,QAAQ,EAAE,CAAA,yBAAA,CAA2B,GAChF,CAAiCnD,8BAAAA,EAAAA,GAAG,CAACmD,QAAQ,EAAE,CAA6B,2BAAA,CAAA,GAC5E,4DAA4D,CAAC,CAAA;AACrE,SAAA;AACA,QAAA,OAAA;AACJ,OAAA;AACA;AACA;AACA;AACA;AACA,MAAA,OAAOL,MAAM,CAACM,KAAK,CAAC,CAAC,CAAC,CAAA;KACzB,CAAA;AACD,IAAA,KAAK,CAACZ,KAAK,EAAEJ,OAAO,EAAEzG,MAAM,CAAC,CAAA;AACjC,GAAA;AACJ;;ACvEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAM0H,cAAc,GAAIrD,GAAG,IAAK;AAC5B,EAAA,MAAMsD,MAAM,GAAG,IAAIC,GAAG,CAACC,MAAM,CAACxD,GAAG,CAAC,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;AAClD;AACA;AACA,EAAA,OAAOM,MAAM,CAACN,IAAI,CAACS,OAAO,CAAC,IAAIZ,MAAM,CAAC,CAAA,CAAA,EAAII,QAAQ,CAACtC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAA;AACrE,CAAC;;ACbD;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+C,MAAM,CAAC;AACT;AACJ;AACA;AACIvC,EAAAA,WAAWA,GAAG;AACV,IAAA,IAAI,CAACwC,OAAO,GAAG,IAAIC,GAAG,EAAE,CAAA;AACxB,IAAA,IAAI,CAACC,kBAAkB,GAAG,IAAID,GAAG,EAAE,CAAA;AACvC,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI,IAAIE,MAAMA,GAAG;IACT,OAAO,IAAI,CAACH,OAAO,CAAA;AACvB,GAAA;AACA;AACJ;AACA;AACA;AACII,EAAAA,gBAAgBA,GAAG;AACf;AACAnJ,IAAAA,IAAI,CAACoJ,gBAAgB,CAAC,OAAO,EAAIC,KAAK,IAAK;MACvC,MAAM;AAAEC,QAAAA,OAAAA;AAAQ,OAAC,GAAGD,KAAK,CAAA;AACzB,MAAA,MAAME,eAAe,GAAG,IAAI,CAACC,aAAa,CAAC;QAAEF,OAAO;AAAED,QAAAA,KAAAA;AAAM,OAAC,CAAC,CAAA;AAC9D,MAAA,IAAIE,eAAe,EAAE;AACjBF,QAAAA,KAAK,CAACI,WAAW,CAACF,eAAe,CAAC,CAAA;AACtC,OAAA;AACJ,KAAE,CAAC,CAAA;AACP,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIG,EAAAA,gBAAgBA,GAAG;AACf;AACA1J,IAAAA,IAAI,CAACoJ,gBAAgB,CAAC,SAAS,EAAIC,KAAK,IAAK;AACzC;AACA;MACA,IAAIA,KAAK,CAACM,IAAI,IAAIN,KAAK,CAACM,IAAI,CAAC1D,IAAI,KAAK,YAAY,EAAE;AAChD;QACA,MAAM;AAAE2D,UAAAA,OAAAA;SAAS,GAAGP,KAAK,CAACM,IAAI,CAAA;QACa;UACvCxJ,MAAM,CAACK,KAAK,CAAC,CAAA,4BAAA,CAA8B,EAAEoJ,OAAO,CAACC,WAAW,CAAC,CAAA;AACrE,SAAA;AACA,QAAA,MAAMC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACJ,OAAO,CAACC,WAAW,CAACI,GAAG,CAAE/G,KAAK,IAAK;AACnE,UAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;YAC3BA,KAAK,GAAG,CAACA,KAAK,CAAC,CAAA;AACnB,WAAA;AACA,UAAA,MAAMoG,OAAO,GAAG,IAAIY,OAAO,CAAC,GAAGhH,KAAK,CAAC,CAAA;UACrC,OAAO,IAAI,CAACsG,aAAa,CAAC;YAAEF,OAAO;AAAED,YAAAA,KAAAA;AAAM,WAAC,CAAC,CAAA;AAC7C;AACA;AACA;SACH,CAAC,CAAC,CAAC;AACJA,QAAAA,KAAK,CAACc,SAAS,CAACL,eAAe,CAAC,CAAA;AAChC;QACA,IAAIT,KAAK,CAACe,KAAK,IAAIf,KAAK,CAACe,KAAK,CAAC,CAAC,CAAC,EAAE;AAC/B,UAAA,KAAKN,eAAe,CAACO,IAAI,CAAC,MAAMhB,KAAK,CAACe,KAAK,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;AACrE,SAAA;AACJ,OAAA;AACJ,KAAE,CAAC,CAAA;AACP,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACId,EAAAA,aAAaA,CAAC;IAAEF,OAAO;AAAED,IAAAA,KAAAA;AAAO,GAAC,EAAE;IACY;AACvC5B,MAAAA,kBAAM,CAACX,UAAU,CAACwC,OAAO,EAAEY,OAAO,EAAE;AAChC5H,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,QAAQ;AACnBC,QAAAA,QAAQ,EAAE,eAAe;AACzBT,QAAAA,SAAS,EAAE,iBAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;AACA,IAAA,MAAMqD,GAAG,GAAG,IAAIuD,GAAG,CAACW,OAAO,CAAClE,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;IAC/C,IAAI,CAAChD,GAAG,CAACmF,QAAQ,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;MACS;AACvCrK,QAAAA,MAAM,CAACK,KAAK,CAAC,CAAA,yDAAA,CAA2D,CAAC,CAAA;AAC7E,OAAA;AACA,MAAA,OAAA;AACJ,KAAA;IACA,MAAMiK,UAAU,GAAGrF,GAAG,CAACW,MAAM,KAAKsC,QAAQ,CAACtC,MAAM,CAAA;IACjD,MAAM;MAAE2E,MAAM;AAAEC,MAAAA,KAAAA;AAAM,KAAC,GAAG,IAAI,CAACC,iBAAiB,CAAC;MAC7CvB,KAAK;MACLC,OAAO;MACPmB,UAAU;AACVrF,MAAAA,GAAAA;AACJ,KAAC,CAAC,CAAA;AACF,IAAA,IAAIoC,OAAO,GAAGmD,KAAK,IAAIA,KAAK,CAACnD,OAAO,CAAA;IACpC,MAAMqD,aAAa,GAAG,EAAE,CAAA;IACmB;AACvC,MAAA,IAAIrD,OAAO,EAAE;QACTqD,aAAa,CAACC,IAAI,CAAC,CAAC,uCAAuC,EAAEH,KAAK,CAAC,CAAC,CAAA;AACpE,QAAA,IAAID,MAAM,EAAE;UACRG,aAAa,CAACC,IAAI,CAAC,CACf,sDAAsD,EACtDJ,MAAM,CACT,CAAC,CAAA;AACN,SAAA;AACJ,OAAA;AACJ,KAAA;AACA;AACA;AACA,IAAA,MAAM3J,MAAM,GAAGuI,OAAO,CAACvI,MAAM,CAAA;IAC7B,IAAI,CAACyG,OAAO,IAAI,IAAI,CAACyB,kBAAkB,CAAC8B,GAAG,CAAChK,MAAM,CAAC,EAAE;MACN;QACvC8J,aAAa,CAACC,IAAI,CAAC,CAAA,yCAAA,CAA2C,GAC1D,CAAmC/J,gCAAAA,EAAAA,MAAM,GAAG,CAAC,CAAA;AACrD,OAAA;MACAyG,OAAO,GAAG,IAAI,CAACyB,kBAAkB,CAAC+B,GAAG,CAACjK,MAAM,CAAC,CAAA;AACjD,KAAA;IACA,IAAI,CAACyG,OAAO,EAAE;MACiC;AACvC;AACA;QACArH,MAAM,CAACK,KAAK,CAAC,CAAA,oBAAA,EAAuBiI,cAAc,CAACrD,GAAG,CAAC,CAAA,CAAE,CAAC,CAAA;AAC9D,OAAA;AACA,MAAA,OAAA;AACJ,KAAA;IAC2C;AACvC;AACA;MACAjF,MAAM,CAACS,cAAc,CAAC,CAAA,yBAAA,EAA4B6H,cAAc,CAACrD,GAAG,CAAC,CAAA,CAAE,CAAC,CAAA;AACxEyF,MAAAA,aAAa,CAACI,OAAO,CAAEC,GAAG,IAAK;AAC3B,QAAA,IAAIxE,KAAK,CAACD,OAAO,CAACyE,GAAG,CAAC,EAAE;AACpB/K,UAAAA,MAAM,CAACM,GAAG,CAAC,GAAGyK,GAAG,CAAC,CAAA;AACtB,SAAC,MACI;AACD/K,UAAAA,MAAM,CAACM,GAAG,CAACyK,GAAG,CAAC,CAAA;AACnB,SAAA;AACJ,OAAC,CAAC,CAAA;MACF/K,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,KAAA;AACA;AACA;AACA,IAAA,IAAI0I,eAAe,CAAA;IACnB,IAAI;AACAA,MAAAA,eAAe,GAAG/B,OAAO,CAACE,MAAM,CAAC;QAAEtC,GAAG;QAAEkE,OAAO;QAAED,KAAK;AAAEqB,QAAAA,MAAAA;AAAO,OAAC,CAAC,CAAA;KACpE,CACD,OAAOS,GAAG,EAAE;AACR5B,MAAAA,eAAe,GAAGQ,OAAO,CAACqB,MAAM,CAACD,GAAG,CAAC,CAAA;AACzC,KAAA;AACA;AACA,IAAA,MAAMrD,YAAY,GAAG6C,KAAK,IAAIA,KAAK,CAAC7C,YAAY,CAAA;IAChD,IAAIyB,eAAe,YAAYQ,OAAO,KACjC,IAAI,CAACsB,aAAa,IAAIvD,YAAY,CAAC,EAAE;AACtCyB,MAAAA,eAAe,GAAGA,eAAe,CAAC+B,KAAK,CAAC,MAAOH,GAAG,IAAK;AACnD;AACA,QAAA,IAAIrD,YAAY,EAAE;UAC6B;AACvC;AACA;YACA3H,MAAM,CAACS,cAAc,CAAC,CAAmC,iCAAA,CAAA,GACrD,CAAI6H,CAAAA,EAAAA,cAAc,CAACrD,GAAG,CAAC,CAAA,wCAAA,CAA0C,CAAC,CAAA;AACtEjF,YAAAA,MAAM,CAACQ,KAAK,CAAC,CAAkB,gBAAA,CAAA,EAAEgK,KAAK,CAAC,CAAA;AACvCxK,YAAAA,MAAM,CAACQ,KAAK,CAACwK,GAAG,CAAC,CAAA;YACjBhL,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,WAAA;UACA,IAAI;AACA,YAAA,OAAO,MAAMiH,YAAY,CAACJ,MAAM,CAAC;cAAEtC,GAAG;cAAEkE,OAAO;cAAED,KAAK;AAAEqB,cAAAA,MAAAA;AAAO,aAAC,CAAC,CAAA;WACpE,CACD,OAAOa,QAAQ,EAAE;YACb,IAAIA,QAAQ,YAAYrJ,KAAK,EAAE;AAC3BiJ,cAAAA,GAAG,GAAGI,QAAQ,CAAA;AAClB,aAAA;AACJ,WAAA;AACJ,SAAA;QACA,IAAI,IAAI,CAACF,aAAa,EAAE;UACuB;AACvC;AACA;YACAlL,MAAM,CAACS,cAAc,CAAC,CAAmC,iCAAA,CAAA,GACrD,CAAI6H,CAAAA,EAAAA,cAAc,CAACrD,GAAG,CAAC,CAAA,uCAAA,CAAyC,CAAC,CAAA;AACrEjF,YAAAA,MAAM,CAACQ,KAAK,CAAC,CAAkB,gBAAA,CAAA,EAAEgK,KAAK,CAAC,CAAA;AACvCxK,YAAAA,MAAM,CAACQ,KAAK,CAACwK,GAAG,CAAC,CAAA;YACjBhL,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,WAAA;AACA,UAAA,OAAO,IAAI,CAACwK,aAAa,CAAC3D,MAAM,CAAC;YAAEtC,GAAG;YAAEkE,OAAO;AAAED,YAAAA,KAAAA;AAAM,WAAC,CAAC,CAAA;AAC7D,SAAA;AACA,QAAA,MAAM8B,GAAG,CAAA;AACb,OAAC,CAAC,CAAA;AACN,KAAA;AACA,IAAA,OAAO5B,eAAe,CAAA;AAC1B,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIqB,EAAAA,iBAAiBA,CAAC;IAAExF,GAAG;IAAEqF,UAAU;IAAEnB,OAAO;AAAED,IAAAA,KAAAA;AAAO,GAAC,EAAE;AACpD,IAAA,MAAMH,MAAM,GAAG,IAAI,CAACH,OAAO,CAACiC,GAAG,CAAC1B,OAAO,CAACvI,MAAM,CAAC,IAAI,EAAE,CAAA;AACrD,IAAA,KAAK,MAAM4J,KAAK,IAAIzB,MAAM,EAAE;AACxB,MAAA,IAAIwB,MAAM,CAAA;AACV;AACA;AACA,MAAA,MAAMc,WAAW,GAAGb,KAAK,CAAC/C,KAAK,CAAC;QAAExC,GAAG;QAAEqF,UAAU;QAAEnB,OAAO;AAAED,QAAAA,KAAAA;AAAM,OAAC,CAAC,CAAA;AACpE,MAAA,IAAImC,WAAW,EAAE;QAC8B;AACvC;AACA;UACA,IAAIA,WAAW,YAAYzB,OAAO,EAAE;AAChC5J,YAAAA,MAAM,CAACO,IAAI,CAAC,CAAA,cAAA,EAAiB+H,cAAc,CAACrD,GAAG,CAAC,CAAA,WAAA,CAAa,GACzD,CAAsD,oDAAA,CAAA,GACtD,CAA8D,4DAAA,CAAA,EAAEuF,KAAK,CAAC,CAAA;AAC9E,WAAA;AACJ,SAAA;AACA;AACA;AACAD,QAAAA,MAAM,GAAGc,WAAW,CAAA;AACpB,QAAA,IAAI9E,KAAK,CAACD,OAAO,CAACiE,MAAM,CAAC,IAAIA,MAAM,CAACe,MAAM,KAAK,CAAC,EAAE;AAC9C;AACAf,UAAAA,MAAM,GAAGgB,SAAS,CAAA;AACtB,SAAC,MACI,IAAIF,WAAW,CAACjF,WAAW,KAAK7E,MAAM;AAAI;QAC3CA,MAAM,CAACC,IAAI,CAAC6J,WAAW,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;AACvC;AACAf,UAAAA,MAAM,GAAGgB,SAAS,CAAA;AACtB,SAAC,MACI,IAAI,OAAOF,WAAW,KAAK,SAAS,EAAE;AACvC;AACA;AACA;AACAd,UAAAA,MAAM,GAAGgB,SAAS,CAAA;AACtB,SAAA;AACA;QACA,OAAO;UAAEf,KAAK;AAAED,UAAAA,MAAAA;SAAQ,CAAA;AAC5B,OAAA;AACJ,KAAA;AACA;AACA,IAAA,OAAO,EAAE,CAAA;AACb,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIiB,EAAAA,iBAAiBA,CAACnE,OAAO,EAAEzG,MAAM,GAAGsG,aAAa,EAAE;IAC/C,IAAI,CAAC4B,kBAAkB,CAAC2C,GAAG,CAAC7K,MAAM,EAAEwG,gBAAgB,CAACC,OAAO,CAAC,CAAC,CAAA;AAClE,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,eAAeA,CAACL,OAAO,EAAE;AACrB,IAAA,IAAI,CAAC6D,aAAa,GAAG9D,gBAAgB,CAACC,OAAO,CAAC,CAAA;AAClD,GAAA;AACA;AACJ;AACA;AACA;AACA;EACIqE,aAAaA,CAAClB,KAAK,EAAE;IAC0B;AACvClD,MAAAA,kBAAM,CAACZ,MAAM,CAAC8D,KAAK,EAAE,QAAQ,EAAE;AAC3BrI,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,QAAQ;AACnBC,QAAAA,QAAQ,EAAE,eAAe;AACzBT,QAAAA,SAAS,EAAE,OAAA;AACf,OAAC,CAAC,CAAA;AACF0F,MAAAA,kBAAM,CAACd,SAAS,CAACgE,KAAK,EAAE,OAAO,EAAE;AAC7BrI,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,QAAQ;AACnBC,QAAAA,QAAQ,EAAE,eAAe;AACzBT,QAAAA,SAAS,EAAE,OAAA;AACf,OAAC,CAAC,CAAA;MACF0F,kBAAM,CAACZ,MAAM,CAAC8D,KAAK,CAACnD,OAAO,EAAE,QAAQ,EAAE;AACnClF,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,QAAQ;AACnBC,QAAAA,QAAQ,EAAE,eAAe;AACzBT,QAAAA,SAAS,EAAE,OAAA;AACf,OAAC,CAAC,CAAA;MACF0F,kBAAM,CAACd,SAAS,CAACgE,KAAK,CAACnD,OAAO,EAAE,QAAQ,EAAE;AACtClF,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,QAAQ;AACnBC,QAAAA,QAAQ,EAAE,eAAe;AACzBT,QAAAA,SAAS,EAAE,eAAA;AACf,OAAC,CAAC,CAAA;MACF0F,kBAAM,CAACZ,MAAM,CAAC8D,KAAK,CAAC5J,MAAM,EAAE,QAAQ,EAAE;AAClCuB,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,QAAQ;AACnBC,QAAAA,QAAQ,EAAE,eAAe;AACzBT,QAAAA,SAAS,EAAE,cAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;IACA,IAAI,CAAC,IAAI,CAACgH,OAAO,CAACgC,GAAG,CAACJ,KAAK,CAAC5J,MAAM,CAAC,EAAE;MACjC,IAAI,CAACgI,OAAO,CAAC6C,GAAG,CAACjB,KAAK,CAAC5J,MAAM,EAAE,EAAE,CAAC,CAAA;AACtC,KAAA;AACA;AACA;AACA,IAAA,IAAI,CAACgI,OAAO,CAACiC,GAAG,CAACL,KAAK,CAAC5J,MAAM,CAAC,CAAC+J,IAAI,CAACH,KAAK,CAAC,CAAA;AAC9C,GAAA;AACA;AACJ;AACA;AACA;AACA;EACImB,eAAeA,CAACnB,KAAK,EAAE;IACnB,IAAI,CAAC,IAAI,CAAC5B,OAAO,CAACgC,GAAG,CAACJ,KAAK,CAAC5J,MAAM,CAAC,EAAE;AACjC,MAAA,MAAM,IAAIuF,YAAY,CAAC,4CAA4C,EAAE;QACjEvF,MAAM,EAAE4J,KAAK,CAAC5J,MAAAA;AAClB,OAAC,CAAC,CAAA;AACN,KAAA;AACA,IAAA,MAAMgL,UAAU,GAAG,IAAI,CAAChD,OAAO,CAACiC,GAAG,CAACL,KAAK,CAAC5J,MAAM,CAAC,CAACiL,OAAO,CAACrB,KAAK,CAAC,CAAA;AAChE,IAAA,IAAIoB,UAAU,GAAG,CAAC,CAAC,EAAE;AACjB,MAAA,IAAI,CAAChD,OAAO,CAACiC,GAAG,CAACL,KAAK,CAAC5J,MAAM,CAAC,CAACkL,MAAM,CAACF,UAAU,EAAE,CAAC,CAAC,CAAA;AACxD,KAAC,MACI;AACD,MAAA,MAAM,IAAIzF,YAAY,CAAC,uCAAuC,CAAC,CAAA;AACnE,KAAA;AACJ,GAAA;AACJ;;ACvYA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA,IAAI4F,aAAa,CAAA;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,wBAAwB,GAAGA,MAAM;EAC1C,IAAI,CAACD,aAAa,EAAE;AAChBA,IAAAA,aAAa,GAAG,IAAIpD,MAAM,EAAE,CAAA;AAC5B;IACAoD,aAAa,CAAC/C,gBAAgB,EAAE,CAAA;IAChC+C,aAAa,CAACxC,gBAAgB,EAAE,CAAA;AACpC,GAAA;AACA,EAAA,OAAOwC,aAAa,CAAA;AACxB,CAAC;;ACzBD;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,aAAaA,CAACO,OAAO,EAAE5E,OAAO,EAAEzG,MAAM,EAAE;AAC7C,EAAA,IAAI4J,KAAK,CAAA;AACT,EAAA,IAAI,OAAOyB,OAAO,KAAK,QAAQ,EAAE;IAC7B,MAAMC,UAAU,GAAG,IAAI1D,GAAG,CAACyD,OAAO,EAAE/D,QAAQ,CAACD,IAAI,CAAC,CAAA;IACP;AACvC,MAAA,IAAI,EAAEgE,OAAO,CAAC5B,UAAU,CAAC,GAAG,CAAC,IAAI4B,OAAO,CAAC5B,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;AAC1D,QAAA,MAAM,IAAIlE,YAAY,CAAC,gBAAgB,EAAE;AACrChE,UAAAA,UAAU,EAAE,iBAAiB;AAC7BE,UAAAA,QAAQ,EAAE,eAAe;AACzBT,UAAAA,SAAS,EAAE,SAAA;AACf,SAAC,CAAC,CAAA;AACN,OAAA;AACA;AACA;AACA,MAAA,MAAMuK,YAAY,GAAGF,OAAO,CAAC5B,UAAU,CAAC,MAAM,CAAC,GACzC6B,UAAU,CAACE,QAAQ,GACnBH,OAAO,CAAA;AACb;MACA,MAAMI,SAAS,GAAG,QAAQ,CAAA;AAC1B,MAAA,IAAI,IAAIvE,MAAM,CAAC,CAAA,EAAGuE,SAAS,CAAA,CAAE,CAAC,CAACrE,IAAI,CAACmE,YAAY,CAAC,EAAE;QAC/CnM,MAAM,CAACK,KAAK,CAAC,CAA8D,4DAAA,CAAA,GACvE,cAAcgM,SAAS,CAAA,yCAAA,CAA2C,GAClE,CAAA,4DAAA,CAA8D,CAAC,CAAA;AACvE,OAAA;AACJ,KAAA;IACA,MAAMC,aAAa,GAAGA,CAAC;AAAErH,MAAAA,GAAAA;AAAI,KAAC,KAAK;MACY;AACvC,QAAA,IAAIA,GAAG,CAACmH,QAAQ,KAAKF,UAAU,CAACE,QAAQ,IACpCnH,GAAG,CAACW,MAAM,KAAKsG,UAAU,CAACtG,MAAM,EAAE;AAClC5F,UAAAA,MAAM,CAACK,KAAK,CAAC,CAAG4L,EAAAA,OAAO,+CAA+C,GAClE,CAAA,EAAGhH,GAAG,CAACmD,QAAQ,EAAE,CAAsD,oDAAA,CAAA,GACvE,+BAA+B,CAAC,CAAA;AACxC,SAAA;AACJ,OAAA;AACA,MAAA,OAAOnD,GAAG,CAACgD,IAAI,KAAKiE,UAAU,CAACjE,IAAI,CAAA;KACtC,CAAA;AACD;IACAuC,KAAK,GAAG,IAAIhD,KAAK,CAAC8E,aAAa,EAAEjF,OAAO,EAAEzG,MAAM,CAAC,CAAA;AACrD,GAAC,MACI,IAAIqL,OAAO,YAAYnE,MAAM,EAAE;AAChC;IACA0C,KAAK,GAAG,IAAI5C,WAAW,CAACqE,OAAO,EAAE5E,OAAO,EAAEzG,MAAM,CAAC,CAAA;AACrD,GAAC,MACI,IAAI,OAAOqL,OAAO,KAAK,UAAU,EAAE;AACpC;IACAzB,KAAK,GAAG,IAAIhD,KAAK,CAACyE,OAAO,EAAE5E,OAAO,EAAEzG,MAAM,CAAC,CAAA;AAC/C,GAAC,MACI,IAAIqL,OAAO,YAAYzE,KAAK,EAAE;AAC/BgD,IAAAA,KAAK,GAAGyB,OAAO,CAAA;AACnB,GAAC,MACI;AACD,IAAA,MAAM,IAAI9F,YAAY,CAAC,wBAAwB,EAAE;AAC7ChE,MAAAA,UAAU,EAAE,iBAAiB;AAC7BE,MAAAA,QAAQ,EAAE,eAAe;AACzBT,MAAAA,SAAS,EAAE,SAAA;AACf,KAAC,CAAC,CAAA;AACN,GAAA;AACA,EAAA,MAAMmK,aAAa,GAAGC,wBAAwB,EAAE,CAAA;AAChDD,EAAAA,aAAa,CAACL,aAAa,CAAClB,KAAK,CAAC,CAAA;AAClC,EAAA,OAAOA,KAAK,CAAA;AAChB;;AC3FA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAM+B,iBAAiB,GAAG;AACtBC,EAAAA,eAAe,EAAE,iBAAiB;AAClCC,EAAAA,QAAQ,EAAE,aAAa;AACvBC,EAAAA,MAAM,EAAE,SAAS;AACjBC,EAAAA,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,OAAOC,YAAY,KAAK,WAAW,GAAGA,YAAY,CAACC,KAAK,GAAG,EAAA;AACvE,CAAC,CAAA;AACD,MAAMC,gBAAgB,GAAIrH,SAAS,IAAK;AACpC,EAAA,OAAO,CAAC6G,iBAAiB,CAACG,MAAM,EAAEhH,SAAS,EAAE6G,iBAAiB,CAACK,MAAM,CAAC,CACjEI,MAAM,CAAElL,KAAK,IAAKA,KAAK,IAAIA,KAAK,CAACwJ,MAAM,GAAG,CAAC,CAAC,CAC5ClK,IAAI,CAAC,GAAG,CAAC,CAAA;AAClB,CAAC,CAAA;AACD,MAAM6L,mBAAmB,GAAIC,EAAE,IAAK;EAChC,KAAK,MAAMzL,GAAG,IAAIF,MAAM,CAACC,IAAI,CAAC+K,iBAAiB,CAAC,EAAE;IAC9CW,EAAE,CAACzL,GAAG,CAAC,CAAA;AACX,GAAA;AACJ,CAAC,CAAA;AACM,MAAM0L,UAAU,GAAG;EACtBC,aAAa,EAAGnH,OAAO,IAAK;IACxBgH,mBAAmB,CAAExL,GAAG,IAAK;AACzB,MAAA,IAAI,OAAOwE,OAAO,CAACxE,GAAG,CAAC,KAAK,QAAQ,EAAE;AAClC8K,QAAAA,iBAAiB,CAAC9K,GAAG,CAAC,GAAGwE,OAAO,CAACxE,GAAG,CAAC,CAAA;AACzC,OAAA;AACJ,KAAC,CAAC,CAAA;GACL;EACD4L,sBAAsB,EAAGC,aAAa,IAAK;AACvC,IAAA,OAAOA,aAAa,IAAIP,gBAAgB,CAACR,iBAAiB,CAACC,eAAe,CAAC,CAAA;GAC9E;EACDe,eAAe,EAAGD,aAAa,IAAK;AAChC,IAAA,OAAOA,aAAa,IAAIP,gBAAgB,CAACR,iBAAiB,CAACE,QAAQ,CAAC,CAAA;GACvE;EACDe,SAAS,EAAEA,MAAM;IACb,OAAOjB,iBAAiB,CAACG,MAAM,CAAA;GAClC;EACDe,cAAc,EAAGH,aAAa,IAAK;AAC/B,IAAA,OAAOA,aAAa,IAAIP,gBAAgB,CAACR,iBAAiB,CAACI,OAAO,CAAC,CAAA;GACtE;EACDe,SAAS,EAAEA,MAAM;IACb,OAAOnB,iBAAiB,CAACK,MAAM,CAAA;AACnC,GAAA;AACJ,CAAC;;AChDD;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACO,SAASe,WAAWA,CAACC,OAAO,EAAE;AACjC;AACA,EAAA,KAAKA,OAAO,CAAC1D,IAAI,CAAC,MAAM,EAAG,CAAC,CAAA;AAChC;;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA,MAAM2D,mBAAmB,GAAG,IAAIC,GAAG,EAAE;;ACXrC;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACC,QAAQ,EAAE;EACC;AACvC1G,IAAAA,kBAAM,CAACZ,MAAM,CAACsH,QAAQ,EAAE,UAAU,EAAE;AAChC7L,MAAAA,UAAU,EAAE,cAAc;AAC1BE,MAAAA,QAAQ,EAAE,UAAU;AACpBT,MAAAA,SAAS,EAAE,UAAA;AACf,KAAC,CAAC,CAAA;AACN,GAAA;AACAiM,EAAAA,mBAAmB,CAACI,GAAG,CAACD,QAAQ,CAAC,CAAA;EACU;AACvChO,IAAAA,MAAM,CAACM,GAAG,CAAC,mDAAmD,EAAE0N,QAAQ,CAAC,CAAA;AAC7E,GAAA;AACJ;;;;;;;;;;;;AChCA,MAAME,aAAa,GAAGA,CAACzH,MAAM,EAAE0H,YAAY,KAAKA,YAAY,CAACC,IAAI,CAAEC,CAAC,IAAK5H,MAAM,YAAY4H,CAAC,CAAC,CAAA;AAE7F,IAAIC,iBAAiB,CAAA;AACrB,IAAIC,oBAAoB,CAAA;AACxB;AACA,SAASC,oBAAoBA,GAAG;AAC5B,EAAA,OAAQF,iBAAiB,KACpBA,iBAAiB,GAAG,CACjBG,WAAW,EACXC,cAAc,EACdC,QAAQ,EACRC,SAAS,EACTC,cAAc,CACjB,CAAC,CAAA;AACV,CAAA;AACA;AACA,SAASC,uBAAuBA,GAAG;EAC/B,OAAQP,oBAAoB,KACvBA,oBAAoB,GAAG,CACpBK,SAAS,CAACG,SAAS,CAACC,OAAO,EAC3BJ,SAAS,CAACG,SAAS,CAACE,QAAQ,EAC5BL,SAAS,CAACG,SAAS,CAACG,kBAAkB,CACzC,CAAC,CAAA;AACV,CAAA;AACA,MAAMC,gBAAgB,GAAG,IAAIC,OAAO,EAAE,CAAA;AACtC,MAAMC,kBAAkB,GAAG,IAAID,OAAO,EAAE,CAAA;AACxC,MAAME,wBAAwB,GAAG,IAAIF,OAAO,EAAE,CAAA;AAC9C,MAAMG,cAAc,GAAG,IAAIH,OAAO,EAAE,CAAA;AACpC,MAAMI,qBAAqB,GAAG,IAAIJ,OAAO,EAAE,CAAA;AAC3C,SAASK,gBAAgBA,CAACtG,OAAO,EAAE;EAC/B,MAAMyE,OAAO,GAAG,IAAIhE,OAAO,CAAC,CAAC8F,OAAO,EAAEzE,MAAM,KAAK;IAC7C,MAAM0E,QAAQ,GAAGA,MAAM;AACnBxG,MAAAA,OAAO,CAACyG,mBAAmB,CAAC,SAAS,EAAEC,OAAO,CAAC,CAAA;AAC/C1G,MAAAA,OAAO,CAACyG,mBAAmB,CAAC,OAAO,EAAEpP,KAAK,CAAC,CAAA;KAC9C,CAAA;IACD,MAAMqP,OAAO,GAAGA,MAAM;AAClBH,MAAAA,OAAO,CAACI,IAAI,CAAC3G,OAAO,CAACpB,MAAM,CAAC,CAAC,CAAA;AAC7B4H,MAAAA,QAAQ,EAAE,CAAA;KACb,CAAA;IACD,MAAMnP,KAAK,GAAGA,MAAM;AAChByK,MAAAA,MAAM,CAAC9B,OAAO,CAAC3I,KAAK,CAAC,CAAA;AACrBmP,MAAAA,QAAQ,EAAE,CAAA;KACb,CAAA;AACDxG,IAAAA,OAAO,CAACF,gBAAgB,CAAC,SAAS,EAAE4G,OAAO,CAAC,CAAA;AAC5C1G,IAAAA,OAAO,CAACF,gBAAgB,CAAC,OAAO,EAAEzI,KAAK,CAAC,CAAA;AAC5C,GAAC,CAAC,CAAA;AACFoN,EAAAA,OAAO,CACF1D,IAAI,CAAEpI,KAAK,IAAK;AACjB;AACA;IACA,IAAIA,KAAK,YAAY8M,SAAS,EAAE;AAC5BO,MAAAA,gBAAgB,CAAC1D,GAAG,CAAC3J,KAAK,EAAEqH,OAAO,CAAC,CAAA;AACxC,KAAA;AACA;AACJ,GAAC,CAAC,CACGgC,KAAK,CAAC,MAAM,EAAG,CAAC,CAAA;AACrB;AACA;AACAqE,EAAAA,qBAAqB,CAAC/D,GAAG,CAACmC,OAAO,EAAEzE,OAAO,CAAC,CAAA;AAC3C,EAAA,OAAOyE,OAAO,CAAA;AAClB,CAAA;AACA,SAASmC,8BAA8BA,CAACC,EAAE,EAAE;AACxC;AACA,EAAA,IAAIX,kBAAkB,CAACzE,GAAG,CAACoF,EAAE,CAAC,EAC1B,OAAA;EACJ,MAAMC,IAAI,GAAG,IAAIrG,OAAO,CAAC,CAAC8F,OAAO,EAAEzE,MAAM,KAAK;IAC1C,MAAM0E,QAAQ,GAAGA,MAAM;AACnBK,MAAAA,EAAE,CAACJ,mBAAmB,CAAC,UAAU,EAAEM,QAAQ,CAAC,CAAA;AAC5CF,MAAAA,EAAE,CAACJ,mBAAmB,CAAC,OAAO,EAAEpP,KAAK,CAAC,CAAA;AACtCwP,MAAAA,EAAE,CAACJ,mBAAmB,CAAC,OAAO,EAAEpP,KAAK,CAAC,CAAA;KACzC,CAAA;IACD,MAAM0P,QAAQ,GAAGA,MAAM;AACnBR,MAAAA,OAAO,EAAE,CAAA;AACTC,MAAAA,QAAQ,EAAE,CAAA;KACb,CAAA;IACD,MAAMnP,KAAK,GAAGA,MAAM;AAChByK,MAAAA,MAAM,CAAC+E,EAAE,CAACxP,KAAK,IAAI,IAAI2P,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAA;AAChER,MAAAA,QAAQ,EAAE,CAAA;KACb,CAAA;AACDK,IAAAA,EAAE,CAAC/G,gBAAgB,CAAC,UAAU,EAAEiH,QAAQ,CAAC,CAAA;AACzCF,IAAAA,EAAE,CAAC/G,gBAAgB,CAAC,OAAO,EAAEzI,KAAK,CAAC,CAAA;AACnCwP,IAAAA,EAAE,CAAC/G,gBAAgB,CAAC,OAAO,EAAEzI,KAAK,CAAC,CAAA;AACvC,GAAC,CAAC,CAAA;AACF;AACA6O,EAAAA,kBAAkB,CAAC5D,GAAG,CAACuE,EAAE,EAAEC,IAAI,CAAC,CAAA;AACpC,CAAA;AACA,IAAIG,aAAa,GAAG;AAChBvF,EAAAA,GAAGA,CAACwF,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAE;IACxB,IAAIF,MAAM,YAAYxB,cAAc,EAAE;AAClC;MACA,IAAIyB,IAAI,KAAK,MAAM,EACf,OAAOjB,kBAAkB,CAACxE,GAAG,CAACwF,MAAM,CAAC,CAAA;AACzC;MACA,IAAIC,IAAI,KAAK,kBAAkB,EAAE;QAC7B,OAAOD,MAAM,CAACG,gBAAgB,IAAIlB,wBAAwB,CAACzE,GAAG,CAACwF,MAAM,CAAC,CAAA;AAC1E,OAAA;AACA;MACA,IAAIC,IAAI,KAAK,OAAO,EAAE;AAClB,QAAA,OAAOC,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CAAC,GAC7BjF,SAAS,GACTgF,QAAQ,CAACE,WAAW,CAACF,QAAQ,CAACC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAA;AAC5D,OAAA;AACJ,KAAA;AACA;AACA,IAAA,OAAOV,IAAI,CAACO,MAAM,CAACC,IAAI,CAAC,CAAC,CAAA;GAC5B;AACD7E,EAAAA,GAAGA,CAAC4E,MAAM,EAAEC,IAAI,EAAExO,KAAK,EAAE;AACrBuO,IAAAA,MAAM,CAACC,IAAI,CAAC,GAAGxO,KAAK,CAAA;AACpB,IAAA,OAAO,IAAI,CAAA;GACd;AACD8I,EAAAA,GAAGA,CAACyF,MAAM,EAAEC,IAAI,EAAE;AACd,IAAA,IAAID,MAAM,YAAYxB,cAAc,KAC/ByB,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,CAAC,EAAE;AACvC,MAAA,OAAO,IAAI,CAAA;AACf,KAAA;IACA,OAAOA,IAAI,IAAID,MAAM,CAAA;AACzB,GAAA;AACJ,CAAC,CAAA;AACD,SAASK,YAAYA,CAAC1C,QAAQ,EAAE;AAC5BoC,EAAAA,aAAa,GAAGpC,QAAQ,CAACoC,aAAa,CAAC,CAAA;AAC3C,CAAA;AACA,SAASO,YAAYA,CAACC,IAAI,EAAE;AACxB;AACA;AACA;AACA,EAAA,IAAIA,IAAI,KAAKnC,WAAW,CAACM,SAAS,CAAC8B,WAAW,IAC1C,EAAE,kBAAkB,IAAIhC,cAAc,CAACE,SAAS,CAAC,EAAE;AACnD,IAAA,OAAO,UAAU+B,UAAU,EAAE,GAAGjQ,IAAI,EAAE;AAClC,MAAA,MAAMmP,EAAE,GAAGY,IAAI,CAACG,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC,EAAEF,UAAU,EAAE,GAAGjQ,IAAI,CAAC,CAAA;AACvDyO,MAAAA,wBAAwB,CAAC7D,GAAG,CAACuE,EAAE,EAAEc,UAAU,CAACG,IAAI,GAAGH,UAAU,CAACG,IAAI,EAAE,GAAG,CAACH,UAAU,CAAC,CAAC,CAAA;MACpF,OAAOhB,IAAI,CAACE,EAAE,CAAC,CAAA;KAClB,CAAA;AACL,GAAA;AACA;AACA;AACA;AACA;AACA;EACA,IAAIlB,uBAAuB,EAAE,CAAChI,QAAQ,CAAC8J,IAAI,CAAC,EAAE;IAC1C,OAAO,UAAU,GAAG/P,IAAI,EAAE;AACtB;AACA;MACA+P,IAAI,CAACM,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC,EAAEnQ,IAAI,CAAC,CAAA;MAC9B,OAAOiP,IAAI,CAACX,gBAAgB,CAACtE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;KAC1C,CAAA;AACL,GAAA;EACA,OAAO,UAAU,GAAGhK,IAAI,EAAE;AACtB;AACA;AACA,IAAA,OAAOiP,IAAI,CAACc,IAAI,CAACM,KAAK,CAACF,MAAM,CAAC,IAAI,CAAC,EAAEnQ,IAAI,CAAC,CAAC,CAAA;GAC9C,CAAA;AACL,CAAA;AACA,SAASsQ,sBAAsBA,CAACrP,KAAK,EAAE;EACnC,IAAI,OAAOA,KAAK,KAAK,UAAU,EAC3B,OAAO6O,YAAY,CAAC7O,KAAK,CAAC,CAAA;AAC9B;AACA;AACA,EAAA,IAAIA,KAAK,YAAY+M,cAAc,EAC/BkB,8BAA8B,CAACjO,KAAK,CAAC,CAAA;AACzC,EAAA,IAAIoM,aAAa,CAACpM,KAAK,EAAE0M,oBAAoB,EAAE,CAAC,EAC5C,OAAO,IAAI4C,KAAK,CAACtP,KAAK,EAAEsO,aAAa,CAAC,CAAA;AAC1C;AACA,EAAA,OAAOtO,KAAK,CAAA;AAChB,CAAA;AACA,SAASgO,IAAIA,CAAChO,KAAK,EAAE;AACjB;AACA;EACA,IAAIA,KAAK,YAAYuP,UAAU,EAC3B,OAAO5B,gBAAgB,CAAC3N,KAAK,CAAC,CAAA;AAClC;AACA;AACA,EAAA,IAAIyN,cAAc,CAAC3E,GAAG,CAAC9I,KAAK,CAAC,EACzB,OAAOyN,cAAc,CAAC1E,GAAG,CAAC/I,KAAK,CAAC,CAAA;AACpC,EAAA,MAAMwP,QAAQ,GAAGH,sBAAsB,CAACrP,KAAK,CAAC,CAAA;AAC9C;AACA;EACA,IAAIwP,QAAQ,KAAKxP,KAAK,EAAE;AACpByN,IAAAA,cAAc,CAAC9D,GAAG,CAAC3J,KAAK,EAAEwP,QAAQ,CAAC,CAAA;AACnC9B,IAAAA,qBAAqB,CAAC/D,GAAG,CAAC6F,QAAQ,EAAExP,KAAK,CAAC,CAAA;AAC9C,GAAA;AACA,EAAA,OAAOwP,QAAQ,CAAA;AACnB,CAAA;AACA,MAAMN,MAAM,GAAIlP,KAAK,IAAK0N,qBAAqB,CAAC3E,GAAG,CAAC/I,KAAK,CAAC;;ACnL1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyP,MAAMA,CAAC7N,IAAI,EAAE8N,OAAO,EAAE;EAAEC,OAAO;EAAEC,OAAO;EAAEC,QAAQ;AAAEC,EAAAA,UAAAA;AAAW,CAAC,GAAG,EAAE,EAAE;EAC5E,MAAMzI,OAAO,GAAG0I,SAAS,CAACC,IAAI,CAACpO,IAAI,EAAE8N,OAAO,CAAC,CAAA;AAC7C,EAAA,MAAMO,WAAW,GAAGjC,IAAI,CAAC3G,OAAO,CAAC,CAAA;AACjC,EAAA,IAAIuI,OAAO,EAAE;AACTvI,IAAAA,OAAO,CAACF,gBAAgB,CAAC,eAAe,EAAGC,KAAK,IAAK;MACjDwI,OAAO,CAAC5B,IAAI,CAAC3G,OAAO,CAACpB,MAAM,CAAC,EAAEmB,KAAK,CAAC8I,UAAU,EAAE9I,KAAK,CAAC+I,UAAU,EAAEnC,IAAI,CAAC3G,OAAO,CAAC0H,WAAW,CAAC,EAAE3H,KAAK,CAAC,CAAA;AACvG,KAAC,CAAC,CAAA;AACN,GAAA;AACA,EAAA,IAAIuI,OAAO,EAAE;AACTtI,IAAAA,OAAO,CAACF,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAKuI,OAAO;AACtD;IACAvI,KAAK,CAAC8I,UAAU,EAAE9I,KAAK,CAAC+I,UAAU,EAAE/I,KAAK,CAAC,CAAC,CAAA;AAC/C,GAAA;AACA6I,EAAAA,WAAW,CACN7H,IAAI,CAAEgI,EAAE,IAAK;AACd,IAAA,IAAIN,UAAU,EACVM,EAAE,CAACjJ,gBAAgB,CAAC,OAAO,EAAE,MAAM2I,UAAU,EAAE,CAAC,CAAA;AACpD,IAAA,IAAID,QAAQ,EAAE;AACVO,MAAAA,EAAE,CAACjJ,gBAAgB,CAAC,eAAe,EAAGC,KAAK,IAAKyI,QAAQ,CAACzI,KAAK,CAAC8I,UAAU,EAAE9I,KAAK,CAAC+I,UAAU,EAAE/I,KAAK,CAAC,CAAC,CAAA;AACxG,KAAA;AACJ,GAAC,CAAC,CACGiC,KAAK,CAAC,MAAM,EAAG,CAAC,CAAA;AACrB,EAAA,OAAO4G,WAAW,CAAA;AACtB,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,QAAQA,CAACzO,IAAI,EAAE;AAAE+N,EAAAA,OAAAA;AAAQ,CAAC,GAAG,EAAE,EAAE;AACtC,EAAA,MAAMtI,OAAO,GAAG0I,SAAS,CAACO,cAAc,CAAC1O,IAAI,CAAC,CAAA;AAC9C,EAAA,IAAI+N,OAAO,EAAE;AACTtI,IAAAA,OAAO,CAACF,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAKuI,OAAO;AACtD;AACAvI,IAAAA,KAAK,CAAC8I,UAAU,EAAE9I,KAAK,CAAC,CAAC,CAAA;AAC7B,GAAA;EACA,OAAO4G,IAAI,CAAC3G,OAAO,CAAC,CAACe,IAAI,CAAC,MAAMqB,SAAS,CAAC,CAAA;AAC9C,CAAA;AAEA,MAAM8G,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,CAAA;AACtE,MAAMC,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;AACtD,MAAMC,aAAa,GAAG,IAAI1J,GAAG,EAAE,CAAA;AAC/B,SAAS2J,SAASA,CAACnC,MAAM,EAAEC,IAAI,EAAE;AAC7B,EAAA,IAAI,EAAED,MAAM,YAAY5B,WAAW,IAC/B,EAAE6B,IAAI,IAAID,MAAM,CAAC,IACjB,OAAOC,IAAI,KAAK,QAAQ,CAAC,EAAE;AAC3B,IAAA,OAAA;AACJ,GAAA;AACA,EAAA,IAAIiC,aAAa,CAAC1H,GAAG,CAACyF,IAAI,CAAC,EACvB,OAAOiC,aAAa,CAAC1H,GAAG,CAACyF,IAAI,CAAC,CAAA;EAClC,MAAMmC,cAAc,GAAGnC,IAAI,CAAC5H,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAA;AACrD,EAAA,MAAMgK,QAAQ,GAAGpC,IAAI,KAAKmC,cAAc,CAAA;AACxC,EAAA,MAAME,OAAO,GAAGL,YAAY,CAACxL,QAAQ,CAAC2L,cAAc,CAAC,CAAA;AACrD,EAAA;AACA;EACA,EAAEA,cAAc,IAAI,CAACC,QAAQ,GAAG/D,QAAQ,GAAGD,cAAc,EAAEK,SAAS,CAAC,IACjE,EAAE4D,OAAO,IAAIN,WAAW,CAACvL,QAAQ,CAAC2L,cAAc,CAAC,CAAC,EAAE;AACpD,IAAA,OAAA;AACJ,GAAA;EACA,MAAM7R,MAAM,GAAG,gBAAgBgS,SAAS,EAAE,GAAG/R,IAAI,EAAE;AAC/C;AACA,IAAA,MAAMmP,EAAE,GAAG,IAAI,CAACa,WAAW,CAAC+B,SAAS,EAAED,OAAO,GAAG,WAAW,GAAG,UAAU,CAAC,CAAA;AAC1E,IAAA,IAAItC,MAAM,GAAGL,EAAE,CAAC6C,KAAK,CAAA;AACrB,IAAA,IAAIH,QAAQ,EACRrC,MAAM,GAAGA,MAAM,CAAClI,KAAK,CAACtH,IAAI,CAACiS,KAAK,EAAE,CAAC,CAAA;AACvC;AACA;AACA;AACA;AACA;IACA,OAAO,CAAC,MAAMlJ,OAAO,CAACC,GAAG,CAAC,CACtBwG,MAAM,CAACoC,cAAc,CAAC,CAAC,GAAG5R,IAAI,CAAC,EAC/B8R,OAAO,IAAI3C,EAAE,CAACC,IAAI,CACrB,CAAC,EAAE,CAAC,CAAC,CAAA;GACT,CAAA;AACDsC,EAAAA,aAAa,CAAC9G,GAAG,CAAC6E,IAAI,EAAE1P,MAAM,CAAC,CAAA;AAC/B,EAAA,OAAOA,MAAM,CAAA;AACjB,CAAA;AACA8P,YAAY,CAAEqC,QAAQ,IAAAC,QAAA,KACfD,QAAQ,EAAA;EACXlI,GAAG,EAAEA,CAACwF,MAAM,EAAEC,IAAI,EAAEC,QAAQ,KAAKiC,SAAS,CAACnC,MAAM,EAAEC,IAAI,CAAC,IAAIyC,QAAQ,CAAClI,GAAG,CAACwF,MAAM,EAAEC,IAAI,EAAEC,QAAQ,CAAC;EAChG3F,GAAG,EAAEA,CAACyF,MAAM,EAAEC,IAAI,KAAK,CAAC,CAACkC,SAAS,CAACnC,MAAM,EAAEC,IAAI,CAAC,IAAIyC,QAAQ,CAACnI,GAAG,CAACyF,MAAM,EAAEC,IAAI,CAAA;AAAC,CAAA,CAChF,CAAC;;AC3FH;AACA,IAAI;AACAzQ,EAAAA,IAAI,CAAC,0BAA0B,CAAC,IAAIC,CAAC,EAAE,CAAA;AAC3C,CAAC,CACD,OAAOC,CAAC,EAAE;;ACLV;AACA;AACA;AACA;AACA;AACA;AACA;AAGA,MAAMkT,SAAO,GAAG,oBAAoB,CAAA;AACpC,MAAMC,kBAAkB,GAAG,eAAe,CAAA;AAC1C,MAAMC,YAAY,GAAIC,eAAe,IAAK;EACtC,MAAMnO,GAAG,GAAG,IAAIuD,GAAG,CAAC4K,eAAe,EAAElL,QAAQ,CAACD,IAAI,CAAC,CAAA;EACnDhD,GAAG,CAACoO,IAAI,GAAG,EAAE,CAAA;EACb,OAAOpO,GAAG,CAACgD,IAAI,CAAA;AACnB,CAAC,CAAA;AACD;AACA;AACA;AACA;AACA;AACA,MAAMqL,oBAAoB,CAAC;AACvB;AACJ;AACA;AACA;AACA;AACA;EACIlN,WAAWA,CAACV,SAAS,EAAE;IACnB,IAAI,CAAC6N,GAAG,GAAG,IAAI,CAAA;IACf,IAAI,CAACC,UAAU,GAAG9N,SAAS,CAAA;AAC/B,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+N,UAAUA,CAACvB,EAAE,EAAE;AACX;AACA;AACA;AACA;AACA,IAAA,MAAMwB,QAAQ,GAAGxB,EAAE,CAACyB,iBAAiB,CAACT,kBAAkB,EAAE;AAAEU,MAAAA,OAAO,EAAE,IAAA;AAAK,KAAC,CAAC,CAAA;AAC5E;AACA;AACA;AACAF,IAAAA,QAAQ,CAACG,WAAW,CAAC,WAAW,EAAE,WAAW,EAAE;AAAEC,MAAAA,MAAM,EAAE,KAAA;AAAM,KAAC,CAAC,CAAA;AACjEJ,IAAAA,QAAQ,CAACG,WAAW,CAAC,WAAW,EAAE,WAAW,EAAE;AAAEC,MAAAA,MAAM,EAAE,KAAA;AAAM,KAAC,CAAC,CAAA;AACrE,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,yBAAyBA,CAAC7B,EAAE,EAAE;AAC1B,IAAA,IAAI,CAACuB,UAAU,CAACvB,EAAE,CAAC,CAAA;IACnB,IAAI,IAAI,CAACsB,UAAU,EAAE;AACjB,MAAA,KAAKrB,QAAQ,CAAC,IAAI,CAACqB,UAAU,CAAC,CAAA;AAClC,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACI,EAAA,MAAMQ,YAAYA,CAAC/O,GAAG,EAAEgP,SAAS,EAAE;AAC/BhP,IAAAA,GAAG,GAAGkO,YAAY,CAAClO,GAAG,CAAC,CAAA;AACvB,IAAA,MAAMlC,KAAK,GAAG;MACVkC,GAAG;MACHgP,SAAS;MACTvO,SAAS,EAAE,IAAI,CAAC8N,UAAU;AAC1B;AACA;AACA;AACAU,MAAAA,EAAE,EAAE,IAAI,CAACC,MAAM,CAAClP,GAAG,CAAA;KACtB,CAAA;AACD,IAAA,MAAMiN,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;IAC7B,MAAMpE,EAAE,GAAGkC,EAAE,CAACrB,WAAW,CAACqC,kBAAkB,EAAE,WAAW,EAAE;AACvDmB,MAAAA,UAAU,EAAE,SAAA;AAChB,KAAC,CAAC,CAAA;AACF,IAAA,MAAMrE,EAAE,CAAC6C,KAAK,CAACyB,GAAG,CAACvR,KAAK,CAAC,CAAA;IACzB,MAAMiN,EAAE,CAACC,IAAI,CAAA;AACjB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMsE,YAAYA,CAACtP,GAAG,EAAE;AACpB,IAAA,MAAMiN,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;AAC7B,IAAA,MAAMrR,KAAK,GAAG,MAAMmP,EAAE,CAACrH,GAAG,CAACqI,kBAAkB,EAAE,IAAI,CAACiB,MAAM,CAAClP,GAAG,CAAC,CAAC,CAAA;AAChE,IAAA,OAAOlC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACkR,SAAS,CAAA;AACxE,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAMO,aAAaA,CAACC,YAAY,EAAEC,QAAQ,EAAE;AACxC,IAAA,MAAMxC,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;IAC7B,IAAIO,MAAM,GAAG,MAAMzC,EAAE,CAChBrB,WAAW,CAACqC,kBAAkB,CAAC,CAC/BL,KAAK,CAAC1K,KAAK,CAAC,WAAW,CAAC,CACxByM,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;IAC7B,MAAMC,eAAe,GAAG,EAAE,CAAA;IAC1B,IAAIC,sBAAsB,GAAG,CAAC,CAAA;AAC9B,IAAA,OAAOH,MAAM,EAAE;AACX,MAAA,MAAM5M,MAAM,GAAG4M,MAAM,CAAC7S,KAAK,CAAA;AAC3B;AACA;AACA,MAAA,IAAIiG,MAAM,CAACrC,SAAS,KAAK,IAAI,CAAC8N,UAAU,EAAE;AACtC;AACA;AACA,QAAA,IAAKiB,YAAY,IAAI1M,MAAM,CAACkM,SAAS,GAAGQ,YAAY,IAC/CC,QAAQ,IAAII,sBAAsB,IAAIJ,QAAS,EAAE;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAG,UAAAA,eAAe,CAAClK,IAAI,CAACgK,MAAM,CAAC7S,KAAK,CAAC,CAAA;AACtC,SAAC,MACI;AACDgT,UAAAA,sBAAsB,EAAE,CAAA;AAC5B,SAAA;AACJ,OAAA;AACAH,MAAAA,MAAM,GAAG,MAAMA,MAAM,CAAC1F,QAAQ,EAAE,CAAA;AACpC,KAAA;AACA;AACA;AACA;AACA;IACA,MAAM8F,WAAW,GAAG,EAAE,CAAA;AACtB,IAAA,KAAK,MAAMhS,KAAK,IAAI8R,eAAe,EAAE;MACjC,MAAM3C,EAAE,CAAC8C,MAAM,CAAC9B,kBAAkB,EAAEnQ,KAAK,CAACmR,EAAE,CAAC,CAAA;AAC7Ca,MAAAA,WAAW,CAACpK,IAAI,CAAC5H,KAAK,CAACkC,GAAG,CAAC,CAAA;AAC/B,KAAA;AACA,IAAA,OAAO8P,WAAW,CAAA;AACtB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIZ,MAAMA,CAAClP,GAAG,EAAE;AACR;AACA;AACA;IACA,OAAO,IAAI,CAACuO,UAAU,GAAG,GAAG,GAAGL,YAAY,CAAClO,GAAG,CAAC,CAAA;AACpD,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI,MAAMmP,KAAKA,GAAG;AACV,IAAA,IAAI,CAAC,IAAI,CAACb,GAAG,EAAE;MACX,IAAI,CAACA,GAAG,GAAG,MAAMhC,MAAM,CAAC0B,SAAO,EAAE,CAAC,EAAE;AAChCvB,QAAAA,OAAO,EAAE,IAAI,CAACqC,yBAAyB,CAACkB,IAAI,CAAC,IAAI,CAAA;AACrD,OAAC,CAAC,CAAA;AACN,KAAA;IACA,OAAO,IAAI,CAAC1B,GAAG,CAAA;AACnB,GAAA;AACJ;;ACvLA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2B,eAAe,CAAC;AAClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI9O,EAAAA,WAAWA,CAACV,SAAS,EAAEyP,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAACC,UAAU,GAAG,KAAK,CAAA;IACvB,IAAI,CAACC,eAAe,GAAG,KAAK,CAAA;IACe;AACvC/N,MAAAA,kBAAM,CAACZ,MAAM,CAAChB,SAAS,EAAE,QAAQ,EAAE;AAC/BvD,QAAAA,UAAU,EAAE,oBAAoB;AAChCC,QAAAA,SAAS,EAAE,iBAAiB;AAC5BC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,WAAA;AACf,OAAC,CAAC,CAAA;MACF,IAAI,EAAEuT,MAAM,CAACG,UAAU,IAAIH,MAAM,CAACI,aAAa,CAAC,EAAE;AAC9C,QAAA,MAAM,IAAIpP,YAAY,CAAC,6BAA6B,EAAE;AAClDhE,UAAAA,UAAU,EAAE,oBAAoB;AAChCC,UAAAA,SAAS,EAAE,iBAAiB;AAC5BC,UAAAA,QAAQ,EAAE,aAAA;AACd,SAAC,CAAC,CAAA;AACN,OAAA;MACA,IAAI8S,MAAM,CAACG,UAAU,EAAE;QACnBhO,kBAAM,CAACZ,MAAM,CAACyO,MAAM,CAACG,UAAU,EAAE,QAAQ,EAAE;AACvCnT,UAAAA,UAAU,EAAE,oBAAoB;AAChCC,UAAAA,SAAS,EAAE,iBAAiB;AAC5BC,UAAAA,QAAQ,EAAE,aAAa;AACvBT,UAAAA,SAAS,EAAE,mBAAA;AACf,SAAC,CAAC,CAAA;AACN,OAAA;MACA,IAAIuT,MAAM,CAACI,aAAa,EAAE;QACtBjO,kBAAM,CAACZ,MAAM,CAACyO,MAAM,CAACI,aAAa,EAAE,QAAQ,EAAE;AAC1CpT,UAAAA,UAAU,EAAE,oBAAoB;AAChCC,UAAAA,SAAS,EAAE,iBAAiB;AAC5BC,UAAAA,QAAQ,EAAE,aAAa;AACvBT,UAAAA,SAAS,EAAE,sBAAA;AACf,SAAC,CAAC,CAAA;AACN,OAAA;AACJ,KAAA;AACA,IAAA,IAAI,CAAC4T,WAAW,GAAGL,MAAM,CAACG,UAAU,CAAA;AACpC,IAAA,IAAI,CAACG,cAAc,GAAGN,MAAM,CAACI,aAAa,CAAA;AAC1C,IAAA,IAAI,CAACG,aAAa,GAAGP,MAAM,CAACQ,YAAY,CAAA;IACxC,IAAI,CAACnC,UAAU,GAAG9N,SAAS,CAAA;AAC3B,IAAA,IAAI,CAACkQ,eAAe,GAAG,IAAItC,oBAAoB,CAAC5N,SAAS,CAAC,CAAA;AAC9D,GAAA;AACA;AACJ;AACA;EACI,MAAM8O,aAAaA,GAAG;IAClB,IAAI,IAAI,CAACY,UAAU,EAAE;MACjB,IAAI,CAACC,eAAe,GAAG,IAAI,CAAA;AAC3B,MAAA,OAAA;AACJ,KAAA;IACA,IAAI,CAACD,UAAU,GAAG,IAAI,CAAA;AACtB,IAAA,MAAMX,YAAY,GAAG,IAAI,CAACgB,cAAc,GAClCI,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACL,cAAc,GAAG,IAAI,GACvC,CAAC,CAAA;AACP,IAAA,MAAMM,WAAW,GAAG,MAAM,IAAI,CAACH,eAAe,CAACpB,aAAa,CAACC,YAAY,EAAE,IAAI,CAACe,WAAW,CAAC,CAAA;AAC5F;AACA,IAAA,MAAMQ,KAAK,GAAG,MAAMnW,IAAI,CAACoW,MAAM,CAACnE,IAAI,CAAC,IAAI,CAAC0B,UAAU,CAAC,CAAA;AACrD,IAAA,KAAK,MAAMvO,GAAG,IAAI8Q,WAAW,EAAE;MAC3B,MAAMC,KAAK,CAAChB,MAAM,CAAC/P,GAAG,EAAE,IAAI,CAACyQ,aAAa,CAAC,CAAA;AAC/C,KAAA;IAC2C;AACvC,MAAA,IAAIK,WAAW,CAACzK,MAAM,GAAG,CAAC,EAAE;AACxBtL,QAAAA,MAAM,CAACS,cAAc,CAAC,CAAWsV,QAAAA,EAAAA,WAAW,CAACzK,MAAM,CAAA,CAAA,CAAG,GAClD,CAAA,EAAGyK,WAAW,CAACzK,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS,CAAe,aAAA,CAAA,GAChE,GAAGyK,WAAW,CAACzK,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,MAAM,YAAY,GACvD,CAAA,CAAA,EAAI,IAAI,CAACkI,UAAU,UAAU,CAAC,CAAA;AAClCxT,QAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,sBAAA,EAAyByV,WAAW,CAACzK,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,GAAG,CAAC,CAAA;AACjFyK,QAAAA,WAAW,CAACjL,OAAO,CAAE7F,GAAG,IAAKjF,MAAM,CAACM,GAAG,CAAC,CAAA,IAAA,EAAO2E,GAAG,CAAA,CAAE,CAAC,CAAC,CAAA;QACtDjF,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,OAAC,MACI;AACDV,QAAAA,MAAM,CAACK,KAAK,CAAC,CAAA,oDAAA,CAAsD,CAAC,CAAA;AACxE,OAAA;AACJ,KAAA;IACA,IAAI,CAAC+U,UAAU,GAAG,KAAK,CAAA;IACvB,IAAI,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,GAAG,KAAK,CAAA;AAC5B1H,MAAAA,WAAW,CAAC,IAAI,CAAC6G,aAAa,EAAE,CAAC,CAAA;AACrC,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAM0B,eAAeA,CAACjR,GAAG,EAAE;IACoB;AACvCqC,MAAAA,kBAAM,CAACZ,MAAM,CAACzB,GAAG,EAAE,QAAQ,EAAE;AACzB9C,QAAAA,UAAU,EAAE,oBAAoB;AAChCC,QAAAA,SAAS,EAAE,iBAAiB;AAC5BC,QAAAA,QAAQ,EAAE,iBAAiB;AAC3BT,QAAAA,SAAS,EAAE,KAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;AACA,IAAA,MAAM,IAAI,CAACgU,eAAe,CAAC5B,YAAY,CAAC/O,GAAG,EAAE4Q,IAAI,CAACC,GAAG,EAAE,CAAC,CAAA;AAC5D,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMK,YAAYA,CAAClR,GAAG,EAAE;AACpB,IAAA,IAAI,CAAC,IAAI,CAACwQ,cAAc,EAAE;MACqB;AACvC,QAAA,MAAM,IAAItP,YAAY,CAAC,CAAA,4BAAA,CAA8B,EAAE;AACnDtC,UAAAA,UAAU,EAAE,cAAc;AAC1BjC,UAAAA,SAAS,EAAE,eAAA;AACf,SAAC,CAAC,CAAA;AACN,OAAA;AAEJ,KAAC,MACI;MACD,MAAMqS,SAAS,GAAG,MAAM,IAAI,CAAC2B,eAAe,CAACrB,YAAY,CAACtP,GAAG,CAAC,CAAA;AAC9D,MAAA,MAAMmR,eAAe,GAAGP,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACL,cAAc,GAAG,IAAI,CAAA;MAC/D,OAAOxB,SAAS,KAAK1I,SAAS,GAAG0I,SAAS,GAAGmC,eAAe,GAAG,IAAI,CAAA;AACvE,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;EACI,MAAMpB,MAAMA,GAAG;AACX;AACA;IACA,IAAI,CAACK,eAAe,GAAG,KAAK,CAAA;IAC5B,MAAM,IAAI,CAACO,eAAe,CAACpB,aAAa,CAAC6B,QAAQ,CAAC,CAAC;AACvD,GAAA;AACJ;;ACvKA;AACA;AACA;AACA;AACA;AACA;AACA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;AACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIlQ,EAAAA,WAAWA,CAAC+O,MAAM,GAAG,EAAE,EAAE;AACrB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACoB,wBAAwB,GAAG,OAAO;MAAErN,KAAK;MAAEC,OAAO;MAAEzD,SAAS;AAAE8Q,MAAAA,cAAAA;AAAgB,KAAC,KAAK;MACtF,IAAI,CAACA,cAAc,EAAE;AACjB,QAAA,OAAO,IAAI,CAAA;AACf,OAAA;AACA,MAAA,MAAMC,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAACF,cAAc,CAAC,CAAA;AACzD;AACA;AACA,MAAA,MAAMG,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAClR,SAAS,CAAC,CAAA;AAC3DiI,MAAAA,WAAW,CAACgJ,eAAe,CAACnC,aAAa,EAAE,CAAC,CAAA;AAC5C;AACA;MACA,MAAMqC,mBAAmB,GAAGF,eAAe,CAACT,eAAe,CAAC/M,OAAO,CAAClE,GAAG,CAAC,CAAA;AACxE,MAAA,IAAIiE,KAAK,EAAE;QACP,IAAI;AACAA,UAAAA,KAAK,CAACc,SAAS,CAAC6M,mBAAmB,CAAC,CAAA;SACvC,CACD,OAAOrW,KAAK,EAAE;UACiC;AACvC;YACA,IAAI,SAAS,IAAI0I,KAAK,EAAE;AACpBlJ,cAAAA,MAAM,CAACO,IAAI,CAAC,CAAmD,iDAAA,CAAA,GAC3D,2BAA2B,GAC3B,CAAA,CAAA,EAAI+H,cAAc,CAACY,KAAK,CAACC,OAAO,CAAClE,GAAG,CAAC,IAAI,CAAC,CAAA;AAClD,aAAA;AACJ,WAAA;AACJ,SAAA;AACJ,OAAA;AACA,MAAA,OAAOwR,OAAO,GAAGD,cAAc,GAAG,IAAI,CAAA;KACzC,CAAA;AACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACM,cAAc,GAAG,OAAO;MAAEpR,SAAS;AAAEyD,MAAAA,OAAAA;AAAS,KAAC,KAAK;MACV;AACvC7B,QAAAA,kBAAM,CAACZ,MAAM,CAAChB,SAAS,EAAE,QAAQ,EAAE;AAC/BvD,UAAAA,UAAU,EAAE,oBAAoB;AAChCC,UAAAA,SAAS,EAAE,QAAQ;AACnBC,UAAAA,QAAQ,EAAE,gBAAgB;AAC1BT,UAAAA,SAAS,EAAE,WAAA;AACf,SAAC,CAAC,CAAA;AACF0F,QAAAA,kBAAM,CAACX,UAAU,CAACwC,OAAO,EAAEY,OAAO,EAAE;AAChC5H,UAAAA,UAAU,EAAE,oBAAoB;AAChCC,UAAAA,SAAS,EAAE,QAAQ;AACnBC,UAAAA,QAAQ,EAAE,gBAAgB;AAC1BT,UAAAA,SAAS,EAAE,SAAA;AACf,SAAC,CAAC,CAAA;AACN,OAAA;AACA,MAAA,MAAM+U,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAClR,SAAS,CAAC,CAAA;AAC3D,MAAA,MAAMiR,eAAe,CAACT,eAAe,CAAC/M,OAAO,CAAClE,GAAG,CAAC,CAAA;AAClD,MAAA,MAAM0R,eAAe,CAACnC,aAAa,EAAE,CAAA;KACxC,CAAA;IAC0C;MACvC,IAAI,EAAEW,MAAM,CAACG,UAAU,IAAIH,MAAM,CAACI,aAAa,CAAC,EAAE;AAC9C,QAAA,MAAM,IAAIpP,YAAY,CAAC,6BAA6B,EAAE;AAClDhE,UAAAA,UAAU,EAAE,oBAAoB;AAChCC,UAAAA,SAAS,EAAE,QAAQ;AACnBC,UAAAA,QAAQ,EAAE,aAAA;AACd,SAAC,CAAC,CAAA;AACN,OAAA;MACA,IAAI8S,MAAM,CAACG,UAAU,EAAE;QACnBhO,kBAAM,CAACZ,MAAM,CAACyO,MAAM,CAACG,UAAU,EAAE,QAAQ,EAAE;AACvCnT,UAAAA,UAAU,EAAE,oBAAoB;AAChCC,UAAAA,SAAS,EAAE,QAAQ;AACnBC,UAAAA,QAAQ,EAAE,aAAa;AACvBT,UAAAA,SAAS,EAAE,mBAAA;AACf,SAAC,CAAC,CAAA;AACN,OAAA;MACA,IAAIuT,MAAM,CAACI,aAAa,EAAE;QACtBjO,kBAAM,CAACZ,MAAM,CAACyO,MAAM,CAACI,aAAa,EAAE,QAAQ,EAAE;AAC1CpT,UAAAA,UAAU,EAAE,oBAAoB;AAChCC,UAAAA,SAAS,EAAE,QAAQ;AACnBC,UAAAA,QAAQ,EAAE,aAAa;AACvBT,UAAAA,SAAS,EAAE,sBAAA;AACf,SAAC,CAAC,CAAA;AACN,OAAA;AACJ,KAAA;IACA,IAAI,CAACmV,OAAO,GAAG5B,MAAM,CAAA;AACrB,IAAA,IAAI,CAACM,cAAc,GAAGN,MAAM,CAACI,aAAa,CAAA;AAC1C,IAAA,IAAI,CAACyB,iBAAiB,GAAG,IAAInO,GAAG,EAAE,CAAA;IAClC,IAAIsM,MAAM,CAAC8B,iBAAiB,EAAE;AAC1BlJ,MAAAA,0BAA0B,CAAC,MAAM,IAAI,CAACmJ,sBAAsB,EAAE,CAAC,CAAA;AACnE,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,mBAAmBA,CAAClR,SAAS,EAAE;AAC3B,IAAA,IAAIA,SAAS,KAAKyH,UAAU,CAACM,cAAc,EAAE,EAAE;AAC3C,MAAA,MAAM,IAAItH,YAAY,CAAC,2BAA2B,CAAC,CAAA;AACvD,KAAA;IACA,IAAIwQ,eAAe,GAAG,IAAI,CAACK,iBAAiB,CAACnM,GAAG,CAACnF,SAAS,CAAC,CAAA;IAC3D,IAAI,CAACiR,eAAe,EAAE;MAClBA,eAAe,GAAG,IAAIzB,eAAe,CAACxP,SAAS,EAAE,IAAI,CAACqR,OAAO,CAAC,CAAA;MAC9D,IAAI,CAACC,iBAAiB,CAACvL,GAAG,CAAC/F,SAAS,EAAEiR,eAAe,CAAC,CAAA;AAC1D,KAAA;AACA,IAAA,OAAOA,eAAe,CAAA;AAC1B,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;EACID,oBAAoBA,CAACF,cAAc,EAAE;AACjC,IAAA,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE;AACtB;AACA,MAAA,OAAO,IAAI,CAAA;AACf,KAAA;AACA;AACA;AACA;AACA,IAAA,MAAM0B,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CAACZ,cAAc,CAAC,CAAA;IACxE,IAAIW,mBAAmB,KAAK,IAAI,EAAE;AAC9B;AACA,MAAA,OAAO,IAAI,CAAA;AACf,KAAA;AACA;AACA;AACA,IAAA,MAAMrB,GAAG,GAAGD,IAAI,CAACC,GAAG,EAAE,CAAA;IACtB,OAAOqB,mBAAmB,IAAIrB,GAAG,GAAG,IAAI,CAACL,cAAc,GAAG,IAAI,CAAA;AAClE,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2B,uBAAuBA,CAACZ,cAAc,EAAE;IACpC,IAAI,CAACA,cAAc,CAACa,OAAO,CAACzM,GAAG,CAAC,MAAM,CAAC,EAAE;AACrC,MAAA,OAAO,IAAI,CAAA;AACf,KAAA;IACA,MAAM0M,UAAU,GAAGd,cAAc,CAACa,OAAO,CAACxM,GAAG,CAAC,MAAM,CAAC,CAAA;AACrD,IAAA,MAAM0M,UAAU,GAAG,IAAI1B,IAAI,CAACyB,UAAU,CAAC,CAAA;AACvC,IAAA,MAAME,UAAU,GAAGD,UAAU,CAACE,OAAO,EAAE,CAAA;AACvC;AACA;AACA,IAAA,IAAIC,KAAK,CAACF,UAAU,CAAC,EAAE;AACnB,MAAA,OAAO,IAAI,CAAA;AACf,KAAA;AACA,IAAA,OAAOA,UAAU,CAAA;AACrB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMN,sBAAsBA,GAAG;AAC3B;AACA;IACA,KAAK,MAAM,CAACxR,SAAS,EAAEiR,eAAe,CAAC,IAAI,IAAI,CAACK,iBAAiB,EAAE;AAC/D,MAAA,MAAMnX,IAAI,CAACoW,MAAM,CAACjB,MAAM,CAACtP,SAAS,CAAC,CAAA;AACnC,MAAA,MAAMiR,eAAe,CAAC3B,MAAM,EAAE,CAAA;AAClC,KAAA;AACA;AACA,IAAA,IAAI,CAACgC,iBAAiB,GAAG,IAAInO,GAAG,EAAE,CAAA;AACtC,GAAA;AACJ;;AC5PA;AACA;AACA;AACA;AACA;AACA;AAEA,SAAS8O,WAAWA,CAACC,OAAO,EAAEC,YAAY,EAAE;AACxC,EAAA,MAAMC,WAAW,GAAG,IAAItP,GAAG,CAACoP,OAAO,CAAC,CAAA;AACpC,EAAA,KAAK,MAAMG,KAAK,IAAIF,YAAY,EAAE;AAC9BC,IAAAA,WAAW,CAACE,YAAY,CAAChD,MAAM,CAAC+C,KAAK,CAAC,CAAA;AAC1C,GAAA;EACA,OAAOD,WAAW,CAAC7P,IAAI,CAAA;AAC3B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAegQ,sBAAsBA,CAACjC,KAAK,EAAE7M,OAAO,EAAE0O,YAAY,EAAElC,YAAY,EAAE;EAC9E,MAAMuC,kBAAkB,GAAGP,WAAW,CAACxO,OAAO,CAAClE,GAAG,EAAE4S,YAAY,CAAC,CAAA;AACjE;AACA,EAAA,IAAI1O,OAAO,CAAClE,GAAG,KAAKiT,kBAAkB,EAAE;AACpC,IAAA,OAAOlC,KAAK,CAACvO,KAAK,CAAC0B,OAAO,EAAEwM,YAAY,CAAC,CAAA;AAC7C,GAAA;AACA;AACA,EAAA,MAAMwC,WAAW,GAAG5W,MAAM,CAAC6W,MAAM,CAAC7W,MAAM,CAAC6W,MAAM,CAAC,EAAE,EAAEzC,YAAY,CAAC,EAAE;AAAE0C,IAAAA,YAAY,EAAE,IAAA;AAAK,GAAC,CAAC,CAAA;EAC1F,MAAMC,SAAS,GAAG,MAAMtC,KAAK,CAACxU,IAAI,CAAC2H,OAAO,EAAEgP,WAAW,CAAC,CAAA;AACxD,EAAA,KAAK,MAAMI,QAAQ,IAAID,SAAS,EAAE;IAC9B,MAAME,mBAAmB,GAAGb,WAAW,CAACY,QAAQ,CAACtT,GAAG,EAAE4S,YAAY,CAAC,CAAA;IACnE,IAAIK,kBAAkB,KAAKM,mBAAmB,EAAE;AAC5C,MAAA,OAAOxC,KAAK,CAACvO,KAAK,CAAC8Q,QAAQ,EAAE5C,YAAY,CAAC,CAAA;AAC9C,KAAA;AACJ,GAAA;AACA,EAAA,OAAA;AACJ;;AC1CA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8C,QAAQ,CAAC;AACX;AACJ;AACA;AACIrS,EAAAA,WAAWA,GAAG;IACV,IAAI,CAACwH,OAAO,GAAG,IAAIhE,OAAO,CAAC,CAAC8F,OAAO,EAAEzE,MAAM,KAAK;MAC5C,IAAI,CAACyE,OAAO,GAAGA,OAAO,CAAA;MACtB,IAAI,CAACzE,MAAM,GAAGA,MAAM,CAAA;AACxB,KAAC,CAAC,CAAA;AACN,GAAA;AACJ;;AC1BA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeyN,0BAA0BA,GAAG;EACG;IACvC1Y,MAAM,CAACM,GAAG,CAAC,CAAgBuN,aAAAA,EAAAA,mBAAmB,CAAChJ,IAAI,CAAA,CAAA,CAAG,GAClD,CAAA,6BAAA,CAA+B,CAAC,CAAA;AACxC,GAAA;AACA,EAAA,KAAK,MAAMmJ,QAAQ,IAAIH,mBAAmB,EAAE;IACxC,MAAMG,QAAQ,EAAE,CAAA;IAC2B;AACvChO,MAAAA,MAAM,CAACM,GAAG,CAAC0N,QAAQ,EAAE,cAAc,CAAC,CAAA;AACxC,KAAA;AACJ,GAAA;EAC2C;AACvChO,IAAAA,MAAM,CAACM,GAAG,CAAC,6BAA6B,CAAC,CAAA;AAC7C,GAAA;AACJ;;AC/BA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASqY,OAAOA,CAACC,EAAE,EAAE;EACxB,OAAO,IAAIhP,OAAO,CAAE8F,OAAO,IAAKmJ,UAAU,CAACnJ,OAAO,EAAEkJ,EAAE,CAAC,CAAC,CAAA;AAC5D;;AChBA;AACA,IAAI;AACA/Y,EAAAA,IAAI,CAAC,0BAA0B,CAAC,IAAIC,CAAC,EAAE,CAAA;AAC3C,CAAC,CACD,OAAOC,CAAC,EAAE;;ACLV;AACA;AACA;AACA;AACA;AACA;AACA;AAUA,SAAS+Y,SAASA,CAACC,KAAK,EAAE;EACtB,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAG,IAAIhP,OAAO,CAACgP,KAAK,CAAC,GAAGA,KAAK,CAAA;AACjE,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;AAClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI5S,EAAAA,WAAWA,CAAC6S,QAAQ,EAAEC,OAAO,EAAE;AAC3B,IAAA,IAAI,CAACC,UAAU,GAAG,EAAE,CAAA;AACpB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACQ;AACR;AACA;AACA;AACA;AACA;AACA;AACQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACmD;MACvC7R,kBAAM,CAACX,UAAU,CAACuS,OAAO,CAAChQ,KAAK,EAAEkQ,eAAe,EAAE;AAC9CjX,QAAAA,UAAU,EAAE,oBAAoB;AAChCC,QAAAA,SAAS,EAAE,iBAAiB;AAC5BC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,eAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;AACAL,IAAAA,MAAM,CAAC6W,MAAM,CAAC,IAAI,EAAEc,OAAO,CAAC,CAAA;AAC5B,IAAA,IAAI,CAAChQ,KAAK,GAAGgQ,OAAO,CAAChQ,KAAK,CAAA;IAC1B,IAAI,CAACmQ,SAAS,GAAGJ,QAAQ,CAAA;AACzB,IAAA,IAAI,CAACK,gBAAgB,GAAG,IAAIb,QAAQ,EAAE,CAAA;IACtC,IAAI,CAACc,uBAAuB,GAAG,EAAE,CAAA;AACjC;AACA;IACA,IAAI,CAACC,QAAQ,GAAG,CAAC,GAAGP,QAAQ,CAACQ,OAAO,CAAC,CAAA;AACrC,IAAA,IAAI,CAACC,eAAe,GAAG,IAAI7Q,GAAG,EAAE,CAAA;AAChC,IAAA,KAAK,MAAM8Q,MAAM,IAAI,IAAI,CAACH,QAAQ,EAAE;MAChC,IAAI,CAACE,eAAe,CAACjO,GAAG,CAACkO,MAAM,EAAE,EAAE,CAAC,CAAA;AACxC,KAAA;IACA,IAAI,CAACzQ,KAAK,CAACc,SAAS,CAAC,IAAI,CAACsP,gBAAgB,CAAC1L,OAAO,CAAC,CAAA;AACvD,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMgM,KAAKA,CAACb,KAAK,EAAE;IACf,MAAM;AAAE7P,MAAAA,KAAAA;AAAM,KAAC,GAAG,IAAI,CAAA;AACtB,IAAA,IAAIC,OAAO,GAAG2P,SAAS,CAACC,KAAK,CAAC,CAAA;AAC9B,IAAA,IAAI5P,OAAO,CAAC0Q,IAAI,KAAK,UAAU,IAC3B3Q,KAAK,YAAY4Q,UAAU,IAC3B5Q,KAAK,CAAC6Q,eAAe,EAAE;AACvB,MAAA,MAAMC,uBAAuB,GAAI,MAAM9Q,KAAK,CAAC6Q,eAAgB,CAAA;AAC7D,MAAA,IAAIC,uBAAuB,EAAE;QACkB;AACvCha,UAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,0CAAA,CAA4C,GACnD,CAAA,CAAA,EAAIgI,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,GAAG,CAAC,CAAA;AAC3C,SAAA;AACA,QAAA,OAAO+U,uBAAuB,CAAA;AAClC,OAAA;AACJ,KAAA;AACA;AACA;AACA;AACA,IAAA,MAAMC,eAAe,GAAG,IAAI,CAACC,WAAW,CAAC,cAAc,CAAC,GAClD/Q,OAAO,CAACgR,KAAK,EAAE,GACf,IAAI,CAAA;IACV,IAAI;MACA,KAAK,MAAMC,EAAE,IAAI,IAAI,CAACC,gBAAgB,CAAC,kBAAkB,CAAC,EAAE;QACxDlR,OAAO,GAAG,MAAMiR,EAAE,CAAC;AAAEjR,UAAAA,OAAO,EAAEA,OAAO,CAACgR,KAAK,EAAE;AAAEjR,UAAAA,KAAAA;AAAM,SAAC,CAAC,CAAA;AAC3D,OAAA;KACH,CACD,OAAO8B,GAAG,EAAE;MACR,IAAIA,GAAG,YAAYjJ,KAAK,EAAE;AACtB,QAAA,MAAM,IAAIoE,YAAY,CAAC,iCAAiC,EAAE;UACtD/C,kBAAkB,EAAE4H,GAAG,CAAC5F,OAAAA;AAC5B,SAAC,CAAC,CAAA;AACN,OAAA;AACJ,KAAA;AACA;AACA;AACA;AACA,IAAA,MAAMkV,qBAAqB,GAAGnR,OAAO,CAACgR,KAAK,EAAE,CAAA;IAC7C,IAAI;AACA,MAAA,IAAII,aAAa,CAAA;AACjB;AACAA,MAAAA,aAAa,GAAG,MAAMX,KAAK,CAACzQ,OAAO,EAAEA,OAAO,CAAC0Q,IAAI,KAAK,UAAU,GAAGtO,SAAS,GAAG,IAAI,CAAC8N,SAAS,CAACmB,YAAY,CAAC,CAAA;MAC3G,IAAI,aAAoB,KAAK,YAAY,EAAE;AACvCxa,QAAAA,MAAM,CAACK,KAAK,CAAC,sBAAsB,GAC/B,CAAA,CAAA,EAAIiI,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,6BAA6B,GAC5D,CAAA,QAAA,EAAWsV,aAAa,CAACjV,MAAM,IAAI,CAAC,CAAA;AAC5C,OAAA;MACA,KAAK,MAAM0I,QAAQ,IAAI,IAAI,CAACqM,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;QAC7DE,aAAa,GAAG,MAAMvM,QAAQ,CAAC;UAC3B9E,KAAK;AACLC,UAAAA,OAAO,EAAEmR,qBAAqB;AAC9BG,UAAAA,QAAQ,EAAEF,aAAAA;AACd,SAAC,CAAC,CAAA;AACN,OAAA;AACA,MAAA,OAAOA,aAAa,CAAA;KACvB,CACD,OAAO/Z,KAAK,EAAE;MACiC;AACvCR,QAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,oBAAA,CAAsB,GAC7B,CAAIgI,CAAAA,EAAAA,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAmB,iBAAA,CAAA,EAAEzE,KAAK,CAAC,CAAA;AAClE,OAAA;AACA;AACA;AACA,MAAA,IAAIyZ,eAAe,EAAE;AACjB,QAAA,MAAM,IAAI,CAACS,YAAY,CAAC,cAAc,EAAE;AACpCla,UAAAA,KAAK,EAAEA,KAAK;UACZ0I,KAAK;AACL+Q,UAAAA,eAAe,EAAEA,eAAe,CAACE,KAAK,EAAE;AACxChR,UAAAA,OAAO,EAAEmR,qBAAqB,CAACH,KAAK,EAAC;AACzC,SAAC,CAAC,CAAA;AACN,OAAA;AACA,MAAA,MAAM3Z,KAAK,CAAA;AACf,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMma,gBAAgBA,CAAC5B,KAAK,EAAE;IAC1B,MAAM0B,QAAQ,GAAG,MAAM,IAAI,CAACb,KAAK,CAACb,KAAK,CAAC,CAAA;AACxC,IAAA,MAAM6B,aAAa,GAAGH,QAAQ,CAACN,KAAK,EAAE,CAAA;AACtC,IAAA,KAAK,IAAI,CAACnQ,SAAS,CAAC,IAAI,CAAC6Q,QAAQ,CAAC9B,KAAK,EAAE6B,aAAa,CAAC,CAAC,CAAA;AACxD,IAAA,OAAOH,QAAQ,CAAA;AACnB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMK,UAAUA,CAACrZ,GAAG,EAAE;AAClB,IAAA,MAAM0H,OAAO,GAAG2P,SAAS,CAACrX,GAAG,CAAC,CAAA;AAC9B,IAAA,IAAI+U,cAAc,CAAA;IAClB,MAAM;MAAE9Q,SAAS;AAAEiQ,MAAAA,YAAAA;KAAc,GAAG,IAAI,CAAC0D,SAAS,CAAA;IAClD,MAAM0B,gBAAgB,GAAG,MAAM,IAAI,CAACC,WAAW,CAAC7R,OAAO,EAAE,MAAM,CAAC,CAAA;AAChE,IAAA,MAAM8R,iBAAiB,GAAG1Z,MAAM,CAAC6W,MAAM,CAAC7W,MAAM,CAAC6W,MAAM,CAAC,EAAE,EAAEzC,YAAY,CAAC,EAAE;AAAEjQ,MAAAA,SAAAA;AAAU,KAAC,CAAC,CAAA;IACvF8Q,cAAc,GAAG,MAAMP,MAAM,CAACxO,KAAK,CAACsT,gBAAgB,EAAEE,iBAAiB,CAAC,CAAA;IAC7B;AACvC,MAAA,IAAIzE,cAAc,EAAE;AAChBxW,QAAAA,MAAM,CAACK,KAAK,CAAC,CAA+BqF,4BAAAA,EAAAA,SAAS,IAAI,CAAC,CAAA;AAC9D,OAAC,MACI;AACD1F,QAAAA,MAAM,CAACK,KAAK,CAAC,CAAgCqF,6BAAAA,EAAAA,SAAS,IAAI,CAAC,CAAA;AAC/D,OAAA;AACJ,KAAA;IACA,KAAK,MAAMsI,QAAQ,IAAI,IAAI,CAACqM,gBAAgB,CAAC,0BAA0B,CAAC,EAAE;AACtE7D,MAAAA,cAAc,GACV,CAAC,MAAMxI,QAAQ,CAAC;QACZtI,SAAS;QACTiQ,YAAY;QACZa,cAAc;AACdrN,QAAAA,OAAO,EAAE4R,gBAAgB;QACzB7R,KAAK,EAAE,IAAI,CAACA,KAAAA;OACf,CAAC,KAAKqC,SAAS,CAAA;AACxB,KAAA;AACA,IAAA,OAAOiL,cAAc,CAAA;AACzB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAMqE,QAAQA,CAACpZ,GAAG,EAAEgZ,QAAQ,EAAE;AAC1B,IAAA,MAAMtR,OAAO,GAAG2P,SAAS,CAACrX,GAAG,CAAC,CAAA;AAC9B;AACA;IACA,MAAMkX,OAAO,CAAC,CAAC,CAAC,CAAA;IAChB,MAAMoC,gBAAgB,GAAG,MAAM,IAAI,CAACC,WAAW,CAAC7R,OAAO,EAAE,OAAO,CAAC,CAAA;IACtB;MACvC,IAAI4R,gBAAgB,CAACna,MAAM,IAAIma,gBAAgB,CAACna,MAAM,KAAK,KAAK,EAAE;AAC9D,QAAA,MAAM,IAAIuF,YAAY,CAAC,kCAAkC,EAAE;AACvDlB,UAAAA,GAAG,EAAEqD,cAAc,CAACyS,gBAAgB,CAAC9V,GAAG,CAAC;UACzCrE,MAAM,EAAEma,gBAAgB,CAACna,MAAAA;AAC7B,SAAC,CAAC,CAAA;AACN,OAAA;AACA;MACA,MAAMsa,IAAI,GAAGT,QAAQ,CAACpD,OAAO,CAACxM,GAAG,CAAC,MAAM,CAAC,CAAA;AACzC,MAAA,IAAIqQ,IAAI,EAAE;AACNlb,QAAAA,MAAM,CAACK,KAAK,CAAC,oBAAoBiI,cAAc,CAACyS,gBAAgB,CAAC9V,GAAG,CAAC,CAAG,CAAA,CAAA,GACpE,gBAAgBiW,IAAI,CAAA,UAAA,CAAY,GAChC,CAAkE,gEAAA,CAAA,GAClE,0DAA0D,CAAC,CAAA;AACnE,OAAA;AACJ,KAAA;IACA,IAAI,CAACT,QAAQ,EAAE;MACgC;AACvCza,QAAAA,MAAM,CAACQ,KAAK,CAAC,CAAA,uCAAA,CAAyC,GAClD,CAAA,CAAA,EAAI8H,cAAc,CAACyS,gBAAgB,CAAC9V,GAAG,CAAC,IAAI,CAAC,CAAA;AACrD,OAAA;AACA,MAAA,MAAM,IAAIkB,YAAY,CAAC,4BAA4B,EAAE;AACjDlB,QAAAA,GAAG,EAAEqD,cAAc,CAACyS,gBAAgB,CAAC9V,GAAG,CAAA;AAC5C,OAAC,CAAC,CAAA;AACN,KAAA;IACA,MAAMkW,eAAe,GAAG,MAAM,IAAI,CAACC,0BAA0B,CAACX,QAAQ,CAAC,CAAA;IACvE,IAAI,CAACU,eAAe,EAAE;MACyB;AACvCnb,QAAAA,MAAM,CAACK,KAAK,CAAC,CAAA,UAAA,EAAaiI,cAAc,CAACyS,gBAAgB,CAAC9V,GAAG,CAAC,CAAI,EAAA,CAAA,GAC9D,CAAqB,mBAAA,CAAA,EAAEkW,eAAe,CAAC,CAAA;AAC/C,OAAA;AACA,MAAA,OAAO,KAAK,CAAA;AAChB,KAAA;IACA,MAAM;MAAEzV,SAAS;AAAEiQ,MAAAA,YAAAA;KAAc,GAAG,IAAI,CAAC0D,SAAS,CAAA;IAClD,MAAMrD,KAAK,GAAG,MAAMnW,IAAI,CAACoW,MAAM,CAACnE,IAAI,CAACpM,SAAS,CAAC,CAAA;AAC/C,IAAA,MAAM2V,sBAAsB,GAAG,IAAI,CAACnB,WAAW,CAAC,gBAAgB,CAAC,CAAA;AACjE,IAAA,MAAMoB,WAAW,GAAGD,sBAAsB,GACpC,MAAMpD,sBAAsB;AAC9B;AACA;AACA;AACAjC,IAAAA,KAAK,EAAE+E,gBAAgB,CAACZ,KAAK,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAExE,YAAY,CAAC,GACjE,IAAI,CAAA;IACiC;AACvC3V,MAAAA,MAAM,CAACK,KAAK,CAAC,CAAA,cAAA,EAAiBqF,SAAS,CAA8B,4BAAA,CAAA,GACjE,CAAO4C,IAAAA,EAAAA,cAAc,CAACyS,gBAAgB,CAAC9V,GAAG,CAAC,GAAG,CAAC,CAAA;AACvD,KAAA;IACA,IAAI;AACA,MAAA,MAAM+Q,KAAK,CAAC1B,GAAG,CAACyG,gBAAgB,EAAEM,sBAAsB,GAAGF,eAAe,CAAChB,KAAK,EAAE,GAAGgB,eAAe,CAAC,CAAA;KACxG,CACD,OAAO3a,KAAK,EAAE;MACV,IAAIA,KAAK,YAAYuB,KAAK,EAAE;AACxB;AACA,QAAA,IAAIvB,KAAK,CAACkD,IAAI,KAAK,oBAAoB,EAAE;UACrC,MAAMgV,0BAA0B,EAAE,CAAA;AACtC,SAAA;AACA,QAAA,MAAMlY,KAAK,CAAA;AACf,OAAA;AACJ,KAAA;IACA,KAAK,MAAMwN,QAAQ,IAAI,IAAI,CAACqM,gBAAgB,CAAC,gBAAgB,CAAC,EAAE;AAC5D,MAAA,MAAMrM,QAAQ,CAAC;QACXtI,SAAS;QACT4V,WAAW;AACXC,QAAAA,WAAW,EAAEJ,eAAe,CAAChB,KAAK,EAAE;AACpChR,QAAAA,OAAO,EAAE4R,gBAAgB;QACzB7R,KAAK,EAAE,IAAI,CAACA,KAAAA;AAChB,OAAC,CAAC,CAAA;AACN,KAAA;AACA,IAAA,OAAO,IAAI,CAAA;AACf,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAM8R,WAAWA,CAAC7R,OAAO,EAAE0Q,IAAI,EAAE;IAC7B,MAAMpY,GAAG,GAAG,CAAG0H,EAAAA,OAAO,CAAClE,GAAG,CAAA,GAAA,EAAM4U,IAAI,CAAE,CAAA,CAAA;AACtC,IAAA,IAAI,CAAC,IAAI,CAACV,UAAU,CAAC1X,GAAG,CAAC,EAAE;MACvB,IAAIsZ,gBAAgB,GAAG5R,OAAO,CAAA;MAC9B,KAAK,MAAM6E,QAAQ,IAAI,IAAI,CAACqM,gBAAgB,CAAC,oBAAoB,CAAC,EAAE;AAChEU,QAAAA,gBAAgB,GAAGjC,SAAS,CAAC,MAAM9K,QAAQ,CAAC;UACxC6L,IAAI;AACJ1Q,UAAAA,OAAO,EAAE4R,gBAAgB;UACzB7R,KAAK,EAAE,IAAI,CAACA,KAAK;AACjB;AACAqB,UAAAA,MAAM,EAAE,IAAI,CAACA,MAAM;AACvB,SAAC,CAAC,CAAC,CAAA;AACP,OAAA;AACA,MAAA,IAAI,CAAC4O,UAAU,CAAC1X,GAAG,CAAC,GAAGsZ,gBAAgB,CAAA;AAC3C,KAAA;AACA,IAAA,OAAO,IAAI,CAAC5B,UAAU,CAAC1X,GAAG,CAAC,CAAA;AAC/B,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIyY,WAAWA,CAACxW,IAAI,EAAE;IACd,KAAK,MAAMiW,MAAM,IAAI,IAAI,CAACN,SAAS,CAACI,OAAO,EAAE;MACzC,IAAI/V,IAAI,IAAIiW,MAAM,EAAE;AAChB,QAAA,OAAO,IAAI,CAAA;AACf,OAAA;AACJ,KAAA;AACA,IAAA,OAAO,KAAK,CAAA;AAChB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAMe,YAAYA,CAAChX,IAAI,EAAEqU,KAAK,EAAE;IAC5B,KAAK,MAAM/J,QAAQ,IAAI,IAAI,CAACqM,gBAAgB,CAAC3W,IAAI,CAAC,EAAE;AAChD;AACA;MACA,MAAMsK,QAAQ,CAAC+J,KAAK,CAAC,CAAA;AACzB,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,CAACsC,gBAAgBA,CAAC3W,IAAI,EAAE;IACpB,KAAK,MAAMiW,MAAM,IAAI,IAAI,CAACN,SAAS,CAACI,OAAO,EAAE;AACzC,MAAA,IAAI,OAAOE,MAAM,CAACjW,IAAI,CAAC,KAAK,UAAU,EAAE;QACpC,MAAM8X,KAAK,GAAG,IAAI,CAAC9B,eAAe,CAAC7O,GAAG,CAAC8O,MAAM,CAAC,CAAA;QAC9C,MAAM8B,gBAAgB,GAAI1D,KAAK,IAAK;AAChC,UAAA,MAAM2D,aAAa,GAAGna,MAAM,CAAC6W,MAAM,CAAC7W,MAAM,CAAC6W,MAAM,CAAC,EAAE,EAAEL,KAAK,CAAC,EAAE;AAAEyD,YAAAA,KAAAA;AAAM,WAAC,CAAC,CAAA;AACxE;AACA;AACA,UAAA,OAAO7B,MAAM,CAACjW,IAAI,CAAC,CAACgY,aAAa,CAAC,CAAA;SACrC,CAAA;AACD,QAAA,MAAMD,gBAAgB,CAAA;AAC1B,OAAA;AACJ,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIzR,SAASA,CAAC4D,OAAO,EAAE;AACf,IAAA,IAAI,CAAC2L,uBAAuB,CAAC5O,IAAI,CAACiD,OAAO,CAAC,CAAA;AAC1C,IAAA,OAAOA,OAAO,CAAA;AAClB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAM+N,WAAWA,GAAG;AAChB,IAAA,IAAI/N,OAAO,CAAA;IACX,OAAQA,OAAO,GAAG,IAAI,CAAC2L,uBAAuB,CAACzG,KAAK,EAAE,EAAG;AACrD,MAAA,MAAMlF,OAAO,CAAA;AACjB,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACIgO,EAAAA,OAAOA,GAAG;AACN,IAAA,IAAI,CAACtC,gBAAgB,CAAC5J,OAAO,CAAC,IAAI,CAAC,CAAA;AACvC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAM0L,0BAA0BA,CAACX,QAAQ,EAAE;IACvC,IAAIU,eAAe,GAAGV,QAAQ,CAAA;IAC9B,IAAIoB,WAAW,GAAG,KAAK,CAAA;IACvB,KAAK,MAAM7N,QAAQ,IAAI,IAAI,CAACqM,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;AAC7Dc,MAAAA,eAAe,GACX,CAAC,MAAMnN,QAAQ,CAAC;QACZ7E,OAAO,EAAE,IAAI,CAACA,OAAO;AACrBsR,QAAAA,QAAQ,EAAEU,eAAe;QACzBjS,KAAK,EAAE,IAAI,CAACA,KAAAA;OACf,CAAC,KAAKqC,SAAS,CAAA;AACpBsQ,MAAAA,WAAW,GAAG,IAAI,CAAA;MAClB,IAAI,CAACV,eAAe,EAAE;AAClB,QAAA,MAAA;AACJ,OAAA;AACJ,KAAA;IACA,IAAI,CAACU,WAAW,EAAE;AACd,MAAA,IAAIV,eAAe,IAAIA,eAAe,CAAC7V,MAAM,KAAK,GAAG,EAAE;AACnD6V,QAAAA,eAAe,GAAG5P,SAAS,CAAA;AAC/B,OAAA;MAC2C;AACvC,QAAA,IAAI4P,eAAe,EAAE;AACjB,UAAA,IAAIA,eAAe,CAAC7V,MAAM,KAAK,GAAG,EAAE;AAChC,YAAA,IAAI6V,eAAe,CAAC7V,MAAM,KAAK,CAAC,EAAE;AAC9BtF,cAAAA,MAAM,CAACO,IAAI,CAAC,CAAA,kBAAA,EAAqB,IAAI,CAAC4I,OAAO,CAAClE,GAAG,CAAI,EAAA,CAAA,GACjD,CAA0D,wDAAA,CAAA,GAC1D,mDAAmD,CAAC,CAAA;AAC5D,aAAC,MACI;AACDjF,cAAAA,MAAM,CAACK,KAAK,CAAC,qBAAqB,IAAI,CAAC8I,OAAO,CAAClE,GAAG,CAAI,EAAA,CAAA,GAClD,8BAA8BwV,QAAQ,CAACnV,MAAM,CAAc,YAAA,CAAA,GAC3D,wBAAwB,CAAC,CAAA;AACjC,aAAA;AACJ,WAAA;AACJ,SAAA;AACJ,OAAA;AACJ,KAAA;AACA,IAAA,OAAO6V,eAAe,CAAA;AAC1B,GAAA;AACJ;;ACngBA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA,MAAMW,QAAQ,CAAC;AACX;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI1V,EAAAA,WAAWA,CAAC8S,OAAO,GAAG,EAAE,EAAE;AACtB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACxT,SAAS,GAAGyH,UAAU,CAACM,cAAc,CAACyL,OAAO,CAACxT,SAAS,CAAC,CAAA;AAC7D;AACR;AACA;AACA;AACA;AACA;AACA;AACQ,IAAA,IAAI,CAAC+T,OAAO,GAAGP,OAAO,CAACO,OAAO,IAAI,EAAE,CAAA;AACpC;AACR;AACA;AACA;AACA;AACA;AACA;AACQ,IAAA,IAAI,CAACe,YAAY,GAAGtB,OAAO,CAACsB,YAAY,CAAA;AACxC;AACR;AACA;AACA;AACA;AACA;AACA;AACQ,IAAA,IAAI,CAAC7E,YAAY,GAAGuD,OAAO,CAACvD,YAAY,CAAA;AAC5C,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpO,MAAMA,CAAC2R,OAAO,EAAE;IACZ,MAAM,CAAC6C,YAAY,CAAC,GAAG,IAAI,CAACC,SAAS,CAAC9C,OAAO,CAAC,CAAA;AAC9C,IAAA,OAAO6C,YAAY,CAAA;AACvB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAAC9C,OAAO,EAAE;AACf;IACA,IAAIA,OAAO,YAAYY,UAAU,EAAE;AAC/BZ,MAAAA,OAAO,GAAG;AACNhQ,QAAAA,KAAK,EAAEgQ,OAAO;QACd/P,OAAO,EAAE+P,OAAO,CAAC/P,OAAAA;OACpB,CAAA;AACL,KAAA;AACA,IAAA,MAAMD,KAAK,GAAGgQ,OAAO,CAAChQ,KAAK,CAAA;AAC3B,IAAA,MAAMC,OAAO,GAAG,OAAO+P,OAAO,CAAC/P,OAAO,KAAK,QAAQ,GAC7C,IAAIY,OAAO,CAACmP,OAAO,CAAC/P,OAAO,CAAC,GAC5B+P,OAAO,CAAC/P,OAAO,CAAA;IACrB,MAAMoB,MAAM,GAAG,QAAQ,IAAI2O,OAAO,GAAGA,OAAO,CAAC3O,MAAM,GAAGgB,SAAS,CAAA;AAC/D,IAAA,MAAMlE,OAAO,GAAG,IAAI2R,eAAe,CAAC,IAAI,EAAE;MAAE9P,KAAK;MAAEC,OAAO;AAAEoB,MAAAA,MAAAA;AAAO,KAAC,CAAC,CAAA;IACrE,MAAMwR,YAAY,GAAG,IAAI,CAACE,YAAY,CAAC5U,OAAO,EAAE8B,OAAO,EAAED,KAAK,CAAC,CAAA;AAC/D,IAAA,MAAMgT,WAAW,GAAG,IAAI,CAACC,cAAc,CAACJ,YAAY,EAAE1U,OAAO,EAAE8B,OAAO,EAAED,KAAK,CAAC,CAAA;AAC9E;AACA,IAAA,OAAO,CAAC6S,YAAY,EAAEG,WAAW,CAAC,CAAA;AACtC,GAAA;AACA,EAAA,MAAMD,YAAYA,CAAC5U,OAAO,EAAE8B,OAAO,EAAED,KAAK,EAAE;AACxC,IAAA,MAAM7B,OAAO,CAACqT,YAAY,CAAC,kBAAkB,EAAE;MAAExR,KAAK;AAAEC,MAAAA,OAAAA;AAAQ,KAAC,CAAC,CAAA;IAClE,IAAIsR,QAAQ,GAAGlP,SAAS,CAAA;IACxB,IAAI;MACAkP,QAAQ,GAAG,MAAM,IAAI,CAAC2B,OAAO,CAACjT,OAAO,EAAE9B,OAAO,CAAC,CAAA;AAC/C;AACA;AACA;MACA,IAAI,CAACoT,QAAQ,IAAIA,QAAQ,CAAC3U,IAAI,KAAK,OAAO,EAAE;AACxC,QAAA,MAAM,IAAIK,YAAY,CAAC,aAAa,EAAE;UAAElB,GAAG,EAAEkE,OAAO,CAAClE,GAAAA;AAAI,SAAC,CAAC,CAAA;AAC/D,OAAA;KACH,CACD,OAAOzE,KAAK,EAAE;MACV,IAAIA,KAAK,YAAYuB,KAAK,EAAE;QACxB,KAAK,MAAMiM,QAAQ,IAAI3G,OAAO,CAACgT,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;UAChEI,QAAQ,GAAG,MAAMzM,QAAQ,CAAC;YAAExN,KAAK;YAAE0I,KAAK;AAAEC,YAAAA,OAAAA;AAAQ,WAAC,CAAC,CAAA;AACpD,UAAA,IAAIsR,QAAQ,EAAE;AACV,YAAA,MAAA;AACJ,WAAA;AACJ,SAAA;AACJ,OAAA;MACA,IAAI,CAACA,QAAQ,EAAE;AACX,QAAA,MAAMja,KAAK,CAAA;AACf,OAAC,MAC+C;QAC5CR,MAAM,CAACM,GAAG,CAAC,CAAwBgI,qBAAAA,EAAAA,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAA,GAAA,CAAK,GAC/D,CAAA,GAAA,EAAMzE,KAAK,YAAYuB,KAAK,GAAGvB,KAAK,CAAC4H,QAAQ,EAAE,GAAG,EAAE,CAAA,uDAAA,CAAyD,GAC7G,CAAA,yBAAA,CAA2B,CAAC,CAAA;AACpC,OAAA;AACJ,KAAA;IACA,KAAK,MAAM4F,QAAQ,IAAI3G,OAAO,CAACgT,gBAAgB,CAAC,oBAAoB,CAAC,EAAE;MACnEI,QAAQ,GAAG,MAAMzM,QAAQ,CAAC;QAAE9E,KAAK;QAAEC,OAAO;AAAEsR,QAAAA,QAAAA;AAAS,OAAC,CAAC,CAAA;AAC3D,KAAA;AACA,IAAA,OAAOA,QAAQ,CAAA;AACnB,GAAA;EACA,MAAM0B,cAAcA,CAACJ,YAAY,EAAE1U,OAAO,EAAE8B,OAAO,EAAED,KAAK,EAAE;AACxD,IAAA,IAAIuR,QAAQ,CAAA;AACZ,IAAA,IAAIja,KAAK,CAAA;IACT,IAAI;MACAia,QAAQ,GAAG,MAAMsB,YAAY,CAAA;KAChC,CACD,OAAOvb,KAAK,EAAE;AACV;AACA;AACA;AAAA,KAAA;IAEJ,IAAI;AACA,MAAA,MAAM6G,OAAO,CAACqT,YAAY,CAAC,mBAAmB,EAAE;QAC5CxR,KAAK;QACLC,OAAO;AACPsR,QAAAA,QAAAA;AACJ,OAAC,CAAC,CAAA;AACF,MAAA,MAAMpT,OAAO,CAACsU,WAAW,EAAE,CAAA;KAC9B,CACD,OAAOU,cAAc,EAAE;MACnB,IAAIA,cAAc,YAAYta,KAAK,EAAE;AACjCvB,QAAAA,KAAK,GAAG6b,cAAc,CAAA;AAC1B,OAAA;AACJ,KAAA;AACA,IAAA,MAAMhV,OAAO,CAACqT,YAAY,CAAC,oBAAoB,EAAE;MAC7CxR,KAAK;MACLC,OAAO;MACPsR,QAAQ;AACRja,MAAAA,KAAK,EAAEA,KAAAA;AACX,KAAC,CAAC,CAAA;IACF6G,OAAO,CAACuU,OAAO,EAAE,CAAA;AACjB,IAAA,IAAIpb,KAAK,EAAE;AACP,MAAA,MAAMA,KAAK,CAAA;AACf,KAAA;AACJ,GAAA;AACJ,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACnOA;AACA;AACA;AACA;AACA;AACA;AACA;AAIO,MAAMkB,QAAQ,GAAG;AACpB4a,EAAAA,aAAa,EAAEA,CAACC,YAAY,EAAEpT,OAAO,KAAK,CAAA,MAAA,EAASoT,YAAY,CAAA,gBAAA,EAAmBjU,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAG,CAAA,CAAA;EAChHuX,kBAAkB,EAAG/B,QAAQ,IAAK;AAC9B,IAAA,IAAIA,QAAQ,EAAE;AACVza,MAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,6BAAA,CAA+B,CAAC,CAAA;AACtDT,MAAAA,MAAM,CAACM,GAAG,CAACma,QAAQ,IAAI,wBAAwB,CAAC,CAAA;MAChDza,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,KAAA;AACJ,GAAA;AACJ,CAAC;;ACnBD;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+b,UAAU,SAASX,QAAQ,CAAC;AAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAMM,OAAOA,CAACjT,OAAO,EAAE9B,OAAO,EAAE;IAC5B,MAAMqV,IAAI,GAAG,EAAE,CAAA;IAC4B;AACvCpV,MAAAA,kBAAM,CAACX,UAAU,CAACwC,OAAO,EAAEY,OAAO,EAAE;AAChC5H,QAAAA,UAAU,EAAE,oBAAoB;AAChCC,QAAAA,SAAS,EAAE,IAAI,CAACgE,WAAW,CAAC1C,IAAI;AAChCrB,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,SAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;IACA,IAAI6Y,QAAQ,GAAG,MAAMpT,OAAO,CAACyT,UAAU,CAAC3R,OAAO,CAAC,CAAA;IAChD,IAAI3I,KAAK,GAAG+K,SAAS,CAAA;IACrB,IAAI,CAACkP,QAAQ,EAAE;MACgC;QACvCiC,IAAI,CAAC/R,IAAI,CAAC,CAA6B,0BAAA,EAAA,IAAI,CAACjF,SAAS,CAAA,SAAA,CAAW,GAC5D,CAAA,oCAAA,CAAsC,CAAC,CAAA;AAC/C,OAAA;MACA,IAAI;AACA+U,QAAAA,QAAQ,GAAG,MAAMpT,OAAO,CAACsT,gBAAgB,CAACxR,OAAO,CAAC,CAAA;OACrD,CACD,OAAO6B,GAAG,EAAE;QACR,IAAIA,GAAG,YAAYjJ,KAAK,EAAE;AACtBvB,UAAAA,KAAK,GAAGwK,GAAG,CAAA;AACf,SAAA;AACJ,OAAA;MAC2C;AACvC,QAAA,IAAIyP,QAAQ,EAAE;AACViC,UAAAA,IAAI,CAAC/R,IAAI,CAAC,CAAA,0BAAA,CAA4B,CAAC,CAAA;AAC3C,SAAC,MACI;AACD+R,UAAAA,IAAI,CAAC/R,IAAI,CAAC,CAAA,0CAAA,CAA4C,CAAC,CAAA;AAC3D,SAAA;AACJ,OAAA;AACJ,KAAC,MACI;MAC0C;QACvC+R,IAAI,CAAC/R,IAAI,CAAC,CAAA,gCAAA,EAAmC,IAAI,CAACjF,SAAS,UAAU,CAAC,CAAA;AAC1E,OAAA;AACJ,KAAA;IAC2C;AACvC1F,MAAAA,MAAM,CAACS,cAAc,CAACiB,QAAQ,CAAC4a,aAAa,CAAC,IAAI,CAAClW,WAAW,CAAC1C,IAAI,EAAEyF,OAAO,CAAC,CAAC,CAAA;AAC7E,MAAA,KAAK,MAAM7I,GAAG,IAAIoc,IAAI,EAAE;AACpB1c,QAAAA,MAAM,CAACM,GAAG,CAACA,GAAG,CAAC,CAAA;AACnB,OAAA;AACAoB,MAAAA,QAAQ,CAAC8a,kBAAkB,CAAC/B,QAAQ,CAAC,CAAA;MACrCza,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,KAAA;IACA,IAAI,CAAC+Z,QAAQ,EAAE;AACX,MAAA,MAAM,IAAItU,YAAY,CAAC,aAAa,EAAE;QAAElB,GAAG,EAAEkE,OAAO,CAAClE,GAAG;AAAEzE,QAAAA,KAAAA;AAAM,OAAC,CAAC,CAAA;AACtE,KAAA;AACA,IAAA,OAAOia,QAAQ,CAAA;AACnB,GAAA;AACJ;;ACtFA;AACA,IAAI;AACA5a,EAAAA,IAAI,CAAC,kCAAkC,CAAC,IAAIC,CAAC,EAAE,CAAA;AACnD,CAAC,CACD,OAAOC,CAAC,EAAE;;ACLV;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4c,iBAAiB,CAAC;AACpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIvW,EAAAA,WAAWA,CAAC+O,MAAM,GAAG,EAAE,EAAE;IACsB;MACvC,IAAI,EAAEA,MAAM,CAACyH,QAAQ,IAAIzH,MAAM,CAACkC,OAAO,CAAC,EAAE;AACtC,QAAA,MAAM,IAAIlR,YAAY,CAAC,8BAA8B,EAAE;AACnDhE,UAAAA,UAAU,EAAE,4BAA4B;AACxCC,UAAAA,SAAS,EAAE,mBAAmB;AAC9BC,UAAAA,QAAQ,EAAE,aAAA;AACd,SAAC,CAAC,CAAA;AACN,OAAA;MACA,IAAI8S,MAAM,CAACyH,QAAQ,EAAE;AACjBtV,QAAAA,kBAAM,CAAChB,OAAO,CAAC6O,MAAM,CAACyH,QAAQ,EAAE;AAC5Bza,UAAAA,UAAU,EAAE,4BAA4B;AACxCC,UAAAA,SAAS,EAAE,mBAAmB;AAC9BC,UAAAA,QAAQ,EAAE,aAAa;AACvBT,UAAAA,SAAS,EAAE,iBAAA;AACf,SAAC,CAAC,CAAA;AACN,OAAA;MACA,IAAIuT,MAAM,CAACkC,OAAO,EAAE;QAChB/P,kBAAM,CAACZ,MAAM,CAACyO,MAAM,CAACkC,OAAO,EAAE,QAAQ,EAAE;AACpClV,UAAAA,UAAU,EAAE,4BAA4B;AACxCC,UAAAA,SAAS,EAAE,mBAAmB;AAC9BC,UAAAA,QAAQ,EAAE,aAAa;AACvBT,UAAAA,SAAS,EAAE,gBAAA;AACf,SAAC,CAAC,CAAA;AACN,OAAA;AACJ,KAAA;AACA,IAAA,IAAI,CAACib,SAAS,GAAG1H,MAAM,CAACyH,QAAQ,CAAA;AAChC,IAAA,IAAI,CAACE,QAAQ,GAAG3H,MAAM,CAACkC,OAAO,CAAA;AAClC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI0F,mBAAmBA,CAACtC,QAAQ,EAAE;IACiB;AACvCnT,MAAAA,kBAAM,CAACX,UAAU,CAAC8T,QAAQ,EAAEuC,QAAQ,EAAE;AAClC7a,QAAAA,UAAU,EAAE,4BAA4B;AACxCC,QAAAA,SAAS,EAAE,mBAAmB;AAC9BC,QAAAA,QAAQ,EAAE,qBAAqB;AAC/BT,QAAAA,SAAS,EAAE,UAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;IACA,IAAIqb,SAAS,GAAG,IAAI,CAAA;IACpB,IAAI,IAAI,CAACJ,SAAS,EAAE;MAChBI,SAAS,GAAG,IAAI,CAACJ,SAAS,CAAC/V,QAAQ,CAAC2T,QAAQ,CAACnV,MAAM,CAAC,CAAA;AACxD,KAAA;AACA,IAAA,IAAI,IAAI,CAACwX,QAAQ,IAAIG,SAAS,EAAE;AAC5BA,MAAAA,SAAS,GAAG1b,MAAM,CAACC,IAAI,CAAC,IAAI,CAACsb,QAAQ,CAAC,CAAC1O,IAAI,CAAE8O,UAAU,IAAK;AACxD,QAAA,OAAOzC,QAAQ,CAACpD,OAAO,CAACxM,GAAG,CAACqS,UAAU,CAAC,KAAK,IAAI,CAACJ,QAAQ,CAACI,UAAU,CAAC,CAAA;AACzE,OAAC,CAAC,CAAA;AACN,KAAA;IAC2C;MACvC,IAAI,CAACD,SAAS,EAAE;AACZjd,QAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,gBAAA,CAAkB,GACpC,CAAI6H,CAAAA,EAAAA,cAAc,CAACmS,QAAQ,CAACxV,GAAG,CAAC,CAAkC,gCAAA,CAAA,GAClE,yCAAyC,CAAC,CAAA;AAC9CjF,QAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,gCAAA,CAAkC,CAAC,CAAA;AACzDT,QAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,oBAAA,CAAsB,GAAG0B,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC4a,SAAS,CAAC,CAAC,CAAA;AACnE7c,QAAAA,MAAM,CAACM,GAAG,CAAC,CAAqB,mBAAA,CAAA,GAAG0B,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC6a,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;QAC1E9c,MAAM,CAACU,QAAQ,EAAE,CAAA;QACjB,MAAMyc,kBAAkB,GAAG,EAAE,CAAA;QAC7B1C,QAAQ,CAACpD,OAAO,CAACvM,OAAO,CAAC,CAAChJ,KAAK,EAAEL,GAAG,KAAK;AACrC0b,UAAAA,kBAAkB,CAAC1b,GAAG,CAAC,GAAGK,KAAK,CAAA;AACnC,SAAC,CAAC,CAAA;AACF9B,QAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,sCAAA,CAAwC,CAAC,CAAA;QAC/DT,MAAM,CAACM,GAAG,CAAC,CAAA,iBAAA,EAAoBma,QAAQ,CAACnV,MAAM,EAAE,CAAC,CAAA;AACjDtF,QAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,kBAAA,CAAoB,GAAG0B,IAAI,CAACC,SAAS,CAACkb,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;QAC9End,MAAM,CAACU,QAAQ,EAAE,CAAA;AACjBV,QAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,gCAAA,CAAkC,CAAC,CAAA;AACzDT,QAAAA,MAAM,CAACM,GAAG,CAACma,QAAQ,CAACpD,OAAO,CAAC,CAAA;AAC5BrX,QAAAA,MAAM,CAACM,GAAG,CAACma,QAAQ,CAAC,CAAA;QACpBza,MAAM,CAACU,QAAQ,EAAE,CAAA;QACjBV,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,OAAA;AACJ,KAAA;AACA,IAAA,OAAOuc,SAAS,CAAA;AACpB,GAAA;AACJ;;ACrHA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,uBAAuB,CAAC;AAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIhX,WAAWA,CAAC+O,MAAM,EAAE;AAChB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkI,eAAe,GAAG,OAAO;AAAE5C,MAAAA,QAAAA;AAAS,KAAC,KAAK;MAC3C,IAAI,IAAI,CAAC6C,kBAAkB,CAACP,mBAAmB,CAACtC,QAAQ,CAAC,EAAE;AACvD,QAAA,OAAOA,QAAQ,CAAA;AACnB,OAAA;AACA,MAAA,OAAO,IAAI,CAAA;KACd,CAAA;AACD,IAAA,IAAI,CAAC6C,kBAAkB,GAAG,IAAIX,iBAAiB,CAACxH,MAAM,CAAC,CAAA;AAC3D,GAAA;AACJ;;AC9CA;AACA;AACA;AACA;AACA;AACA;AACA;AAEO,MAAMoI,sBAAsB,GAAG;AAClC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,eAAe,EAAE,OAAO;AAAE5C,IAAAA,QAAAA;AAAS,GAAC,KAAK;IACrC,IAAIA,QAAQ,CAACnV,MAAM,KAAK,GAAG,IAAImV,QAAQ,CAACnV,MAAM,KAAK,CAAC,EAAE;AAClD,MAAA,OAAOmV,QAAQ,CAAA;AACnB,KAAA;AACA,IAAA,OAAO,IAAI,CAAA;AACf,GAAA;AACJ,CAAC;;ACzBD;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+C,oBAAoB,SAAS1B,QAAQ,CAAC;AACxC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI1V,EAAAA,WAAWA,CAAC8S,OAAO,GAAG,EAAE,EAAE;IACtB,KAAK,CAACA,OAAO,CAAC,CAAA;AACd;AACA;AACA,IAAA,IAAI,CAAC,IAAI,CAACO,OAAO,CAACrL,IAAI,CAAEqP,CAAC,IAAK,iBAAiB,IAAIA,CAAC,CAAC,EAAE;AACnD,MAAA,IAAI,CAAChE,OAAO,CAACiE,OAAO,CAACH,sBAAsB,CAAC,CAAA;AAChD,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAMnB,OAAOA,CAACjT,OAAO,EAAE9B,OAAO,EAAE;IAC5B,MAAMqV,IAAI,GAAG,EAAE,CAAA;IAC4B;AACvCpV,MAAAA,kBAAM,CAACX,UAAU,CAACwC,OAAO,EAAEY,OAAO,EAAE;AAChC5H,QAAAA,UAAU,EAAE,oBAAoB;AAChCC,QAAAA,SAAS,EAAE,IAAI,CAACgE,WAAW,CAAC1C,IAAI;AAChCrB,QAAAA,QAAQ,EAAE,QAAQ;AAClBT,QAAAA,SAAS,EAAE,SAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;IACA,MAAM+b,oBAAoB,GAAGtW,OAAO,CAACsT,gBAAgB,CAACxR,OAAO,CAAC,CAACgC,KAAK,CAAC,MAAM;AACvE;AACA;AAAA,KACH,CAAC,CAAA;AACF,IAAA,KAAK9D,OAAO,CAAC2C,SAAS,CAAC2T,oBAAoB,CAAC,CAAA;IAC5C,IAAIlD,QAAQ,GAAG,MAAMpT,OAAO,CAACyT,UAAU,CAAC3R,OAAO,CAAC,CAAA;AAChD,IAAA,IAAI3I,KAAK,CAAA;AACT,IAAA,IAAIia,QAAQ,EAAE;MACiC;QACvCiC,IAAI,CAAC/R,IAAI,CAAC,CAAmC,gCAAA,EAAA,IAAI,CAACjF,SAAS,CAAA,CAAA,CAAG,GAC1D,CAAA,gEAAA,CAAkE,CAAC,CAAA;AAC3E,OAAA;AACJ,KAAC,MACI;MAC0C;QACvCgX,IAAI,CAAC/R,IAAI,CAAC,CAA6B,0BAAA,EAAA,IAAI,CAACjF,SAAS,CAAA,SAAA,CAAW,GAC5D,CAAA,mCAAA,CAAqC,CAAC,CAAA;AAC9C,OAAA;MACA,IAAI;AACA;AACA;QACA+U,QAAQ,GAAI,MAAMkD,oBAAqB,CAAA;OAC1C,CACD,OAAO3S,GAAG,EAAE;QACR,IAAIA,GAAG,YAAYjJ,KAAK,EAAE;AACtBvB,UAAAA,KAAK,GAAGwK,GAAG,CAAA;AACf,SAAA;AACJ,OAAA;AACJ,KAAA;IAC2C;AACvChL,MAAAA,MAAM,CAACS,cAAc,CAACiB,QAAQ,CAAC4a,aAAa,CAAC,IAAI,CAAClW,WAAW,CAAC1C,IAAI,EAAEyF,OAAO,CAAC,CAAC,CAAA;AAC7E,MAAA,KAAK,MAAM7I,GAAG,IAAIoc,IAAI,EAAE;AACpB1c,QAAAA,MAAM,CAACM,GAAG,CAACA,GAAG,CAAC,CAAA;AACnB,OAAA;AACAoB,MAAAA,QAAQ,CAAC8a,kBAAkB,CAAC/B,QAAQ,CAAC,CAAA;MACrCza,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,KAAA;IACA,IAAI,CAAC+Z,QAAQ,EAAE;AACX,MAAA,MAAM,IAAItU,YAAY,CAAC,aAAa,EAAE;QAAElB,GAAG,EAAEkE,OAAO,CAAClE,GAAG;AAAEzE,QAAAA,KAAAA;AAAM,OAAC,CAAC,CAAA;AACtE,KAAA;AACA,IAAA,OAAOia,QAAQ,CAAA;AACnB,GAAA;AACJ;;ACpHA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmD,YAAY,SAAS9B,QAAQ,CAAC;AAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI1V,EAAAA,WAAWA,CAAC8S,OAAO,GAAG,EAAE,EAAE;IACtB,KAAK,CAACA,OAAO,CAAC,CAAA;AACd;AACA;AACA,IAAA,IAAI,CAAC,IAAI,CAACO,OAAO,CAACrL,IAAI,CAAEqP,CAAC,IAAK,iBAAiB,IAAIA,CAAC,CAAC,EAAE;AACnD,MAAA,IAAI,CAAChE,OAAO,CAACiE,OAAO,CAACH,sBAAsB,CAAC,CAAA;AAChD,KAAA;AACA,IAAA,IAAI,CAACM,sBAAsB,GAAG3E,OAAO,CAAC4E,qBAAqB,IAAI,CAAC,CAAA;IACrB;MACvC,IAAI,IAAI,CAACD,sBAAsB,EAAE;QAC7BvW,kBAAM,CAACZ,MAAM,CAAC,IAAI,CAACmX,sBAAsB,EAAE,QAAQ,EAAE;AACjD1b,UAAAA,UAAU,EAAE,oBAAoB;AAChCC,UAAAA,SAAS,EAAE,IAAI,CAACgE,WAAW,CAAC1C,IAAI;AAChCrB,UAAAA,QAAQ,EAAE,aAAa;AACvBT,UAAAA,SAAS,EAAE,uBAAA;AACf,SAAC,CAAC,CAAA;AACN,OAAA;AACJ,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAMwa,OAAOA,CAACjT,OAAO,EAAE9B,OAAO,EAAE;IAC5B,MAAMqV,IAAI,GAAG,EAAE,CAAA;IAC4B;AACvCpV,MAAAA,kBAAM,CAACX,UAAU,CAACwC,OAAO,EAAEY,OAAO,EAAE;AAChC5H,QAAAA,UAAU,EAAE,oBAAoB;AAChCC,QAAAA,SAAS,EAAE,IAAI,CAACgE,WAAW,CAAC1C,IAAI;AAChCrB,QAAAA,QAAQ,EAAE,QAAQ;AAClBT,QAAAA,SAAS,EAAE,aAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;IACA,MAAMmc,QAAQ,GAAG,EAAE,CAAA;AACnB,IAAA,IAAIC,SAAS,CAAA;IACb,IAAI,IAAI,CAACH,sBAAsB,EAAE;MAC7B,MAAM;QAAE3J,EAAE;AAAEtG,QAAAA,OAAAA;AAAQ,OAAC,GAAG,IAAI,CAACqQ,kBAAkB,CAAC;QAAE9U,OAAO;QAAEuT,IAAI;AAAErV,QAAAA,OAAAA;AAAQ,OAAC,CAAC,CAAA;AAC3E2W,MAAAA,SAAS,GAAG9J,EAAE,CAAA;AACd6J,MAAAA,QAAQ,CAACpT,IAAI,CAACiD,OAAO,CAAC,CAAA;AAC1B,KAAA;AACA,IAAA,MAAMsQ,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC;MAC3CH,SAAS;MACT7U,OAAO;MACPuT,IAAI;AACJrV,MAAAA,OAAAA;AACJ,KAAC,CAAC,CAAA;AACF0W,IAAAA,QAAQ,CAACpT,IAAI,CAACuT,cAAc,CAAC,CAAA;IAC7B,MAAMzD,QAAQ,GAAG,MAAMpT,OAAO,CAAC2C,SAAS,CAAC,CAAC,YAAY;AAClD;AACA,MAAA,OAAQ,CAAC,MAAM3C,OAAO,CAAC2C,SAAS,CAACJ,OAAO,CAACwU,IAAI,CAACL,QAAQ,CAAC,CAAC;AACpD;AACA;AACA;AACA;AACA;AACC,MAAA,MAAMG,cAAc,CAAC,CAAA;KAC7B,GAAG,CAAC,CAAA;IACsC;AACvCle,MAAAA,MAAM,CAACS,cAAc,CAACiB,QAAQ,CAAC4a,aAAa,CAAC,IAAI,CAAClW,WAAW,CAAC1C,IAAI,EAAEyF,OAAO,CAAC,CAAC,CAAA;AAC7E,MAAA,KAAK,MAAM7I,GAAG,IAAIoc,IAAI,EAAE;AACpB1c,QAAAA,MAAM,CAACM,GAAG,CAACA,GAAG,CAAC,CAAA;AACnB,OAAA;AACAoB,MAAAA,QAAQ,CAAC8a,kBAAkB,CAAC/B,QAAQ,CAAC,CAAA;MACrCza,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,KAAA;IACA,IAAI,CAAC+Z,QAAQ,EAAE;AACX,MAAA,MAAM,IAAItU,YAAY,CAAC,aAAa,EAAE;QAAElB,GAAG,EAAEkE,OAAO,CAAClE,GAAAA;AAAI,OAAC,CAAC,CAAA;AAC/D,KAAA;AACA,IAAA,OAAOwV,QAAQ,CAAA;AACnB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIwD,EAAAA,kBAAkBA,CAAC;IAAE9U,OAAO;IAAEuT,IAAI;AAAErV,IAAAA,OAAAA;AAAS,GAAC,EAAE;AAC5C,IAAA,IAAI2W,SAAS,CAAA;AACb,IAAA,MAAMK,cAAc,GAAG,IAAIzU,OAAO,CAAE8F,OAAO,IAAK;AAC5C,MAAA,MAAM4O,gBAAgB,GAAG,YAAY;QACU;UACvC5B,IAAI,CAAC/R,IAAI,CAAC,CAAqC,mCAAA,CAAA,GAC3C,GAAG,IAAI,CAACkT,sBAAsB,CAAA,SAAA,CAAW,CAAC,CAAA;AAClD,SAAA;QACAnO,OAAO,CAAC,MAAMrI,OAAO,CAACyT,UAAU,CAAC3R,OAAO,CAAC,CAAC,CAAA;OAC7C,CAAA;MACD6U,SAAS,GAAGnF,UAAU,CAACyF,gBAAgB,EAAE,IAAI,CAACT,sBAAsB,GAAG,IAAI,CAAC,CAAA;AAChF,KAAC,CAAC,CAAA;IACF,OAAO;AACHjQ,MAAAA,OAAO,EAAEyQ,cAAc;AACvBnK,MAAAA,EAAE,EAAE8J,SAAAA;KACP,CAAA;AACL,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAMG,kBAAkBA,CAAC;IAAEH,SAAS;IAAE7U,OAAO;IAAEuT,IAAI;AAAErV,IAAAA,OAAAA;AAAS,GAAC,EAAE;AAC7D,IAAA,IAAI7G,KAAK,CAAA;AACT,IAAA,IAAIia,QAAQ,CAAA;IACZ,IAAI;AACAA,MAAAA,QAAQ,GAAG,MAAMpT,OAAO,CAACsT,gBAAgB,CAACxR,OAAO,CAAC,CAAA;KACrD,CACD,OAAOoV,UAAU,EAAE;MACf,IAAIA,UAAU,YAAYxc,KAAK,EAAE;AAC7BvB,QAAAA,KAAK,GAAG+d,UAAU,CAAA;AACtB,OAAA;AACJ,KAAA;AACA,IAAA,IAAIP,SAAS,EAAE;MACXQ,YAAY,CAACR,SAAS,CAAC,CAAA;AAC3B,KAAA;IAC2C;AACvC,MAAA,IAAIvD,QAAQ,EAAE;AACViC,QAAAA,IAAI,CAAC/R,IAAI,CAAC,CAAA,0BAAA,CAA4B,CAAC,CAAA;AAC3C,OAAC,MACI;AACD+R,QAAAA,IAAI,CAAC/R,IAAI,CAAC,CAA0D,wDAAA,CAAA,GAChE,yBAAyB,CAAC,CAAA;AAClC,OAAA;AACJ,KAAA;AACA,IAAA,IAAInK,KAAK,IAAI,CAACia,QAAQ,EAAE;AACpBA,MAAAA,QAAQ,GAAG,MAAMpT,OAAO,CAACyT,UAAU,CAAC3R,OAAO,CAAC,CAAA;MACD;AACvC,QAAA,IAAIsR,QAAQ,EAAE;UACViC,IAAI,CAAC/R,IAAI,CAAC,CAAmC,gCAAA,EAAA,IAAI,CAACjF,SAAS,CAAA,CAAA,CAAG,GAAG,CAAA,OAAA,CAAS,CAAC,CAAA;AAC/E,SAAC,MACI;UACDgX,IAAI,CAAC/R,IAAI,CAAC,CAAA,0BAAA,EAA6B,IAAI,CAACjF,SAAS,UAAU,CAAC,CAAA;AACpE,SAAA;AACJ,OAAA;AACJ,KAAA;AACA,IAAA,OAAO+U,QAAQ,CAAA;AACnB,GAAA;AACJ;;AClMA;AACA,IAAI;AACA5a,EAAAA,IAAI,CAAC,+BAA+B,CAAC,IAAIC,CAAC,EAAE,CAAA;AAChD,CAAC,CACD,OAAOC,CAAC,EAAE;;ACLV;AACA;AACA;AACA;AACA;AACA;AACA;AAGA,MAAM0e,UAAU,GAAG,CAAC,CAAA;AACpB,MAAMxL,OAAO,GAAG,yBAAyB,CAAA;AACzC,MAAMyL,yBAAyB,GAAG,UAAU,CAAA;AAC5C,MAAMC,gBAAgB,GAAG,WAAW,CAAA;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,OAAO,CAAC;AACjBxY,EAAAA,WAAWA,GAAG;IACV,IAAI,CAACmN,GAAG,GAAG,IAAI,CAAA;AACnB,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI,MAAMsL,QAAQA,CAAC9b,KAAK,EAAE;AAClB,IAAA,MAAMmP,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;IAC7B,MAAMpE,EAAE,GAAGkC,EAAE,CAACrB,WAAW,CAAC6N,yBAAyB,EAAE,WAAW,EAAE;AAC9DrK,MAAAA,UAAU,EAAE,SAAA;AAChB,KAAC,CAAC,CAAA;AACF,IAAA,MAAMrE,EAAE,CAAC6C,KAAK,CAAC5E,GAAG,CAAClL,KAAK,CAAC,CAAA;IACzB,MAAMiN,EAAE,CAACC,IAAI,CAAA;AACjB,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI,MAAM6O,eAAeA,GAAG;AACpB,IAAA,MAAM5M,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;AAC7B,IAAA,MAAMO,MAAM,GAAG,MAAMzC,EAAE,CAClBrB,WAAW,CAAC6N,yBAAyB,CAAC,CACtC7L,KAAK,CAAC+B,UAAU,EAAE,CAAA;AACvB,IAAA,OAAOD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC7S,KAAK,CAACoS,EAAE,CAAA;AAC1E,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAM6K,wBAAwBA,CAACC,SAAS,EAAE;AACtC,IAAA,MAAM9M,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;AAC7B,IAAA,MAAM6K,OAAO,GAAG,MAAM/M,EAAE,CAACgN,eAAe,CAACR,yBAAyB,EAAEC,gBAAgB,EAAEQ,WAAW,CAACC,IAAI,CAACJ,SAAS,CAAC,CAAC,CAAA;AAClH,IAAA,OAAOC,OAAO,GAAGA,OAAO,GAAG,IAAI1Y,KAAK,EAAE,CAAA;AAC1C,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAM8Y,wBAAwBA,CAACL,SAAS,EAAE;AACtC,IAAA,MAAM9M,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;AAC7B,IAAA,OAAOlC,EAAE,CAACoN,cAAc,CAACZ,yBAAyB,EAAEC,gBAAgB,EAAEQ,WAAW,CAACC,IAAI,CAACJ,SAAS,CAAC,CAAC,CAAA;AACtG,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI,MAAMO,WAAWA,CAACrL,EAAE,EAAE;AAClB,IAAA,MAAMhC,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;AAC7B,IAAA,MAAMlC,EAAE,CAAC8C,MAAM,CAAC0J,yBAAyB,EAAExK,EAAE,CAAC,CAAA;AAClD,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI,MAAMsL,wBAAwBA,CAACR,SAAS,EAAE;AACtC,IAAA,OAAO,MAAM,IAAI,CAACS,oBAAoB,CAACN,WAAW,CAACC,IAAI,CAACJ,SAAS,CAAC,EAAE,MAAM,CAAC,CAAA;AAC/E,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI,MAAMU,uBAAuBA,CAACV,SAAS,EAAE;AACrC,IAAA,OAAO,MAAM,IAAI,CAACS,oBAAoB,CAACN,WAAW,CAACC,IAAI,CAACJ,SAAS,CAAC,EAAE,MAAM,CAAC,CAAA;AAC/E,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAMS,oBAAoBA,CAACE,KAAK,EAAEC,SAAS,EAAE;AACzC,IAAA,MAAM1N,EAAE,GAAG,MAAM,IAAI,CAACkC,KAAK,EAAE,CAAA;IAC7B,MAAMO,MAAM,GAAG,MAAMzC,EAAE,CAClBrB,WAAW,CAAC6N,yBAAyB,CAAC,CACtC7L,KAAK,CAAC1K,KAAK,CAACwW,gBAAgB,CAAC,CAC7B/J,UAAU,CAAC+K,KAAK,EAAEC,SAAS,CAAC,CAAA;AACjC,IAAA,OAAOjL,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC7S,KAAK,CAAA;AACvE,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI,MAAMsS,KAAKA,GAAG;AACV,IAAA,IAAI,CAAC,IAAI,CAACb,GAAG,EAAE;MACX,IAAI,CAACA,GAAG,GAAG,MAAMhC,MAAM,CAAC0B,OAAO,EAAEwL,UAAU,EAAE;QACzC/M,OAAO,EAAE,IAAI,CAAC+B,UAAAA;AAClB,OAAC,CAAC,CAAA;AACN,KAAA;IACA,OAAO,IAAI,CAACF,GAAG,CAAA;AACnB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACIE,EAAAA,UAAUA,CAACvB,EAAE,EAAEF,UAAU,EAAE;AACvB,IAAA,IAAIA,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAGyM,UAAU,EAAE;MAC3C,IAAIvM,EAAE,CAAC1B,gBAAgB,CAACqP,QAAQ,CAACnB,yBAAyB,CAAC,EAAE;AACzDxM,QAAAA,EAAE,CAAC4N,iBAAiB,CAACpB,yBAAyB,CAAC,CAAA;AACnD,OAAA;AACJ,KAAA;AACA,IAAA,MAAMhL,QAAQ,GAAGxB,EAAE,CAACyB,iBAAiB,CAAC+K,yBAAyB,EAAE;AAC7DqB,MAAAA,aAAa,EAAE,IAAI;AACnBnM,MAAAA,OAAO,EAAE,IAAA;AACb,KAAC,CAAC,CAAA;AACFF,IAAAA,QAAQ,CAACG,WAAW,CAAC8K,gBAAgB,EAAEA,gBAAgB,EAAE;AAAE7K,MAAAA,MAAM,EAAE,KAAA;AAAM,KAAC,CAAC,CAAA;AAC/E,GAAA;AACJ;;AChJA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMkM,UAAU,CAAC;AACpB;AACJ;AACA;AACA;AACA;AACA;EACI5Z,WAAWA,CAAC4Y,SAAS,EAAE;IACnB,IAAI,CAACiB,UAAU,GAAGjB,SAAS,CAAA;AAC3B,IAAA,IAAI,CAACkB,QAAQ,GAAG,IAAItB,OAAO,EAAE,CAAA;AACjC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMuB,SAASA,CAACpd,KAAK,EAAE;IACwB;AACvCuE,MAAAA,kBAAM,CAACZ,MAAM,CAAC3D,KAAK,EAAE,QAAQ,EAAE;AAC3BZ,QAAAA,UAAU,EAAE,yBAAyB;AACrCC,QAAAA,SAAS,EAAE,YAAY;AACvBC,QAAAA,QAAQ,EAAE,WAAW;AACrBT,QAAAA,SAAS,EAAE,OAAA;AACf,OAAC,CAAC,CAAA;MACF0F,kBAAM,CAACZ,MAAM,CAAC3D,KAAK,CAACqd,WAAW,EAAE,QAAQ,EAAE;AACvCje,QAAAA,UAAU,EAAE,yBAAyB;AACrCC,QAAAA,SAAS,EAAE,YAAY;AACvBC,QAAAA,QAAQ,EAAE,WAAW;AACrBT,QAAAA,SAAS,EAAE,mBAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;AACA;IACA,OAAOmB,KAAK,CAACmR,EAAE,CAAA;AACfnR,IAAAA,KAAK,CAACic,SAAS,GAAG,IAAI,CAACiB,UAAU,CAAA;AACjC,IAAA,MAAM,IAAI,CAACC,QAAQ,CAACrB,QAAQ,CAAC9b,KAAK,CAAC,CAAA;AACvC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMsd,YAAYA,CAACtd,KAAK,EAAE;IACqB;AACvCuE,MAAAA,kBAAM,CAACZ,MAAM,CAAC3D,KAAK,EAAE,QAAQ,EAAE;AAC3BZ,QAAAA,UAAU,EAAE,yBAAyB;AACrCC,QAAAA,SAAS,EAAE,YAAY;AACvBC,QAAAA,QAAQ,EAAE,cAAc;AACxBT,QAAAA,SAAS,EAAE,OAAA;AACf,OAAC,CAAC,CAAA;MACF0F,kBAAM,CAACZ,MAAM,CAAC3D,KAAK,CAACqd,WAAW,EAAE,QAAQ,EAAE;AACvCje,QAAAA,UAAU,EAAE,yBAAyB;AACrCC,QAAAA,SAAS,EAAE,YAAY;AACvBC,QAAAA,QAAQ,EAAE,cAAc;AACxBT,QAAAA,SAAS,EAAE,mBAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;IACA,MAAM0e,OAAO,GAAG,MAAM,IAAI,CAACJ,QAAQ,CAACpB,eAAe,EAAE,CAAA;AACrD,IAAA,IAAIwB,OAAO,EAAE;AACT;AACAvd,MAAAA,KAAK,CAACmR,EAAE,GAAGoM,OAAO,GAAG,CAAC,CAAA;AAC1B,KAAC,MACI;AACD;MACA,OAAOvd,KAAK,CAACmR,EAAE,CAAA;AACnB,KAAA;AACAnR,IAAAA,KAAK,CAACic,SAAS,GAAG,IAAI,CAACiB,UAAU,CAAA;AACjC,IAAA,MAAM,IAAI,CAACC,QAAQ,CAACrB,QAAQ,CAAC9b,KAAK,CAAC,CAAA;AACvC,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI,MAAMwd,QAAQA,GAAG;AACb,IAAA,OAAO,IAAI,CAACC,YAAY,CAAC,MAAM,IAAI,CAACN,QAAQ,CAACR,uBAAuB,CAAC,IAAI,CAACO,UAAU,CAAC,CAAC,CAAA;AAC1F,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI,MAAMQ,UAAUA,GAAG;AACf,IAAA,OAAO,IAAI,CAACD,YAAY,CAAC,MAAM,IAAI,CAACN,QAAQ,CAACV,wBAAwB,CAAC,IAAI,CAACS,UAAU,CAAC,CAAC,CAAA;AAC3F,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMS,MAAMA,GAAG;IACX,OAAO,MAAM,IAAI,CAACR,QAAQ,CAACnB,wBAAwB,CAAC,IAAI,CAACkB,UAAU,CAAC,CAAA;AACxE,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMpb,IAAIA,GAAG;IACT,OAAO,MAAM,IAAI,CAACqb,QAAQ,CAACb,wBAAwB,CAAC,IAAI,CAACY,UAAU,CAAC,CAAA;AACxE,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMV,WAAWA,CAACrL,EAAE,EAAE;AAClB,IAAA,MAAM,IAAI,CAACgM,QAAQ,CAACX,WAAW,CAACrL,EAAE,CAAC,CAAA;AACvC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMsM,YAAYA,CAACzd,KAAK,EAAE;AACtB,IAAA,IAAIA,KAAK,EAAE;AACP,MAAA,MAAM,IAAI,CAACwc,WAAW,CAACxc,KAAK,CAACmR,EAAE,CAAC,CAAA;AACpC,KAAA;AACA,IAAA,OAAOnR,KAAK,CAAA;AAChB,GAAA;AACJ;;ACvJA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA,MAAM4d,sBAAsB,GAAG,CAC3B,QAAQ,EACR,UAAU,EACV,gBAAgB,EAChB,MAAM,EACN,aAAa,EACb,OAAO,EACP,UAAU,EACV,WAAW,EACX,WAAW,CACd,CAAA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;AAClB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,aAAaC,WAAWA,CAAC1X,OAAO,EAAE;AAC9B,IAAA,MAAMiX,WAAW,GAAG;MAChBnb,GAAG,EAAEkE,OAAO,CAAClE,GAAG;AAChBoS,MAAAA,OAAO,EAAE,EAAC;KACb,CAAA;AACD;AACA,IAAA,IAAIlO,OAAO,CAACvI,MAAM,KAAK,KAAK,EAAE;AAC1B;AACA;AACA;AACA;AACAwf,MAAAA,WAAW,CAACU,IAAI,GAAG,MAAM3X,OAAO,CAACgR,KAAK,EAAE,CAAC4G,WAAW,EAAE,CAAA;AAC1D,KAAA;AACA;AACA,IAAA,KAAK,MAAM,CAACtf,GAAG,EAAEK,KAAK,CAAC,IAAIqH,OAAO,CAACkO,OAAO,CAAC2J,OAAO,EAAE,EAAE;AAClDZ,MAAAA,WAAW,CAAC/I,OAAO,CAAC5V,GAAG,CAAC,GAAGK,KAAK,CAAA;AACpC,KAAA;AACA;AACA,IAAA,KAAK,MAAMwO,IAAI,IAAIqQ,sBAAsB,EAAE;AACvC,MAAA,IAAIxX,OAAO,CAACmH,IAAI,CAAC,KAAK/E,SAAS,EAAE;AAC7B6U,QAAAA,WAAW,CAAC9P,IAAI,CAAC,GAAGnH,OAAO,CAACmH,IAAI,CAAC,CAAA;AACrC,OAAA;AACJ,KAAA;AACA,IAAA,OAAO,IAAIsQ,eAAe,CAACR,WAAW,CAAC,CAAA;AAC3C,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIha,WAAWA,CAACga,WAAW,EAAE;IACsB;AACvC9Y,MAAAA,kBAAM,CAACZ,MAAM,CAAC0Z,WAAW,EAAE,QAAQ,EAAE;AACjCje,QAAAA,UAAU,EAAE,yBAAyB;AACrCC,QAAAA,SAAS,EAAE,iBAAiB;AAC5BC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,aAAA;AACf,OAAC,CAAC,CAAA;MACF0F,kBAAM,CAACZ,MAAM,CAAC0Z,WAAW,CAACnb,GAAG,EAAE,QAAQ,EAAE;AACrC9C,QAAAA,UAAU,EAAE,yBAAyB;AACrCC,QAAAA,SAAS,EAAE,iBAAiB;AAC5BC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,iBAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;AACA;AACA;AACA,IAAA,IAAIwe,WAAW,CAAC,MAAM,CAAC,KAAK,UAAU,EAAE;AACpCA,MAAAA,WAAW,CAAC,MAAM,CAAC,GAAG,aAAa,CAAA;AACvC,KAAA;IACA,IAAI,CAACa,YAAY,GAAGb,WAAW,CAAA;AACnC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACIc,EAAAA,QAAQA,GAAG;AACP,IAAA,MAAMd,WAAW,GAAG7e,MAAM,CAAC6W,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC6I,YAAY,CAAC,CAAA;AACxDb,IAAAA,WAAW,CAAC/I,OAAO,GAAG9V,MAAM,CAAC6W,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC6I,YAAY,CAAC5J,OAAO,CAAC,CAAA;IAClE,IAAI+I,WAAW,CAACU,IAAI,EAAE;MAClBV,WAAW,CAACU,IAAI,GAAGV,WAAW,CAACU,IAAI,CAACzY,KAAK,CAAC,CAAC,CAAC,CAAA;AAChD,KAAA;AACA,IAAA,OAAO+X,WAAW,CAAA;AACtB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACItH,EAAAA,SAASA,GAAG;AACR,IAAA,OAAO,IAAI/O,OAAO,CAAC,IAAI,CAACkX,YAAY,CAAChc,GAAG,EAAE,IAAI,CAACgc,YAAY,CAAC,CAAA;AAChE,GAAA;AACA;AACJ;AACA;AACA;AACA;AACI9G,EAAAA,KAAKA,GAAG;IACJ,OAAO,IAAIyG,eAAe,CAAC,IAAI,CAACM,QAAQ,EAAE,CAAC,CAAA;AAC/C,GAAA;AACJ;;ACvHA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA,MAAMC,UAAU,GAAG,yBAAyB,CAAA;AAC5C,MAAMC,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACvC,MAAMC,UAAU,GAAG,IAAIvT,GAAG,EAAE,CAAA;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwT,YAAY,GAAIC,eAAe,IAAK;AACtC,EAAA,MAAMC,UAAU,GAAG;IACfrY,OAAO,EAAE,IAAIyX,eAAe,CAACW,eAAe,CAACnB,WAAW,CAAC,CAACtH,SAAS,EAAE;IACrE7E,SAAS,EAAEsN,eAAe,CAACtN,SAAAA;GAC9B,CAAA;EACD,IAAIsN,eAAe,CAACE,QAAQ,EAAE;AAC1BD,IAAAA,UAAU,CAACC,QAAQ,GAAGF,eAAe,CAACE,QAAQ,CAAA;AAClD,GAAA;AACA,EAAA,OAAOD,UAAU,CAAA;AACrB,CAAC,CAAA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,KAAK,CAAC;AACR;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACItb,WAAWA,CAAC1C,IAAI,EAAE;IAAEie,iBAAiB;IAAEC,MAAM;AAAEC,IAAAA,gBAAAA;GAAkB,GAAG,EAAE,EAAE;IACpE,IAAI,CAACC,eAAe,GAAG,KAAK,CAAA;IAC5B,IAAI,CAACC,wBAAwB,GAAG,KAAK,CAAA;AACrC;AACA,IAAA,IAAIV,UAAU,CAACzW,GAAG,CAAClH,IAAI,CAAC,EAAE;AACtB,MAAA,MAAM,IAAIyC,YAAY,CAAC,sBAAsB,EAAE;AAAEzC,QAAAA,IAAAA;AAAK,OAAC,CAAC,CAAA;AAC5D,KAAC,MACI;AACD2d,MAAAA,UAAU,CAACpT,GAAG,CAACvK,IAAI,CAAC,CAAA;AACxB,KAAA;IACA,IAAI,CAACse,KAAK,GAAGte,IAAI,CAAA;AACjB,IAAA,IAAI,CAACue,OAAO,GAAGL,MAAM,IAAI,IAAI,CAACM,cAAc,CAAA;AAC5C,IAAA,IAAI,CAACC,iBAAiB,GAAGN,gBAAgB,IAAIT,kBAAkB,CAAA;AAC/D,IAAA,IAAI,CAACgB,kBAAkB,GAAGC,OAAO,CAACV,iBAAiB,CAAC,CAAA;IACpD,IAAI,CAACW,WAAW,GAAG,IAAItC,UAAU,CAAC,IAAI,CAACgC,KAAK,CAAC,CAAA;IAC7C,IAAI,CAACO,gBAAgB,EAAE,CAAA;AAC3B,GAAA;AACA;AACJ;AACA;EACI,IAAI7e,IAAIA,GAAG;IACP,OAAO,IAAI,CAACse,KAAK,CAAA;AACrB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMQ,WAAWA,CAACzf,KAAK,EAAE;IACsB;AACvCuE,MAAAA,kBAAM,CAACZ,MAAM,CAAC3D,KAAK,EAAE,QAAQ,EAAE;AAC3BZ,QAAAA,UAAU,EAAE,yBAAyB;AACrCC,QAAAA,SAAS,EAAE,OAAO;AAClBC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,OAAA;AACf,OAAC,CAAC,CAAA;MACF0F,kBAAM,CAACX,UAAU,CAAC5D,KAAK,CAACoG,OAAO,EAAEY,OAAO,EAAE;AACtC5H,QAAAA,UAAU,EAAE,yBAAyB;AACrCC,QAAAA,SAAS,EAAE,OAAO;AAClBC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,eAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;AACA,IAAA,MAAM,IAAI,CAAC6gB,WAAW,CAAC1f,KAAK,EAAE,MAAM,CAAC,CAAA;AACzC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAM2f,cAAcA,CAAC3f,KAAK,EAAE;IACmB;AACvCuE,MAAAA,kBAAM,CAACZ,MAAM,CAAC3D,KAAK,EAAE,QAAQ,EAAE;AAC3BZ,QAAAA,UAAU,EAAE,yBAAyB;AACrCC,QAAAA,SAAS,EAAE,OAAO;AAClBC,QAAAA,QAAQ,EAAE,gBAAgB;AAC1BT,QAAAA,SAAS,EAAE,OAAA;AACf,OAAC,CAAC,CAAA;MACF0F,kBAAM,CAACX,UAAU,CAAC5D,KAAK,CAACoG,OAAO,EAAEY,OAAO,EAAE;AACtC5H,QAAAA,UAAU,EAAE,yBAAyB;AACrCC,QAAAA,SAAS,EAAE,OAAO;AAClBC,QAAAA,QAAQ,EAAE,gBAAgB;AAC1BT,QAAAA,SAAS,EAAE,eAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;AACA,IAAA,MAAM,IAAI,CAAC6gB,WAAW,CAAC1f,KAAK,EAAE,SAAS,CAAC,CAAA;AAC5C,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAM4f,UAAUA,GAAG;AACf,IAAA,OAAO,IAAI,CAACC,cAAc,CAAC,KAAK,CAAC,CAAA;AACrC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,YAAYA,GAAG;AACjB,IAAA,OAAO,IAAI,CAACD,cAAc,CAAC,OAAO,CAAC,CAAA;AACvC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMlC,MAAMA,GAAG;IACX,MAAMoC,UAAU,GAAG,MAAM,IAAI,CAACR,WAAW,CAAC5B,MAAM,EAAE,CAAA;AAClD,IAAA,MAAM5K,GAAG,GAAGD,IAAI,CAACC,GAAG,EAAE,CAAA;IACtB,MAAMiN,gBAAgB,GAAG,EAAE,CAAA;AAC3B,IAAA,KAAK,MAAMhgB,KAAK,IAAI+f,UAAU,EAAE;AAC5B;AACA;MACA,MAAME,oBAAoB,GAAG,IAAI,CAACb,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAA;AAC/D,MAAA,IAAIrM,GAAG,GAAG/S,KAAK,CAACkR,SAAS,GAAG+O,oBAAoB,EAAE;QAC9C,MAAM,IAAI,CAACV,WAAW,CAAC/C,WAAW,CAACxc,KAAK,CAACmR,EAAE,CAAC,CAAA;AAChD,OAAC,MACI;AACD6O,QAAAA,gBAAgB,CAACpY,IAAI,CAAC2W,YAAY,CAACve,KAAK,CAAC,CAAC,CAAA;AAC9C,OAAA;AACJ,KAAA;AACA,IAAA,OAAOggB,gBAAgB,CAAA;AAC3B,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMle,IAAIA,GAAG;AACT,IAAA,OAAO,MAAM,IAAI,CAACyd,WAAW,CAACzd,IAAI,EAAE,CAAA;AACxC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAM4d,WAAWA,CAAC;IAAEtZ,OAAO;IAAEsY,QAAQ;AAAExN,IAAAA,SAAS,GAAG4B,IAAI,CAACC,GAAG,EAAC;GAAG,EAAEmN,SAAS,EAAE;AACxE,IAAA,MAAMC,eAAe,GAAG,MAAMtC,eAAe,CAACC,WAAW,CAAC1X,OAAO,CAACgR,KAAK,EAAE,CAAC,CAAA;AAC1E,IAAA,MAAMpX,KAAK,GAAG;AACVqd,MAAAA,WAAW,EAAE8C,eAAe,CAAChC,QAAQ,EAAE;AACvCjN,MAAAA,SAAAA;KACH,CAAA;AACD;AACA,IAAA,IAAIwN,QAAQ,EAAE;MACV1e,KAAK,CAAC0e,QAAQ,GAAGA,QAAQ,CAAA;AAC7B,KAAA;AACA,IAAA,QAAQwB,SAAS;AACb,MAAA,KAAK,MAAM;AACP,QAAA,MAAM,IAAI,CAACX,WAAW,CAACnC,SAAS,CAACpd,KAAK,CAAC,CAAA;AACvC,QAAA,MAAA;AACJ,MAAA,KAAK,SAAS;AACV,QAAA,MAAM,IAAI,CAACuf,WAAW,CAACjC,YAAY,CAACtd,KAAK,CAAC,CAAA;AAC1C,QAAA,MAAA;AACR,KAAA;IAC2C;AACvC/C,MAAAA,MAAM,CAACM,GAAG,CAAC,CAAgBgI,aAAAA,EAAAA,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,QAAQ,GAC1D,CAAA,qCAAA,EAAwC,IAAI,CAAC+c,KAAK,IAAI,CAAC,CAAA;AAC/D,KAAA;AACA;AACA;AACA;IACA,IAAI,IAAI,CAACF,eAAe,EAAE;MACtB,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAAA;AACxC,KAAC,MACI;AACD,MAAA,MAAM,IAAI,CAACoB,YAAY,EAAE,CAAA;AAC7B,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMP,cAAcA,CAACK,SAAS,EAAE;AAC5B,IAAA,MAAMnN,GAAG,GAAGD,IAAI,CAACC,GAAG,EAAE,CAAA;AACtB,IAAA,IAAI/S,KAAK,CAAA;AACT,IAAA,QAAQkgB,SAAS;AACb,MAAA,KAAK,KAAK;QACNlgB,KAAK,GAAG,MAAM,IAAI,CAACuf,WAAW,CAAC/B,QAAQ,EAAE,CAAA;AACzC,QAAA,MAAA;AACJ,MAAA,KAAK,OAAO;QACRxd,KAAK,GAAG,MAAM,IAAI,CAACuf,WAAW,CAAC7B,UAAU,EAAE,CAAA;AAC3C,QAAA,MAAA;AACR,KAAA;AACA,IAAA,IAAI1d,KAAK,EAAE;AACP;AACA;MACA,MAAMigB,oBAAoB,GAAG,IAAI,CAACb,iBAAiB,GAAG,EAAE,GAAG,IAAI,CAAA;AAC/D,MAAA,IAAIrM,GAAG,GAAG/S,KAAK,CAACkR,SAAS,GAAG+O,oBAAoB,EAAE;AAC9C,QAAA,OAAO,IAAI,CAACJ,cAAc,CAACK,SAAS,CAAC,CAAA;AACzC,OAAA;MACA,OAAO3B,YAAY,CAACve,KAAK,CAAC,CAAA;AAC9B,KAAC,MACI;AACD,MAAA,OAAOwI,SAAS,CAAA;AACpB,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI,MAAM2W,cAAcA,GAAG;AACnB,IAAA,IAAInf,KAAK,CAAA;IACT,OAAQA,KAAK,GAAG,MAAM,IAAI,CAAC8f,YAAY,EAAE,EAAG;MACxC,IAAI;QACA,MAAMjJ,KAAK,CAAC7W,KAAK,CAACoG,OAAO,CAACgR,KAAK,EAAE,CAAC,CAAA;QAClC,IAAI,aAAoB,KAAK,YAAY,EAAE;AACvCna,UAAAA,MAAM,CAACM,GAAG,CAAC,gBAAgBgI,cAAc,CAACvF,KAAK,CAACoG,OAAO,CAAClE,GAAG,CAAC,IAAI,GAC5D,CAAA,4BAAA,EAA+B,IAAI,CAAC+c,KAAK,GAAG,CAAC,CAAA;AACrD,SAAA;OACH,CACD,OAAOxhB,KAAK,EAAE;AACV,QAAA,MAAM,IAAI,CAACkiB,cAAc,CAAC3f,KAAK,CAAC,CAAA;QACW;AACvC/C,UAAAA,MAAM,CAACM,GAAG,CAAC,gBAAgBgI,cAAc,CAACvF,KAAK,CAACoG,OAAO,CAAClE,GAAG,CAAC,IAAI,GAC5D,CAAA,4CAAA,EAA+C,IAAI,CAAC+c,KAAK,GAAG,CAAC,CAAA;AACrE,SAAA;AACA,QAAA,MAAM,IAAI7b,YAAY,CAAC,qBAAqB,EAAE;UAAEzC,IAAI,EAAE,IAAI,CAACse,KAAAA;AAAM,SAAC,CAAC,CAAA;AACvE,OAAA;AACJ,KAAA;IAC2C;MACvChiB,MAAM,CAACM,GAAG,CAAC,CAA0B,uBAAA,EAAA,IAAI,CAACoD,IAAI,CAAA,oBAAA,CAAsB,GAChE,CAAA,iCAAA,CAAmC,CAAC,CAAA;AAC5C,KAAA;AACJ,GAAA;AACA;AACJ;AACA;EACI,MAAMyf,YAAYA,GAAG;AACjB;IACA,IAAI,MAAM,IAAItjB,IAAI,CAACgN,YAAY,IAAI,CAAC,IAAI,CAACuV,kBAAkB,EAAE;MACzD,IAAI;AACA,QAAA,MAAMviB,IAAI,CAACgN,YAAY,CAACuW,IAAI,CAACC,QAAQ,CAAC,CAAA,EAAGlC,UAAU,CAAI,CAAA,EAAA,IAAI,CAACa,KAAK,EAAE,CAAC,CAAA;OACvE,CACD,OAAOhX,GAAG,EAAE;AACR;AACA;QAC2C;UACvChL,MAAM,CAACO,IAAI,CAAC,CAAsC,mCAAA,EAAA,IAAI,CAACyhB,KAAK,CAAA,EAAA,CAAI,EAAEhX,GAAG,CAAC,CAAA;AAC1E,SAAA;AACJ,OAAA;AACJ,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACIuX,EAAAA,gBAAgBA,GAAG;AACf;IACA,IAAI,MAAM,IAAI1iB,IAAI,CAACgN,YAAY,IAAI,CAAC,IAAI,CAACuV,kBAAkB,EAAE;AACzDviB,MAAAA,IAAI,CAACoJ,gBAAgB,CAAC,MAAM,EAAGC,KAAK,IAAK;QACrC,IAAIA,KAAK,CAACoa,GAAG,KAAK,CAAA,EAAGnC,UAAU,CAAA,CAAA,EAAI,IAAI,CAACa,KAAK,CAAA,CAAE,EAAE;UACF;YACvChiB,MAAM,CAACM,GAAG,CAAC,CAA4B4I,yBAAAA,EAAAA,KAAK,CAACoa,GAAG,CAAA,EAAA,CAAI,GAAG,CAAA,iBAAA,CAAmB,CAAC,CAAA;AAC/E,WAAA;AACA,UAAA,MAAMC,YAAY,GAAG,YAAY;YAC7B,IAAI,CAACzB,eAAe,GAAG,IAAI,CAAA;AAC3B,YAAA,IAAI0B,SAAS,CAAA;YACb,IAAI;cACA,MAAM,IAAI,CAACvB,OAAO,CAAC;AAAEwB,gBAAAA,KAAK,EAAE,IAAA;AAAK,eAAC,CAAC,CAAA;aACtC,CACD,OAAOjjB,KAAK,EAAE;cACV,IAAIA,KAAK,YAAYuB,KAAK,EAAE;AACxByhB,gBAAAA,SAAS,GAAGhjB,KAAK,CAAA;AACjB;AACA;AACA,gBAAA,MAAMgjB,SAAS,CAAA;AACnB,eAAA;AACJ,aAAC,SACO;AACJ;AACA;AACA;AACA;AACA;AACA,cAAA,IAAI,IAAI,CAACzB,wBAAwB,IAC7B,EAAEyB,SAAS,IAAI,CAACta,KAAK,CAACwa,UAAU,CAAC,EAAE;AACnC,gBAAA,MAAM,IAAI,CAACP,YAAY,EAAE,CAAA;AAC7B,eAAA;cACA,IAAI,CAACrB,eAAe,GAAG,KAAK,CAAA;cAC5B,IAAI,CAACC,wBAAwB,GAAG,KAAK,CAAA;AACzC,aAAA;WACH,CAAA;AACD7Y,UAAAA,KAAK,CAACc,SAAS,CAACuZ,YAAY,EAAE,CAAC,CAAA;AACnC,SAAA;AACJ,OAAC,CAAC,CAAA;AACN,KAAC,MACI;MAC0C;AACvCvjB,QAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,uDAAA,CAAyD,CAAC,CAAA;AACzE,OAAA;AACA;AACA;AACA;MACA,KAAK,IAAI,CAAC2hB,OAAO,CAAC;AAAEwB,QAAAA,KAAK,EAAE,IAAA;AAAK,OAAC,CAAC,CAAA;AACtC,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,WAAWE,WAAWA,GAAG;AACrB,IAAA,OAAOtC,UAAU,CAAA;AACrB,GAAA;AACJ;;AC/YA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuC,oBAAoB,CAAC;AACvB;AACJ;AACA;AACA;AACA;AACA;AACA;AACIxd,EAAAA,WAAWA,CAAC1C,IAAI,EAAEwV,OAAO,EAAE;AACvB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC2K,YAAY,GAAG,OAAO;AAAE1a,MAAAA,OAAAA;AAAQ,KAAC,KAAK;AACvC,MAAA,MAAM,IAAI,CAAC2a,MAAM,CAACtB,WAAW,CAAC;AAAErZ,QAAAA,OAAAA;AAAQ,OAAC,CAAC,CAAA;KAC7C,CAAA;IACD,IAAI,CAAC2a,MAAM,GAAG,IAAIpC,KAAK,CAAChe,IAAI,EAAEwV,OAAO,CAAC,CAAA;AAC1C,GAAA;AACJ;;AClCA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6K,YAAYA,GAAG;AACpBlkB,EAAAA,IAAI,CAACoJ,gBAAgB,CAAC,UAAU,EAAE,MAAMpJ,IAAI,CAACmkB,OAAO,CAACC,KAAK,EAAE,CAAC,CAAA;AACjE;;AChBA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASja,SAASA,CAACd,KAAK,EAAEgb,OAAO,EAAE;AAC/B,EAAA,MAAMC,aAAa,GAAGD,OAAO,EAAE,CAAA;AAC/Bhb,EAAAA,KAAK,CAACc,SAAS,CAACma,aAAa,CAAC,CAAA;AAC9B,EAAA,OAAOA,aAAa,CAAA;AACxB;;ACnBA;AACA,IAAI;AACAtkB,EAAAA,IAAI,CAAC,0BAA0B,CAAC,IAAIC,CAAC,EAAE,CAAA;AAC3C,CAAC,CACD,OAAOC,CAAC,EAAE;;ACLV;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA,MAAMqkB,qBAAqB,GAAG,iBAAiB,CAAA;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,cAAcA,CAACthB,KAAK,EAAE;EAClC,IAAI,CAACA,KAAK,EAAE;AACR,IAAA,MAAM,IAAIoD,YAAY,CAAC,mCAAmC,EAAE;AAAEpD,MAAAA,KAAAA;AAAM,KAAC,CAAC,CAAA;AAC1E,GAAA;AACA;AACA;AACA,EAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3B,MAAMuhB,SAAS,GAAG,IAAI9b,GAAG,CAACzF,KAAK,EAAEmF,QAAQ,CAACD,IAAI,CAAC,CAAA;IAC/C,OAAO;MACHsQ,QAAQ,EAAE+L,SAAS,CAACrc,IAAI;MACxBhD,GAAG,EAAEqf,SAAS,CAACrc,IAAAA;KAClB,CAAA;AACL,GAAA;EACA,MAAM;IAAEsc,QAAQ;AAAEtf,IAAAA,GAAAA;AAAI,GAAC,GAAGlC,KAAK,CAAA;EAC/B,IAAI,CAACkC,GAAG,EAAE;AACN,IAAA,MAAM,IAAIkB,YAAY,CAAC,mCAAmC,EAAE;AAAEpD,MAAAA,KAAAA;AAAM,KAAC,CAAC,CAAA;AAC1E,GAAA;AACA;AACA;EACA,IAAI,CAACwhB,QAAQ,EAAE;IACX,MAAMD,SAAS,GAAG,IAAI9b,GAAG,CAACvD,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;IAC7C,OAAO;MACHsQ,QAAQ,EAAE+L,SAAS,CAACrc,IAAI;MACxBhD,GAAG,EAAEqf,SAAS,CAACrc,IAAAA;KAClB,CAAA;AACL,GAAA;AACA;AACA;EACA,MAAMuc,WAAW,GAAG,IAAIhc,GAAG,CAACvD,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;EAC/C,MAAMwc,WAAW,GAAG,IAAIjc,GAAG,CAACvD,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;EAC/Cuc,WAAW,CAACxM,YAAY,CAACvM,GAAG,CAAC2Y,qBAAqB,EAAEG,QAAQ,CAAC,CAAA;EAC7D,OAAO;IACHhM,QAAQ,EAAEiM,WAAW,CAACvc,IAAI;IAC1BhD,GAAG,EAAEwf,WAAW,CAACxc,IAAAA;GACpB,CAAA;AACL;;ACvDA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyc,2BAA2B,CAAC;AAC9Bte,EAAAA,WAAWA,GAAG;IACV,IAAI,CAACue,WAAW,GAAG,EAAE,CAAA;IACrB,IAAI,CAACC,cAAc,GAAG,EAAE,CAAA;IACxB,IAAI,CAACC,gBAAgB,GAAG,OAAO;MAAE1b,OAAO;AAAEqS,MAAAA,KAAAA;AAAO,KAAC,KAAK;AACnD;AACA,MAAA,IAAIA,KAAK,EAAE;QACPA,KAAK,CAACvB,eAAe,GAAG9Q,OAAO,CAAA;AACnC,OAAA;KACH,CAAA;IACD,IAAI,CAACoN,wBAAwB,GAAG,OAAO;MAAErN,KAAK;MAAEsS,KAAK;AAAEhF,MAAAA,cAAAA;AAAgB,KAAC,KAAK;AACzE,MAAA,IAAItN,KAAK,CAACpD,IAAI,KAAK,SAAS,EAAE;QAC1B,IAAI0V,KAAK,IACLA,KAAK,CAACvB,eAAe,IACrBuB,KAAK,CAACvB,eAAe,YAAYlQ,OAAO,EAAE;AAC1C;AACA,UAAA,MAAM9E,GAAG,GAAGuW,KAAK,CAACvB,eAAe,CAAChV,GAAG,CAAA;AACrC,UAAA,IAAIuR,cAAc,EAAE;AAChB,YAAA,IAAI,CAACoO,cAAc,CAACja,IAAI,CAAC1F,GAAG,CAAC,CAAA;AACjC,WAAC,MACI;AACD,YAAA,IAAI,CAAC0f,WAAW,CAACha,IAAI,CAAC1F,GAAG,CAAC,CAAA;AAC9B,WAAA;AACJ,SAAA;AACJ,OAAA;AACA,MAAA,OAAOuR,cAAc,CAAA;KACxB,CAAA;AACL,GAAA;AACJ;;AC1CA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsO,sBAAsB,CAAC;AACzB1e,EAAAA,WAAWA,CAAC;AAAE2e,IAAAA,kBAAAA;AAAmB,GAAC,EAAE;IAChC,IAAI,CAACC,kBAAkB,GAAG,OAAO;MAAE7b,OAAO;AAAEoB,MAAAA,MAAAA;AAAQ,KAAC,KAAK;AACtD;AACA;AACA,MAAA,MAAMgO,QAAQ,GAAG,CAAChO,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACgO,QAAQ,KAC7E,IAAI,CAAC0M,mBAAmB,CAACC,iBAAiB,CAAC/b,OAAO,CAAClE,GAAG,CAAC,CAAA;AAC3D;AACA,MAAA,OAAOsT,QAAQ,GACT,IAAIxO,OAAO,CAACwO,QAAQ,EAAE;QAAElB,OAAO,EAAElO,OAAO,CAACkO,OAAAA;OAAS,CAAC,GACnDlO,OAAO,CAAA;KAChB,CAAA;IACD,IAAI,CAAC8b,mBAAmB,GAAGF,kBAAkB,CAAA;AACjD,GAAA;AACJ;;AC5BA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,QAAQ,GAAGA,CAACC,UAAU,EAAEC,WAAW,KAAK;AAC1CrlB,EAAAA,MAAM,CAACS,cAAc,CAAC2kB,UAAU,CAAC,CAAA;AACjC,EAAA,KAAK,MAAMngB,GAAG,IAAIogB,WAAW,EAAE;AAC3BrlB,IAAAA,MAAM,CAACM,GAAG,CAAC2E,GAAG,CAAC,CAAA;AACnB,GAAA;EACAjF,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,CAAC,CAAA;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS4kB,mBAAmBA,CAACD,WAAW,EAAE;AAC7C,EAAA,MAAME,aAAa,GAAGF,WAAW,CAAC/Z,MAAM,CAAA;EACxC,IAAIia,aAAa,GAAG,CAAC,EAAE;AACnBvlB,IAAAA,MAAM,CAACS,cAAc,CAAC,6BAA6B,GAC/C,CAAA,EAAG8kB,aAAa,CAAU,QAAA,CAAA,GAC1B,CAAUA,OAAAA,EAAAA,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG,QAAQ,WAAW,CAAC,CAAA;AACjEJ,IAAAA,QAAQ,CAAC,wBAAwB,EAAEE,WAAW,CAAC,CAAA;IAC/CrlB,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,GAAA;AACJ;;ACrCA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8kB,YAAYA,CAACJ,UAAU,EAAEK,IAAI,EAAE;AACpC,EAAA,IAAIA,IAAI,CAACna,MAAM,KAAK,CAAC,EAAE;AACnB,IAAA,OAAA;AACJ,GAAA;AACAtL,EAAAA,MAAM,CAACS,cAAc,CAAC2kB,UAAU,CAAC,CAAA;AACjC,EAAA,KAAK,MAAMngB,GAAG,IAAIwgB,IAAI,EAAE;AACpBzlB,IAAAA,MAAM,CAACM,GAAG,CAAC2E,GAAG,CAAC,CAAA;AACnB,GAAA;EACAjF,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASglB,mBAAmBA,CAACC,cAAc,EAAEC,oBAAoB,EAAE;AACtE,EAAA,MAAMC,cAAc,GAAGF,cAAc,CAACra,MAAM,CAAA;AAC5C,EAAA,MAAMwa,qBAAqB,GAAGF,oBAAoB,CAACta,MAAM,CAAA;EACzD,IAAIua,cAAc,IAAIC,qBAAqB,EAAE;AACzC,IAAA,IAAI1gB,OAAO,GAAG,CAAcygB,WAAAA,EAAAA,cAAc,CAAQA,KAAAA,EAAAA,cAAc,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAG,CAAA,CAAA,CAAA;IACpF,IAAIC,qBAAqB,GAAG,CAAC,EAAE;AAC3B1gB,MAAAA,OAAO,IACH,CAAA,CAAA,EAAI0gB,qBAAqB,CAAA,CAAA,CAAG,GACxB,CAAA,IAAA,EAAOA,qBAAqB,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO,CAAkB,gBAAA,CAAA,CAAA;AAClF,KAAA;AACA9lB,IAAAA,MAAM,CAACS,cAAc,CAAC2E,OAAO,CAAC,CAAA;AAC9BogB,IAAAA,YAAY,CAAC,CAAA,0BAAA,CAA4B,EAAEG,cAAc,CAAC,CAAA;AAC1DH,IAAAA,YAAY,CAAC,CAAA,+BAAA,CAAiC,EAAEI,oBAAoB,CAAC,CAAA;IACrE5lB,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,GAAA;AACJ;;AC/CA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAIqlB,aAAa,CAAA;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kCAAkCA,GAAG;EAC1C,IAAID,aAAa,KAAKxa,SAAS,EAAE;AAC7B,IAAA,MAAM0a,YAAY,GAAG,IAAIjJ,QAAQ,CAAC,EAAE,CAAC,CAAA;IACrC,IAAI,MAAM,IAAIiJ,YAAY,EAAE;MACxB,IAAI;AACA,QAAA,IAAIjJ,QAAQ,CAACiJ,YAAY,CAACnF,IAAI,CAAC,CAAA;AAC/BiF,QAAAA,aAAa,GAAG,IAAI,CAAA;OACvB,CACD,OAAOvlB,KAAK,EAAE;AACVulB,QAAAA,aAAa,GAAG,KAAK,CAAA;AACzB,OAAA;AACJ,KAAA;AACAA,IAAAA,aAAa,GAAG,KAAK,CAAA;AACzB,GAAA;AACA,EAAA,OAAOA,aAAa,CAAA;AACxB;;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeG,YAAYA,CAACzL,QAAQ,EAAE0L,QAAQ,EAAE;EAC5C,IAAIvgB,MAAM,GAAG,IAAI,CAAA;AACjB;EACA,IAAI6U,QAAQ,CAACxV,GAAG,EAAE;IACd,MAAMmhB,WAAW,GAAG,IAAI5d,GAAG,CAACiS,QAAQ,CAACxV,GAAG,CAAC,CAAA;IACzCW,MAAM,GAAGwgB,WAAW,CAACxgB,MAAM,CAAA;AAC/B,GAAA;AACA,EAAA,IAAIA,MAAM,KAAK/F,IAAI,CAACqI,QAAQ,CAACtC,MAAM,EAAE;AACjC,IAAA,MAAM,IAAIO,YAAY,CAAC,4BAA4B,EAAE;AAAEP,MAAAA,MAAAA;AAAO,KAAC,CAAC,CAAA;AACpE,GAAA;AACA,EAAA,MAAMygB,cAAc,GAAG5L,QAAQ,CAACN,KAAK,EAAE,CAAA;AACvC;AACA,EAAA,MAAMmM,YAAY,GAAG;AACjBjP,IAAAA,OAAO,EAAE,IAAIkP,OAAO,CAACF,cAAc,CAAChP,OAAO,CAAC;IAC5C/R,MAAM,EAAE+gB,cAAc,CAAC/gB,MAAM;IAC7BkhB,UAAU,EAAEH,cAAc,CAACG,UAAAA;GAC9B,CAAA;AACD;EACA,MAAMC,oBAAoB,GAAGN,QAAQ,GAAGA,QAAQ,CAACG,YAAY,CAAC,GAAGA,YAAY,CAAA;AAC7E;AACA;AACA;AACA,EAAA,MAAMxF,IAAI,GAAGkF,kCAAkC,EAAE,GAC3CK,cAAc,CAACvF,IAAI,GACnB,MAAMuF,cAAc,CAACK,IAAI,EAAE,CAAA;AACjC,EAAA,OAAO,IAAI1J,QAAQ,CAAC8D,IAAI,EAAE2F,oBAAoB,CAAC,CAAA;AACnD;;ACvDA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,gBAAgB,SAAS7K,QAAQ,CAAC;AACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI1V,EAAAA,WAAWA,CAAC8S,OAAO,GAAG,EAAE,EAAE;IACtBA,OAAO,CAACxT,SAAS,GAAGyH,UAAU,CAACI,eAAe,CAAC2L,OAAO,CAACxT,SAAS,CAAC,CAAA;IACjE,KAAK,CAACwT,OAAO,CAAC,CAAA;IACd,IAAI,CAAC0N,kBAAkB,GACnB1N,OAAO,CAAC2N,iBAAiB,KAAK,KAAK,GAAG,KAAK,GAAG,IAAI,CAAA;AACtD;AACA;AACA;AACA;IACA,IAAI,CAACpN,OAAO,CAAC9O,IAAI,CAACgc,gBAAgB,CAACG,sCAAsC,CAAC,CAAA;AAC9E,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACI,EAAA,MAAM1K,OAAOA,CAACjT,OAAO,EAAE9B,OAAO,EAAE;IAC5B,MAAMoT,QAAQ,GAAG,MAAMpT,OAAO,CAACyT,UAAU,CAAC3R,OAAO,CAAC,CAAA;AAClD,IAAA,IAAIsR,QAAQ,EAAE;AACV,MAAA,OAAOA,QAAQ,CAAA;AACnB,KAAA;AACA;AACA;IACA,IAAIpT,OAAO,CAAC6B,KAAK,IAAI7B,OAAO,CAAC6B,KAAK,CAACpD,IAAI,KAAK,SAAS,EAAE;MACnD,OAAO,MAAM,IAAI,CAACihB,cAAc,CAAC5d,OAAO,EAAE9B,OAAO,CAAC,CAAA;AACtD,KAAA;AACA;AACA;IACA,OAAO,MAAM,IAAI,CAAC2f,YAAY,CAAC7d,OAAO,EAAE9B,OAAO,CAAC,CAAA;AACpD,GAAA;AACA,EAAA,MAAM2f,YAAYA,CAAC7d,OAAO,EAAE9B,OAAO,EAAE;AACjC,IAAA,IAAIoT,QAAQ,CAAA;AACZ,IAAA,MAAMlQ,MAAM,GAAIlD,OAAO,CAACkD,MAAM,IAAI,EAAG,CAAA;AACrC;IACA,IAAI,IAAI,CAACqc,kBAAkB,EAAE;MACkB;AACvC5mB,QAAAA,MAAM,CAACO,IAAI,CAAC,6BAA6B,GACrC,CAAA,EAAG+H,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,OAAO,IAAI,CAACS,SAAS,CAAW,SAAA,CAAA,GAC9D,qCAAqC,CAAC,CAAA;AAC9C,OAAA;AACA,MAAA,MAAMuhB,mBAAmB,GAAG1c,MAAM,CAAC2c,SAAS,CAAA;AAC5C,MAAA,MAAMC,kBAAkB,GAAGhe,OAAO,CAAC+d,SAAS,CAAA;AAC5C,MAAA,MAAME,mBAAmB,GAAG,CAACD,kBAAkB,IAAIA,kBAAkB,KAAKF,mBAAmB,CAAA;AAC7F;AACA;MACAxM,QAAQ,GAAG,MAAMpT,OAAO,CAACuS,KAAK,CAAC,IAAI7P,OAAO,CAACZ,OAAO,EAAE;QAChD+d,SAAS,EAAE/d,OAAO,CAAC0Q,IAAI,KAAK,SAAS,GAC/BsN,kBAAkB,IAAIF,mBAAmB,GACzC1b,SAAAA;AACV,OAAC,CAAC,CAAC,CAAA;AACH;AACA;AACA;AACA;AACA;AACA;AACA;MACA,IAAI0b,mBAAmB,IACnBG,mBAAmB,IACnBje,OAAO,CAAC0Q,IAAI,KAAK,SAAS,EAAE;QAC5B,IAAI,CAACwN,qCAAqC,EAAE,CAAA;AAC5C,QAAA,MAAMC,SAAS,GAAG,MAAMjgB,OAAO,CAACwT,QAAQ,CAAC1R,OAAO,EAAEsR,QAAQ,CAACN,KAAK,EAAE,CAAC,CAAA;QACxB;AACvC,UAAA,IAAImN,SAAS,EAAE;AACXtnB,YAAAA,MAAM,CAACM,GAAG,CAAC,CAAA,eAAA,EAAkBgI,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAG,CAAA,CAAA,GACvD,oCAAoC,CAAC,CAAA;AAC7C,WAAA;AACJ,SAAA;AACJ,OAAA;AACJ,KAAC,MACI;AACD;AACA;AACA,MAAA,MAAM,IAAIkB,YAAY,CAAC,wBAAwB,EAAE;QAC7CT,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBT,GAAG,EAAEkE,OAAO,CAAClE,GAAAA;AACjB,OAAC,CAAC,CAAA;AACN,KAAA;IAC2C;AACvC,MAAA,MAAMsT,QAAQ,GAAGhO,MAAM,CAACgO,QAAQ,KAAK,MAAMlR,OAAO,CAAC2T,WAAW,CAAC7R,OAAO,EAAE,MAAM,CAAC,CAAC,CAAA;AAChF;AACA;MACAnJ,MAAM,CAACS,cAAc,CAAC,CAA+B,6BAAA,CAAA,GAAG6H,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAC,CAAA;AACpFjF,MAAAA,MAAM,CAACM,GAAG,CAAC,CAA8BgI,2BAAAA,EAAAA,cAAc,CAACiQ,QAAQ,YAAYxO,OAAO,GAAGwO,QAAQ,CAACtT,GAAG,GAAGsT,QAAQ,CAAC,EAAE,CAAC,CAAA;AACjHvY,MAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,0BAAA,CAA4B,CAAC,CAAA;AACnDT,MAAAA,MAAM,CAACM,GAAG,CAAC6I,OAAO,CAAC,CAAA;MACnBnJ,MAAM,CAACU,QAAQ,EAAE,CAAA;AACjBV,MAAAA,MAAM,CAACS,cAAc,CAAC,CAAA,2BAAA,CAA6B,CAAC,CAAA;AACpDT,MAAAA,MAAM,CAACM,GAAG,CAACma,QAAQ,CAAC,CAAA;MACpBza,MAAM,CAACU,QAAQ,EAAE,CAAA;MACjBV,MAAM,CAACU,QAAQ,EAAE,CAAA;AACrB,KAAA;AACA,IAAA,OAAO+Z,QAAQ,CAAA;AACnB,GAAA;AACA,EAAA,MAAMsM,cAAcA,CAAC5d,OAAO,EAAE9B,OAAO,EAAE;IACnC,IAAI,CAACggB,qCAAqC,EAAE,CAAA;IAC5C,MAAM5M,QAAQ,GAAG,MAAMpT,OAAO,CAACuS,KAAK,CAACzQ,OAAO,CAAC,CAAA;AAC7C;AACA;AACA,IAAA,MAAMme,SAAS,GAAG,MAAMjgB,OAAO,CAACwT,QAAQ,CAAC1R,OAAO,EAAEsR,QAAQ,CAACN,KAAK,EAAE,CAAC,CAAA;IACnE,IAAI,CAACmN,SAAS,EAAE;AACZ;AACA;AACA,MAAA,MAAM,IAAInhB,YAAY,CAAC,yBAAyB,EAAE;QAC9ClB,GAAG,EAAEkE,OAAO,CAAClE,GAAG;QAChBK,MAAM,EAAEmV,QAAQ,CAACnV,MAAAA;AACrB,OAAC,CAAC,CAAA;AACN,KAAA;AACA,IAAA,OAAOmV,QAAQ,CAAA;AACnB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACI4M,EAAAA,qCAAqCA,GAAG;IACpC,IAAIE,kBAAkB,GAAG,IAAI,CAAA;IAC7B,IAAIC,0BAA0B,GAAG,CAAC,CAAA;AAClC,IAAA,KAAK,MAAM,CAACrf,KAAK,EAAEwR,MAAM,CAAC,IAAI,IAAI,CAACF,OAAO,CAACuH,OAAO,EAAE,EAAE;AAClD;AACA,MAAA,IAAIrH,MAAM,KAAKgN,gBAAgB,CAACG,sCAAsC,EAAE;AACpE,QAAA,SAAA;AACJ,OAAA;AACA;AACA,MAAA,IAAInN,MAAM,KAAKgN,gBAAgB,CAACc,iCAAiC,EAAE;AAC/DF,QAAAA,kBAAkB,GAAGpf,KAAK,CAAA;AAC9B,OAAA;MACA,IAAIwR,MAAM,CAAC0D,eAAe,EAAE;AACxBmK,QAAAA,0BAA0B,EAAE,CAAA;AAChC,OAAA;AACJ,KAAA;IACA,IAAIA,0BAA0B,KAAK,CAAC,EAAE;MAClC,IAAI,CAAC/N,OAAO,CAAC9O,IAAI,CAACgc,gBAAgB,CAACc,iCAAiC,CAAC,CAAA;KACxE,MACI,IAAID,0BAA0B,GAAG,CAAC,IAAID,kBAAkB,KAAK,IAAI,EAAE;AACpE;MACA,IAAI,CAAC9N,OAAO,CAAC3N,MAAM,CAACyb,kBAAkB,EAAE,CAAC,CAAC,CAAA;AAC9C,KAAA;AACA;AACJ,GAAA;AACJ,CAAA;AACAZ,gBAAgB,CAACc,iCAAiC,GAAG;AACjD,EAAA,MAAMpK,eAAeA,CAAC;AAAE5C,IAAAA,QAAAA;AAAS,GAAC,EAAE;IAChC,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACnV,MAAM,IAAI,GAAG,EAAE;AACrC,MAAA,OAAO,IAAI,CAAA;AACf,KAAA;AACA,IAAA,OAAOmV,QAAQ,CAAA;AACnB,GAAA;AACJ,CAAC,CAAA;AACDkM,gBAAgB,CAACG,sCAAsC,GAAG;AACtD,EAAA,MAAMzJ,eAAeA,CAAC;AAAE5C,IAAAA,QAAAA;AAAS,GAAC,EAAE;IAChC,OAAOA,QAAQ,CAACiN,UAAU,GAAG,MAAMxB,YAAY,CAACzL,QAAQ,CAAC,GAAGA,QAAQ,CAAA;AACxE,GAAA;AACJ,CAAC;;AC7ND;AACA;AACA;AACA;AACA;AACA;AACA;AAaA;AACA;AACA;AACA;AACA;AACA,MAAMkN,kBAAkB,CAAC;AACrB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIvhB,EAAAA,WAAWA,CAAC;IAAEV,SAAS;AAAE+T,IAAAA,OAAO,GAAG,EAAE;AAAEoN,IAAAA,iBAAiB,GAAG,IAAA;GAAO,GAAG,EAAE,EAAE;AACrE,IAAA,IAAI,CAACe,gBAAgB,GAAG,IAAI/e,GAAG,EAAE,CAAA;AACjC,IAAA,IAAI,CAACgf,iBAAiB,GAAG,IAAIhf,GAAG,EAAE,CAAA;AAClC,IAAA,IAAI,CAACif,uBAAuB,GAAG,IAAIjf,GAAG,EAAE,CAAA;AACxC,IAAA,IAAI,CAACwQ,SAAS,GAAG,IAAIsN,gBAAgB,CAAC;AAClCjhB,MAAAA,SAAS,EAAEyH,UAAU,CAACI,eAAe,CAAC7H,SAAS,CAAC;AAChD+T,MAAAA,OAAO,EAAE,CACL,GAAGA,OAAO,EACV,IAAIqL,sBAAsB,CAAC;AAAEC,QAAAA,kBAAkB,EAAE,IAAA;AAAK,OAAC,CAAC,CAC3D;AACD8B,MAAAA,iBAAAA;AACJ,KAAC,CAAC,CAAA;AACF;IACA,IAAI,CAACkB,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC9S,IAAI,CAAC,IAAI,CAAC,CAAA;IACtC,IAAI,CAAC+S,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC/S,IAAI,CAAC,IAAI,CAAC,CAAA;AAC5C,GAAA;AACA;AACJ;AACA;AACA;EACI,IAAIgE,QAAQA,GAAG;IACX,OAAO,IAAI,CAACI,SAAS,CAAA;AACzB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5M,QAAQA,CAACuU,OAAO,EAAE;AACd,IAAA,IAAI,CAACiH,cAAc,CAACjH,OAAO,CAAC,CAAA;AAC5B,IAAA,IAAI,CAAC,IAAI,CAACkH,+BAA+B,EAAE;MACvCroB,IAAI,CAACoJ,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC8e,OAAO,CAAC,CAAA;MAC9CloB,IAAI,CAACoJ,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC+e,QAAQ,CAAC,CAAA;MAChD,IAAI,CAACE,+BAA+B,GAAG,IAAI,CAAA;AAC/C,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,cAAcA,CAACjH,OAAO,EAAE;IACuB;AACvC1Z,MAAAA,kBAAM,CAAChB,OAAO,CAAC0a,OAAO,EAAE;AACpB7e,QAAAA,UAAU,EAAE,oBAAoB;AAChCC,QAAAA,SAAS,EAAE,oBAAoB;AAC/BC,QAAAA,QAAQ,EAAE,gBAAgB;AAC1BT,QAAAA,SAAS,EAAE,SAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;IACA,MAAMumB,eAAe,GAAG,EAAE,CAAA;AAC1B,IAAA,KAAK,MAAMplB,KAAK,IAAIie,OAAO,EAAE;AACzB;AACA,MAAA,IAAI,OAAOje,KAAK,KAAK,QAAQ,EAAE;AAC3BolB,QAAAA,eAAe,CAACxd,IAAI,CAAC5H,KAAK,CAAC,CAAA;OAC9B,MACI,IAAIA,KAAK,IAAIA,KAAK,CAACwhB,QAAQ,KAAKhZ,SAAS,EAAE;AAC5C4c,QAAAA,eAAe,CAACxd,IAAI,CAAC5H,KAAK,CAACkC,GAAG,CAAC,CAAA;AACnC,OAAA;MACA,MAAM;QAAEsT,QAAQ;AAAEtT,QAAAA,GAAAA;AAAI,OAAC,GAAGof,cAAc,CAACthB,KAAK,CAAC,CAAA;AAC/C,MAAA,MAAMqlB,SAAS,GAAG,OAAOrlB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACwhB,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAA;AACpF,MAAA,IAAI,IAAI,CAACqD,gBAAgB,CAAChd,GAAG,CAAC3F,GAAG,CAAC,IAC9B,IAAI,CAAC2iB,gBAAgB,CAAC/c,GAAG,CAAC5F,GAAG,CAAC,KAAKsT,QAAQ,EAAE;AAC7C,QAAA,MAAM,IAAIpS,YAAY,CAAC,uCAAuC,EAAE;UAC5DlD,UAAU,EAAE,IAAI,CAAC2kB,gBAAgB,CAAC/c,GAAG,CAAC5F,GAAG,CAAC;AAC1C/B,UAAAA,WAAW,EAAEqV,QAAAA;AACjB,SAAC,CAAC,CAAA;AACN,OAAA;MACA,IAAI,OAAOxV,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACmkB,SAAS,EAAE;QAC9C,IAAI,IAAI,CAACY,uBAAuB,CAACld,GAAG,CAAC2N,QAAQ,CAAC,IAC1C,IAAI,CAACuP,uBAAuB,CAACjd,GAAG,CAAC0N,QAAQ,CAAC,KAAKxV,KAAK,CAACmkB,SAAS,EAAE;AAChE,UAAA,MAAM,IAAI/gB,YAAY,CAAC,2CAA2C,EAAE;AAChElB,YAAAA,GAAAA;AACJ,WAAC,CAAC,CAAA;AACN,SAAA;QACA,IAAI,CAAC6iB,uBAAuB,CAACrc,GAAG,CAAC8M,QAAQ,EAAExV,KAAK,CAACmkB,SAAS,CAAC,CAAA;AAC/D,OAAA;MACA,IAAI,CAACU,gBAAgB,CAACnc,GAAG,CAACxG,GAAG,EAAEsT,QAAQ,CAAC,CAAA;MACxC,IAAI,CAACsP,iBAAiB,CAACpc,GAAG,CAACxG,GAAG,EAAEmjB,SAAS,CAAC,CAAA;AAC1C,MAAA,IAAID,eAAe,CAAC7c,MAAM,GAAG,CAAC,EAAE;AAC5B,QAAA,MAAM+c,cAAc,GAAG,CAA8C,4CAAA,CAAA,GACjE,CAASF,MAAAA,EAAAA,eAAe,CAAC/mB,IAAI,CAAC,IAAI,CAAC,CAAA,8BAAA,CAAgC,GACnE,CAA0C,wCAAA,CAAA,CAAA;QAMzC;AACDpB,UAAAA,MAAM,CAACO,IAAI,CAAC8nB,cAAc,CAAC,CAAA;AAC/B,SAAA;AACJ,OAAA;AACJ,KAAA;AACJ,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,OAAOA,CAAC7e,KAAK,EAAE;AACX;AACA;AACA,IAAA,OAAOc,SAAS,CAACd,KAAK,EAAE,YAAY;AAChC,MAAA,MAAMof,mBAAmB,GAAG,IAAI5D,2BAA2B,EAAE,CAAA;MAC7D,IAAI,CAACzL,QAAQ,CAACQ,OAAO,CAAC9O,IAAI,CAAC2d,mBAAmB,CAAC,CAAA;AAC/C;AACA;MACA,KAAK,MAAM,CAACrjB,GAAG,EAAEsT,QAAQ,CAAC,IAAI,IAAI,CAACqP,gBAAgB,EAAE;QACjD,MAAMV,SAAS,GAAG,IAAI,CAACY,uBAAuB,CAACjd,GAAG,CAAC0N,QAAQ,CAAC,CAAA;QAC5D,MAAM6P,SAAS,GAAG,IAAI,CAACP,iBAAiB,CAAChd,GAAG,CAAC5F,GAAG,CAAC,CAAA;AACjD,QAAA,MAAMkE,OAAO,GAAG,IAAIY,OAAO,CAAC9E,GAAG,EAAE;UAC7BiiB,SAAS;AACTlR,UAAAA,KAAK,EAAEoS,SAAS;AAChBG,UAAAA,WAAW,EAAE,aAAA;AACjB,SAAC,CAAC,CAAA;QACF,MAAM3e,OAAO,CAACC,GAAG,CAAC,IAAI,CAACoP,QAAQ,CAAC+C,SAAS,CAAC;AACtCzR,UAAAA,MAAM,EAAE;AAAEgO,YAAAA,QAAAA;WAAU;UACpBpP,OAAO;AACPD,UAAAA,KAAAA;AACJ,SAAC,CAAC,CAAC,CAAA;AACP,OAAA;MACA,MAAM;QAAEyb,WAAW;AAAEC,QAAAA,cAAAA;AAAe,OAAC,GAAG0D,mBAAmB,CAAA;MAChB;AACvC5C,QAAAA,mBAAmB,CAACf,WAAW,EAAEC,cAAc,CAAC,CAAA;AACpD,OAAA;MACA,OAAO;QAAED,WAAW;AAAEC,QAAAA,cAAAA;OAAgB,CAAA;AAC1C,KAAC,CAAC,CAAA;AACN,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIoD,QAAQA,CAAC9e,KAAK,EAAE;AACZ;AACA;AACA,IAAA,OAAOc,SAAS,CAACd,KAAK,EAAE,YAAY;AAChC,MAAA,MAAM8M,KAAK,GAAG,MAAMnW,IAAI,CAACoW,MAAM,CAACnE,IAAI,CAAC,IAAI,CAACmH,QAAQ,CAACvT,SAAS,CAAC,CAAA;AAC7D,MAAA,MAAM8iB,uBAAuB,GAAG,MAAMxS,KAAK,CAACxU,IAAI,EAAE,CAAA;AAClD,MAAA,MAAMinB,iBAAiB,GAAG,IAAI3a,GAAG,CAAC,IAAI,CAAC8Z,gBAAgB,CAACc,MAAM,EAAE,CAAC,CAAA;MACjE,MAAMrD,WAAW,GAAG,EAAE,CAAA;AACtB,MAAA,KAAK,MAAMlc,OAAO,IAAIqf,uBAAuB,EAAE;QAC3C,IAAI,CAACC,iBAAiB,CAAC7d,GAAG,CAACzB,OAAO,CAAClE,GAAG,CAAC,EAAE;AACrC,UAAA,MAAM+Q,KAAK,CAAChB,MAAM,CAAC7L,OAAO,CAAC,CAAA;AAC3Bkc,UAAAA,WAAW,CAAC1a,IAAI,CAACxB,OAAO,CAAClE,GAAG,CAAC,CAAA;AACjC,SAAA;AACJ,OAAA;MAC2C;QACvCqgB,mBAAmB,CAACD,WAAW,CAAC,CAAA;AACpC,OAAA;MACA,OAAO;AAAEA,QAAAA,WAAAA;OAAa,CAAA;AAC1B,KAAC,CAAC,CAAA;AACN,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACIsD,EAAAA,kBAAkBA,GAAG;IACjB,OAAO,IAAI,CAACf,gBAAgB,CAAA;AAChC,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACIgB,EAAAA,aAAaA,GAAG;IACZ,OAAO,CAAC,GAAG,IAAI,CAAChB,gBAAgB,CAACpmB,IAAI,EAAE,CAAC,CAAA;AAC5C,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI0jB,iBAAiBA,CAACjgB,GAAG,EAAE;IACnB,MAAMqf,SAAS,GAAG,IAAI9b,GAAG,CAACvD,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;IAC7C,OAAO,IAAI,CAAC2f,gBAAgB,CAAC/c,GAAG,CAACyZ,SAAS,CAACrc,IAAI,CAAC,CAAA;AACpD,GAAA;AACA;AACJ;AACA;AACA;AACA;EACI4gB,uBAAuBA,CAACtQ,QAAQ,EAAE;AAC9B,IAAA,OAAO,IAAI,CAACuP,uBAAuB,CAACjd,GAAG,CAAC0N,QAAQ,CAAC,CAAA;AACrD,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMuQ,aAAaA,CAAC3f,OAAO,EAAE;IACzB,MAAMlE,GAAG,GAAGkE,OAAO,YAAYY,OAAO,GAAGZ,OAAO,CAAClE,GAAG,GAAGkE,OAAO,CAAA;AAC9D,IAAA,MAAMoP,QAAQ,GAAG,IAAI,CAAC2M,iBAAiB,CAACjgB,GAAG,CAAC,CAAA;AAC5C,IAAA,IAAIsT,QAAQ,EAAE;AACV,MAAA,MAAMvC,KAAK,GAAG,MAAMnW,IAAI,CAACoW,MAAM,CAACnE,IAAI,CAAC,IAAI,CAACmH,QAAQ,CAACvT,SAAS,CAAC,CAAA;AAC7D,MAAA,OAAOsQ,KAAK,CAACvO,KAAK,CAAC8Q,QAAQ,CAAC,CAAA;AAChC,KAAA;AACA,IAAA,OAAOhN,SAAS,CAAA;AACpB,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIwd,uBAAuBA,CAAC9jB,GAAG,EAAE;AACzB,IAAA,MAAMsT,QAAQ,GAAG,IAAI,CAAC2M,iBAAiB,CAACjgB,GAAG,CAAC,CAAA;IAC5C,IAAI,CAACsT,QAAQ,EAAE;AACX,MAAA,MAAM,IAAIpS,YAAY,CAAC,mBAAmB,EAAE;AAAElB,QAAAA,GAAAA;AAAI,OAAC,CAAC,CAAA;AACxD,KAAA;AACA,IAAA,OAAQiU,OAAO,IAAK;AAChBA,MAAAA,OAAO,CAAC/P,OAAO,GAAG,IAAIY,OAAO,CAAC9E,GAAG,CAAC,CAAA;AAClCiU,MAAAA,OAAO,CAAC3O,MAAM,GAAGhJ,MAAM,CAAC6W,MAAM,CAAC;AAAEG,QAAAA,QAAAA;AAAS,OAAC,EAAEW,OAAO,CAAC3O,MAAM,CAAC,CAAA;AAC5D,MAAA,OAAO,IAAI,CAAC0O,QAAQ,CAAC1R,MAAM,CAAC2R,OAAO,CAAC,CAAA;KACvC,CAAA;AACL,GAAA;AACJ;;AClSA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA,IAAI6L,kBAAkB,CAAA;AACtB;AACA;AACA;AACA;AACO,MAAMiE,6BAA6B,GAAGA,MAAM;EAC/C,IAAI,CAACjE,kBAAkB,EAAE;AACrBA,IAAAA,kBAAkB,GAAG,IAAI4C,kBAAkB,EAAE,CAAA;AACjD,GAAA;AACA,EAAA,OAAO5C,kBAAkB,CAAA;AAC7B,CAAC;;ACnBD;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASkE,yBAAyBA,CAAC3E,SAAS,EAAE4E,2BAA2B,GAAG,EAAE,EAAE;AACnF;AACA;AACA,EAAA,KAAK,MAAMtnB,SAAS,IAAI,CAAC,GAAG0iB,SAAS,CAACtM,YAAY,CAACxW,IAAI,EAAE,CAAC,EAAE;AACxD,IAAA,IAAI0nB,2BAA2B,CAAC9a,IAAI,CAAEvG,MAAM,IAAKA,MAAM,CAAC/G,IAAI,CAACc,SAAS,CAAC,CAAC,EAAE;AACtE0iB,MAAAA,SAAS,CAACtM,YAAY,CAAChD,MAAM,CAACpT,SAAS,CAAC,CAAA;AAC5C,KAAA;AACJ,GAAA;AACA,EAAA,OAAO0iB,SAAS,CAAA;AACpB;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,UAAU6E,qBAAqBA,CAAClkB,GAAG,EAAE;AAAEikB,EAAAA,2BAA2B,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC;AAAEE,EAAAA,cAAc,GAAG,YAAY;AAAEC,EAAAA,SAAS,GAAG,IAAI;AAAEC,EAAAA,eAAAA;AAAiB,CAAC,GAAG,EAAE,EAAE;EACzK,MAAMhF,SAAS,GAAG,IAAI9b,GAAG,CAACvD,GAAG,EAAEiD,QAAQ,CAACD,IAAI,CAAC,CAAA;EAC7Cqc,SAAS,CAACjR,IAAI,GAAG,EAAE,CAAA;EACnB,MAAMiR,SAAS,CAACrc,IAAI,CAAA;AACpB,EAAA,MAAMshB,uBAAuB,GAAGN,yBAAyB,CAAC3E,SAAS,EAAE4E,2BAA2B,CAAC,CAAA;EACjG,MAAMK,uBAAuB,CAACthB,IAAI,CAAA;EAClC,IAAImhB,cAAc,IAAIG,uBAAuB,CAACnd,QAAQ,CAACod,QAAQ,CAAC,GAAG,CAAC,EAAE;IAClE,MAAMC,YAAY,GAAG,IAAIjhB,GAAG,CAAC+gB,uBAAuB,CAACthB,IAAI,CAAC,CAAA;IAC1DwhB,YAAY,CAACrd,QAAQ,IAAIgd,cAAc,CAAA;IACvC,MAAMK,YAAY,CAACxhB,IAAI,CAAA;AAC3B,GAAA;AACA,EAAA,IAAIohB,SAAS,EAAE;IACX,MAAMK,QAAQ,GAAG,IAAIlhB,GAAG,CAAC+gB,uBAAuB,CAACthB,IAAI,CAAC,CAAA;IACtDyhB,QAAQ,CAACtd,QAAQ,IAAI,OAAO,CAAA;IAC5B,MAAMsd,QAAQ,CAACzhB,IAAI,CAAA;AACvB,GAAA;AACA,EAAA,IAAIqhB,eAAe,EAAE;IACjB,MAAMK,cAAc,GAAGL,eAAe,CAAC;AAAErkB,MAAAA,GAAG,EAAEqf,SAAAA;AAAU,KAAC,CAAC,CAAA;AAC1D,IAAA,KAAK,MAAMsF,YAAY,IAAID,cAAc,EAAE;MACvC,MAAMC,YAAY,CAAC3hB,IAAI,CAAA;AAC3B,KAAA;AACJ,GAAA;AACJ;;ACzCA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4hB,aAAa,SAASriB,KAAK,CAAC;AAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIpB,EAAAA,WAAWA,CAAC2e,kBAAkB,EAAE7L,OAAO,EAAE;IACrC,MAAMzR,KAAK,GAAGA,CAAC;AAAE0B,MAAAA,OAAAA;AAAS,KAAC,KAAK;AAC5B,MAAA,MAAM2gB,eAAe,GAAG/E,kBAAkB,CAAC4D,kBAAkB,EAAE,CAAA;MAC/D,KAAK,MAAMoB,WAAW,IAAIZ,qBAAqB,CAAChgB,OAAO,CAAClE,GAAG,EAAEiU,OAAO,CAAC,EAAE;AACnE,QAAA,MAAMX,QAAQ,GAAGuR,eAAe,CAACjf,GAAG,CAACkf,WAAW,CAAC,CAAA;AACjD,QAAA,IAAIxR,QAAQ,EAAE;AACV,UAAA,MAAM2O,SAAS,GAAGnC,kBAAkB,CAAC8D,uBAAuB,CAACtQ,QAAQ,CAAC,CAAA;UACtE,OAAO;YAAEA,QAAQ;AAAE2O,YAAAA,SAAAA;WAAW,CAAA;AAClC,SAAA;AACJ,OAAA;MAC2C;QACvClnB,MAAM,CAACK,KAAK,CAAC,CAAsC,oCAAA,CAAA,GAAGiI,cAAc,CAACa,OAAO,CAAClE,GAAG,CAAC,CAAC,CAAA;AACtF,OAAA;AACA,MAAA,OAAA;KACH,CAAA;AACD,IAAA,KAAK,CAACwC,KAAK,EAAEsd,kBAAkB,CAAC9L,QAAQ,CAAC,CAAA;AAC7C,GAAA;AACJ;;ACvDA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+Q,QAAQA,CAAC9Q,OAAO,EAAE;AACvB,EAAA,MAAM6L,kBAAkB,GAAGiE,6BAA6B,EAAE,CAAA;EAC1D,MAAMiB,aAAa,GAAG,IAAIJ,aAAa,CAAC9E,kBAAkB,EAAE7L,OAAO,CAAC,CAAA;EACpExN,aAAa,CAACue,aAAa,CAAC,CAAA;AAChC;;AC7BA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASxd,QAAQA,CAACuU,OAAO,EAAE;AACvB,EAAA,MAAM+D,kBAAkB,GAAGiE,6BAA6B,EAAE,CAAA;AAC1DjE,EAAAA,kBAAkB,CAACtY,QAAQ,CAACuU,OAAO,CAAC,CAAA;AACxC;;AC/BA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkJ,gBAAgBA,CAAClJ,OAAO,EAAE9H,OAAO,EAAE;EACxCzM,QAAQ,CAACuU,OAAO,CAAC,CAAA;EACjBgJ,QAAQ,CAAC9Q,OAAO,CAAC,CAAA;AACrB;;AC3BA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAMiR,iBAAiB,GAAG,YAAY,CAAA;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG,OAAOC,mBAAmB,EAAEC,eAAe,GAAGH,iBAAiB,KAAK;EAC7F,MAAMhd,UAAU,GAAG,MAAMtN,IAAI,CAACoW,MAAM,CAACzU,IAAI,EAAE,CAAA;AAC3C,EAAA,MAAM+oB,kBAAkB,GAAGpd,UAAU,CAACH,MAAM,CAAEtH,SAAS,IAAK;IACxD,OAAQA,SAAS,CAACoB,QAAQ,CAACwjB,eAAe,CAAC,IACvC5kB,SAAS,CAACoB,QAAQ,CAACjH,IAAI,CAACgN,YAAY,CAACC,KAAK,CAAC,IAC3CpH,SAAS,KAAK2kB,mBAAmB,CAAA;AACzC,GAAC,CAAC,CAAA;AACF,EAAA,MAAMzgB,OAAO,CAACC,GAAG,CAAC0gB,kBAAkB,CAACzgB,GAAG,CAAEpE,SAAS,IAAK7F,IAAI,CAACoW,MAAM,CAACjB,MAAM,CAACtP,SAAS,CAAC,CAAC,CAAC,CAAA;AACvF,EAAA,OAAO6kB,kBAAkB,CAAA;AAC7B,CAAC;;ACpCD;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,GAAG;AAC7B;AACA3qB,EAAAA,IAAI,CAACoJ,gBAAgB,CAAC,UAAU,EAAIC,KAAK,IAAK;AAC1C,IAAA,MAAMxD,SAAS,GAAGyH,UAAU,CAACI,eAAe,EAAE,CAAA;IAC9CrE,KAAK,CAACc,SAAS,CAACogB,oBAAoB,CAAC1kB,SAAS,CAAC,CAACwE,IAAI,CAAEugB,aAAa,IAAK;MACzB;AACvC,QAAA,IAAIA,aAAa,CAACnf,MAAM,GAAG,CAAC,EAAE;UAC1BtL,MAAM,CAACM,GAAG,CAAC,CAAA,oDAAA,CAAsD,GAC7D,CAAgB,cAAA,CAAA,EAAEmqB,aAAa,CAAC,CAAA;AACxC,SAAA;AACJ,OAAA;AACJ,KAAC,CAAC,CAAC,CAAA;AACP,GAAE,CAAC,CAAA;AACP;;AC9BA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASljB,KAAK,CAAC;AAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpB,WAAWA,CAACiB,OAAO,EAAE;IAAEsjB,SAAS,GAAG,CAAC,GAAG,CAAC;AAAEC,IAAAA,QAAQ,GAAG,EAAA;GAAI,GAAG,EAAE,EAAE;IACjB;AACvCtjB,MAAAA,kBAAM,CAACP,cAAc,CAAC4jB,SAAS,EAAE7iB,MAAM,EAAE;AACrC3F,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,iBAAiB;AAC5BC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,mBAAA;AACf,OAAC,CAAC,CAAA;AACF0F,MAAAA,kBAAM,CAACP,cAAc,CAAC6jB,QAAQ,EAAE9iB,MAAM,EAAE;AACpC3F,QAAAA,UAAU,EAAE,iBAAiB;AAC7BC,QAAAA,SAAS,EAAE,iBAAiB;AAC5BC,QAAAA,QAAQ,EAAE,aAAa;AACvBT,QAAAA,SAAS,EAAE,kBAAA;AACf,OAAC,CAAC,CAAA;AACN,KAAA;IACA,KAAK,CAAEsX,OAAO,IAAK,IAAI,CAAC2R,MAAM,CAAC3R,OAAO,CAAC,EAAE7R,OAAO,CAAC,CAAA;IACjD,IAAI,CAACyjB,UAAU,GAAGH,SAAS,CAAA;IAC3B,IAAI,CAACI,SAAS,GAAGH,QAAQ,CAAA;AAC7B,GAAA;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIC,EAAAA,MAAMA,CAAC;IAAE5lB,GAAG;AAAEkE,IAAAA,OAAAA;AAAQ,GAAC,EAAE;AACrB,IAAA,IAAIA,OAAO,IAAIA,OAAO,CAAC0Q,IAAI,KAAK,UAAU,EAAE;AACxC,MAAA,OAAO,KAAK,CAAA;AAChB,KAAA;IACA,MAAMmR,iBAAiB,GAAG/lB,GAAG,CAACmH,QAAQ,GAAGnH,GAAG,CAACgmB,MAAM,CAAA;AACnD,IAAA,KAAK,MAAMpjB,MAAM,IAAI,IAAI,CAACkjB,SAAS,EAAE;AACjC,MAAA,IAAIljB,MAAM,CAAC/G,IAAI,CAACkqB,iBAAiB,CAAC,EAAE;QACW;AACvChrB,UAAAA,MAAM,CAACM,GAAG,CAAC,CAAwB0qB,qBAAAA,EAAAA,iBAAiB,UAAU,GAC1D,CAAA,yDAAA,CAA2D,GAC3D,CAAA,EAAGnjB,MAAM,CAACO,QAAQ,EAAE,EAAE,CAAC,CAAA;AAC/B,SAAA;AACA,QAAA,OAAO,KAAK,CAAA;AAChB,OAAA;AACJ,KAAA;AACA,IAAA,IAAI,IAAI,CAAC0iB,UAAU,CAAC1c,IAAI,CAAEvG,MAAM,IAAKA,MAAM,CAAC/G,IAAI,CAACkqB,iBAAiB,CAAC,CAAC,EAAE;MACvB;QACvChrB,MAAM,CAACK,KAAK,CAAC,CAAA,qBAAA,EAAwB2qB,iBAAiB,CAAG,CAAA,CAAA,GAAG,gBAAgB,CAAC,CAAA;AACjF,OAAA;AACA,MAAA,OAAO,IAAI,CAAA;AACf,KAAA;IAC2C;MACvChrB,MAAM,CAACM,GAAG,CAAC,CAAwB0qB,qBAAAA,EAAAA,iBAAiB,UAAU,GAC1D,CAAA,qDAAA,CAAuD,GACvD,CAAA,oBAAA,CAAsB,CAAC,CAAA;AAC/B,KAAA;AACA,IAAA,OAAO,KAAK,CAAA;AAChB,GAAA;AACJ;;AC5GA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjC,uBAAuBA,CAAC9jB,GAAG,EAAE;AAClC,EAAA,MAAM8f,kBAAkB,GAAGiE,6BAA6B,EAAE,CAAA;AAC1D,EAAA,OAAOjE,kBAAkB,CAACgE,uBAAuB,CAAC9jB,GAAG,CAAC,CAAA;AAC1D;;ACCApF,IAAI,CAACqrB,WAAW,EAAE,CAAA;AAElBC,YAAyB,EAAE,CAAA;;AAG3B;AACA;AACA;AACA;AACA;AACAC,gBAAmC,CAAC,CAClC;AACE,EAAA,KAAK,EAAE,eAAe;AACtB,EAAA,UAAU,EAAE,kCAAA;AACd,CAAC,EACD;AACE,EAAA,KAAK,EAAE,YAAY;AACnB,EAAA,UAAU,EAAE,eAAA;AACd,CAAC,CACF,EAAE,EAAE,CAAC,CAAA;AACNC,qBAAwC,EAAE,CAAA;AAC1CC,aAA6B,CAAC,IAAIC,eAA+B,CAACC,uBAA0C,CAAC,YAAY,CAAC,EAAE;EAC1Hb,SAAS,EAAE,CAAC,MAAM,CAAA;AAEpB,CAAC,CAAC,CAAC,CAAA;AAGHW,aAA6B,CAAC,mCAAmC,EAAE,IAAIG,UAA6B,CAAC;AAAE,EAAA,WAAW,EAAC,0BAA0B;AAAEhS,EAAAA,OAAO,EAAE,CAAC,IAAIiS,gBAAmC,CAAC;AAAEpW,IAAAA,UAAU,EAAE,EAAE;AAAEC,IAAAA,aAAa,EAAE,QAAA;AAAS,GAAC,CAAC,CAAA;AAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AACzP+V,aAA6B,CAAC,gCAAgC,EAAE,IAAIG,UAA6B,CAAC;AAAE,EAAA,WAAW,EAAC,uBAAuB;AAAEhS,EAAAA,OAAO,EAAE,CAAC,IAAIiS,gBAAmC,CAAC;AAAEpW,IAAAA,UAAU,EAAE,EAAE;AAAEC,IAAAA,aAAa,EAAE,QAAA;AAAS,GAAC,CAAC,EAAE,IAAIoW,uBAAkD,CAAC;AAAE/O,IAAAA,QAAQ,EAAE,CAAE,CAAC,EAAE,GAAG,CAAA;AAAG,GAAC,CAAC,CAAA;AAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AACrU0O,aAA6B,CAAC,4BAA4B,EAAE,IAAIM,oBAAuC,CAAC;AAAE,EAAA,WAAW,EAAC,eAAe;AAAEnS,EAAAA,OAAO,EAAE,CAAC,IAAIiS,gBAAmC,CAAC;AAAEpW,IAAAA,UAAU,EAAE,EAAE;AAAEC,IAAAA,aAAa,EAAE,KAAA;AAAM,GAAC,CAAC,CAAA;AAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AAC9O+V,aAA6B,CAAC,2BAA2B,EAAE,IAAIO,YAA+B,CAAC;AAAE,EAAA,WAAW,EAAC,cAAc;AAAC,EAAA,uBAAuB,EAAC,CAAC;AAAEpS,EAAAA,OAAO,EAAE,CAAC,IAAIiS,gBAAmC,CAAC;AAAEpW,IAAAA,UAAU,EAAE,EAAE;AAAEC,IAAAA,aAAa,EAAE,IAAA;AAAK,GAAC,CAAC,CAAA;AAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AAC7P+V,aAA6B,CAAC,oCAAoC,EAAE,IAAIG,UAA6B,CAAC;AAAE,EAAA,WAAW,EAAC,eAAe;AAAEhS,EAAAA,OAAO,EAAE,CAAC,IAAIiS,gBAAmC,CAAC;AAAEpW,IAAAA,UAAU,EAAE,EAAE;AAAEC,IAAAA,aAAa,EAAE,KAAA;AAAM,GAAC,CAAC,CAAA;AAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AAC5O+V,aAA6B,CAAC,sBAAsB,EAAE,IAAIO,YAA+B,CAAC;AAAE,EAAA,WAAW,EAAC,gBAAgB;AAAC,EAAA,uBAAuB,EAAC,CAAC;AAAEpS,EAAAA,OAAO,EAAE,CAAC,IAAIiS,gBAAmC,CAAC;AAAEpW,IAAAA,UAAU,EAAE,EAAE;AAAEC,IAAAA,aAAa,EAAE,IAAA;AAAK,GAAC,CAAC,EAAE,IAAIuW,oBAA4C,CAAC,eAAe,EAAE;AAAEjK,IAAAA,gBAAgB,EAAE,IAAA;AAAK,GAAC,CAAC,CAAA;AAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AACzVyJ,aAA6B,CAAC,sBAAsB,EAAE,IAAIO,YAA+B,CAAC;AAAE,EAAA,WAAW,EAAC,gBAAgB;AAAC,EAAA,uBAAuB,EAAC,CAAC;AAAEpS,EAAAA,OAAO,EAAE,CAAC,IAAIiS,gBAAmC,CAAC;AAAEpW,IAAAA,UAAU,EAAE,EAAE;AAAEC,IAAAA,aAAa,EAAE,IAAA;AAAK,GAAC,CAAC,EAAE,IAAIuW,oBAA4C,CAAC,eAAe,EAAE;AAAEjK,IAAAA,gBAAgB,EAAE,IAAA;AAAK,GAAC,CAAC,CAAA;AAAE,CAAC,CAAC,EAAE,KAAK,CAAC"}