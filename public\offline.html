<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>快乐股市 - 离线模式</title>
  <link rel="manifest" href="/manifest.json">
  <meta name="theme-color" content="#4CAF50">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 600px;
      background-color: white;
      border-radius: 8px;
      padding: 30px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #4CAF50;
      margin-top: 0;
    }
    .icon {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
    }
    .message {
      margin: 20px 0;
      line-height: 1.6;
    }
    .button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      transition: background-color 0.3s;
      text-decoration: none;
      display: inline-block;
      margin-top: 20px;
    }
    .button:hover {
      background-color: #388E3C;
    }
    .cached-data {
      margin-top: 30px;
      text-align: left;
      border-top: 1px solid #eee;
      padding-top: 20px;
    }
    .data-item {
      margin-bottom: 10px;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 4px;
    }
    .status {
      display: flex;
      align-items: center;
      margin-top: 20px;
      font-size: 14px;
      color: #666;
    }
    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #ff5722;
      margin-right: 8px;
    }
  </style>
</head>
<body>
  <div class="container">
    <img src="/icons/icon-192x192.png" alt="快乐股市" class="icon">
    <h1>您当前处于离线模式</h1>
    
    <div class="message">
      <p>无法连接到服务器。请检查您的网络连接并重试。</p>
      <p>您仍然可以访问之前缓存的数据。</p>
    </div>
    
    <button class="button" onclick="tryReconnect()">重试连接</button>
    
    <div class="status">
      <div class="status-dot"></div>
      <span>离线</span>
    </div>
    
    <div class="cached-data">
      <h3>可用的离线数据</h3>
      <div id="cached-content">
        <p>加载缓存数据中...</p>
      </div>
    </div>
  </div>

  <script>
    // 检查网络状态
    function updateNetworkStatus() {
      const statusDot = document.querySelector('.status-dot');
      const statusText = document.querySelector('.status span');
      
      if (navigator.onLine) {
        statusDot.style.backgroundColor = '#4CAF50';
        statusText.textContent = '网络已恢复';
        
        // 显示重新加载按钮
        document.querySelector('.button').textContent = '重新加载应用';
      } else {
        statusDot.style.backgroundColor = '#ff5722';
        statusText.textContent = '离线';
      }
    }
    
    // 尝试重新连接
    function tryReconnect() {
      if (navigator.onLine) {
        // 如果网络已恢复，重新加载页面
        window.location.href = '/';
      } else {
        alert('您仍处于离线状态。请检查网络连接后重试。');
      }
    }
    
    // 加载缓存的数据
    async function loadCachedData() {
      const cachedContentElement = document.getElementById('cached-content');
      
      try {
        // 尝试打开IndexedDB
        const request = indexedDB.open('happyStockMarketDB', 1);
        
        request.onerror = () => {
          cachedContentElement.innerHTML = '<p>无法访问缓存数据</p>';
        };
        
        request.onsuccess = (event) => {
          const db = event.target.result;
          
          // 检查是否有offlineData存储
          if (!db.objectStoreNames.contains('offlineData')) {
            cachedContentElement.innerHTML = '<p>没有可用的缓存数据</p>';
            return;
          }
          
          // 获取所有缓存的数据
          const transaction = db.transaction(['offlineData'], 'readonly');
          const store = transaction.objectStore('offlineData');
          const request = store.getAll();
          
          request.onsuccess = () => {
            const data = request.result;
            
            if (data.length === 0) {
              cachedContentElement.innerHTML = '<p>没有可用的缓存数据</p>';
              return;
            }
            
            // 显示缓存的数据
            let html = '';
            data.forEach(item => {
              html += `
                <div class="data-item">
                  <strong>${item.key}</strong>
                  <p>上次更新: ${new Date(item.timestamp).toLocaleString()}</p>
                </div>
              `;
            });
            
            cachedContentElement.innerHTML = html;
          };
          
          request.onerror = () => {
            cachedContentElement.innerHTML = '<p>读取缓存数据时出错</p>';
          };
        };
      } catch (error) {
        cachedContentElement.innerHTML = `<p>访问缓存数据时出错: ${error.message}</p>`;
      }
    }
    
    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
      updateNetworkStatus();
      loadCachedData();
      
      // 监听网络状态变化
      window.addEventListener('online', updateNetworkStatus);
      window.addEventListener('offline', updateNetworkStatus);
    });
  </script>
</body>
</html>