[{"name": "Process Start", "start": 1753510395557, "end": 1753510399679, "duration": 4122, "pid": 19348, "index": 0}, {"name": "Application Start", "start": 1753510399683, "end": 1753510401489, "duration": 1806, "pid": 19348, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1753510399703, "end": 1753510399740, "duration": 37, "pid": 19348, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1753510399740, "end": 1753510399798, "duration": 58, "pid": 19348, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1753510399741, "end": 1753510399742, "duration": 1, "pid": 19348, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1753510399747, "end": 1753510399748, "duration": 1, "pid": 19348, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1753510399749, "end": 1753510399750, "duration": 1, "pid": 19348, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1753510399751, "end": 1753510399752, "duration": 1, "pid": 19348, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1753510399753, "end": 1753510399754, "duration": 1, "pid": 19348, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1753510399755, "end": 1753510399756, "duration": 1, "pid": 19348, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1753510399757, "end": 1753510399757, "duration": 0, "pid": 19348, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1753510399758, "end": 1753510399759, "duration": 1, "pid": 19348, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1753510399760, "end": 1753510399761, "duration": 1, "pid": 19348, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1753510399762, "end": 1753510399762, "duration": 0, "pid": 19348, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1753510399764, "end": 1753510399765, "duration": 1, "pid": 19348, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1753510399766, "end": 1753510399767, "duration": 1, "pid": 19348, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1753510399768, "end": 1753510399768, "duration": 0, "pid": 19348, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1753510399770, "end": 1753510399771, "duration": 1, "pid": 19348, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1753510399772, "end": 1753510399772, "duration": 0, "pid": 19348, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1753510399773, "end": 1753510399774, "duration": 1, "pid": 19348, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1753510399775, "end": 1753510399775, "duration": 0, "pid": 19348, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1753510399776, "end": 1753510399777, "duration": 1, "pid": 19348, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1753510399778, "end": 1753510399778, "duration": 0, "pid": 19348, "index": 22}, {"name": "Require(19) node_modules/egg/config/config.default.js", "start": 1753510399779, "end": 1753510399780, "duration": 1, "pid": 19348, "index": 23}, {"name": "Require(20) config/config.default.js", "start": 1753510399782, "end": 1753510399782, "duration": 0, "pid": 19348, "index": 24}, {"name": "Require(21) node_modules/egg-security/config/config.local.js", "start": 1753510399784, "end": 1753510399784, "duration": 0, "pid": 19348, "index": 25}, {"name": "Require(22) node_modules/egg-watcher/config/config.local.js", "start": 1753510399787, "end": 1753510399788, "duration": 1, "pid": 19348, "index": 26}, {"name": "Require(23) node_modules/egg-view/config/config.local.js", "start": 1753510399792, "end": 1753510399792, "duration": 0, "pid": 19348, "index": 27}, {"name": "Require(24) node_modules/egg/config/config.local.js", "start": 1753510399797, "end": 1753510399798, "duration": 1, "pid": 19348, "index": 28}, {"name": "Require(25) config/config.local.js", "start": 1753510399798, "end": 1753510399798, "duration": 0, "pid": 19348, "index": 29}, {"name": "Load extend/agent.js", "start": 1753510399799, "end": 1753510399898, "duration": 99, "pid": 19348, "index": 30}, {"name": "Require(26) node_modules/egg-security/app/extend/agent.js", "start": 1753510399800, "end": 1753510399802, "duration": 2, "pid": 19348, "index": 31}, {"name": "Require(27) node_modules/egg-schedule/app/extend/agent.js", "start": 1753510399806, "end": 1753510399880, "duration": 74, "pid": 19348, "index": 32}, {"name": "Require(28) node_modules/egg-logrotator/app/extend/agent.js", "start": 1753510399882, "end": 1753510399884, "duration": 2, "pid": 19348, "index": 33}, {"name": "Load extend/context.js", "start": 1753510399898, "end": 1753510399980, "duration": 82, "pid": 19348, "index": 34}, {"name": "Require(29) node_modules/egg-security/app/extend/context.js", "start": 1753510399899, "end": 1753510399916, "duration": 17, "pid": 19348, "index": 35}, {"name": "Require(30) node_modules/egg-jsonp/app/extend/context.js", "start": 1753510399917, "end": 1753510399922, "duration": 5, "pid": 19348, "index": 36}, {"name": "Require(31) node_modules/egg-i18n/app/extend/context.js", "start": 1753510399923, "end": 1753510399924, "duration": 1, "pid": 19348, "index": 37}, {"name": "Require(32) node_modules/egg-multipart/app/extend/context.js", "start": 1753510399925, "end": 1753510399957, "duration": 32, "pid": 19348, "index": 38}, {"name": "Require(33) node_modules/egg-view/app/extend/context.js", "start": 1753510399960, "end": 1753510399962, "duration": 2, "pid": 19348, "index": 39}, {"name": "Require(34) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1753510399964, "end": 1753510399964, "duration": 0, "pid": 19348, "index": 40}, {"name": "Require(35) node_modules/egg/app/extend/context.js", "start": 1753510399965, "end": 1753510399969, "duration": 4, "pid": 19348, "index": 41}, {"name": "Require(36) app/extend/context.js", "start": 1753510399970, "end": 1753510399971, "duration": 1, "pid": 19348, "index": 42}, {"name": "Load agent.js", "start": 1753510399980, "end": 1753510400056, "duration": 76, "pid": 19348, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1753510399981, "end": 1753510399982, "duration": 1, "pid": 19348, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1753510399983, "end": 1753510399984, "duration": 1, "pid": 19348, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1753510399985, "end": 1753510400002, "duration": 17, "pid": 19348, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1753510400003, "end": 1753510400007, "duration": 4, "pid": 19348, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1753510400009, "end": 1753510400024, "duration": 15, "pid": 19348, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1753510400025, "end": 1753510400026, "duration": 1, "pid": 19348, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1753510400028, "end": 1753510400029, "duration": 1, "pid": 19348, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1753510400032, "end": 1753510400052, "duration": 20, "pid": 19348, "index": 51}, {"name": "Require(45) node_modules/egg/agent.js", "start": 1753510400053, "end": 1753510400054, "duration": 1, "pid": 19348, "index": 52}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1753510400064, "end": 1753510401136, "duration": 1072, "pid": 19348, "index": 53}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1753510400065, "end": 1753510401102, "duration": 1037, "pid": 19348, "index": 54}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1753510400065, "end": 1753510401488, "duration": 1423, "pid": 19348, "index": 55}, {"name": "Load \"Symbol(model)\" to Application", "start": 1753510400840, "end": 1753510400985, "duration": 145, "pid": 19348, "index": 56}, {"name": "Before Start in app/model/index.js:8:7", "start": 1753510400880, "end": 1753510401131, "duration": 251, "pid": 19348, "index": 57}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1753510401042, "end": 1753510401469, "duration": 427, "pid": 19348, "index": 58}]